{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 广告弹窗 -->\n\t\t<view v-if=\"showAdPopup\" class=\"ad-popup-overlay\" @click=\"closeAdPopup\">\n\t\t\t<view class=\"ad-popup-content\" @click.stop>\n\t\t\t\t<view class=\"ad-popup-close\" @click=\"closeAdPopup\">✕</view>\n\t\t\t\t<view class=\"ad-popup-title\" v-if=\"adData.title\">{{ adData.title }}</view>\n\t\t\t\t<image v-if=\"adData.imageUrl\" :src=\"adData.imageUrl\" class=\"ad-popup-image\" @click=\"handleAdClick\" mode=\"aspectFit\"></image>\n\t\t\t\t<view class=\"ad-popup-button\" @click=\"handleAdClick\">\n\t\t\t\t\t{{ adData.buttonText || '立即查看' }}\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view class=\"search-box\">\n\t\t\t<input v-model=\"searchKeyword\" placeholder=\"请输入您感兴趣的服务\" class=\"search-input\" @confirm=\"handleSearch\" />\n\t\t\t<button class=\"search-btn\" @click=\"handleSearch\">搜索</button>\n\t\t</view>\n\t\t<view class=\"category-list\">\n\t\t\t<view v-for=\"(item, index) in categories\" :key=\"index\" class=\"category-item\"\n\t\t\t\t@click=\"handleCategoryClick(item)\">\n\t\t\t\t<view class=\"img\">\n\t\t\t\t\t<image class=\"icon\" :src=\"item.imageUrl || '/static/熙迈LOGO.png'\" mode=\"heightFix\"></image>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"banner\">\n\t\t\t\t\t<view class=\"banner-left\">\n\t\t\t\t\t\t<view class=\"top-blue\">\n\t\t\t\t\t\t\t<text class=\"english-name\">\n\t\t\t\t\t\t\t\t{{ item.englishName }}\n\t\t\t\t\t\t\t\t<span style=\"display:inline-block;width:16rpx;\"></span>\n\t\t\t\t\t\t\t\t<text class=\"more-text\" style=\"margin-left: 12rpx;\">更多&gt;&gt;</text>\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"bottom-white\">\n\t\t\t\t\t\t\t<view class=\"logo\">\n\t\t\t\t\t\t\t\t<image v-if=\"item.YWLX === '代理'\" class=\"icon\"\n\t\t\t\t\t\t\t\t\t:src=\"'/static/代理图标.png'\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t<image v-else class=\"icon\"\n\t\t\t\t\t\t\t\t\t:src=\"'/static/熙迈LOGO.png'\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"bottom-white-text\">{{item.productName}}</view>\n\t\t\t\t\t\t\t<view class=\"like-section\" @click.stop=\"handleLike(item)\">\n\t\t\t\t\t\t\t\t<image class=\"like-icon\" :src=\"item.isLiked ? '/static/红色小红心.svg' : '/static/灰色小红心.svg'\"></image>\n\t\t\t\t\t\t\t\t<view v-if=\"item.showHeart\" class=\"pop-animation\">\n\t\t\t\t\t\t\t\t\t<text class=\"pop-heart\">❤️</text>\n\t\t\t\t\t\t\t\t\t<text class=\"pop-count\">{{ item.likeCount }}</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"fixed-customer-service\">\n\t\t\t<button class=\"service-btn\"  @click=\"handleContact\" >\n\t\t\t\t<view class=\"text-container\">\n\t\t\t\t\t<text class=\"btn-text\">微信客服</text>\n\t\t\t\t\t<text class=\"btn-subtext\">如有需求，请点我联系</text>\n\t\t\t\t</view>\n\t\t\t</button>\n\t\t</view>\n\n\n\n\t\t<view v-if=\"categories.length === 0\" class=\"no-result\">\n\t\t\t<text class=\"no-result-icon\">&#9785;</text>\n\t\t\t<text class=\"no-result-text\">没有找到相关产品</text>\n\t\t\t<text class=\"no-result-subtext\">尝试更改搜索关键词或稍后再试</text>\n\t\t</view>\n\t</view>\n\n\n</template>\n\n<script>\n\timport {\n\t\tFIELD_MAPPING,\n\t\tPRODUCT_MANAGER_SERVICE_LINKS\n\t} from '@/config/fields.js';\n\timport {\n\t\tobjectToParams\n\t} from '@/utils/url.js';\n\timport ShareUtils from '@/utils/share.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tsearchKeyword: '',\n\t\t\t\tcategories: [],\n\t\t\t\tlikesLog: {},\n\t\t\t\t// 广告弹窗相关\n\t\t\t\tshowAdPopup: false,\n\t\t\t\tadData: {}\n\t\t\t}\n\t\t},\n\t\tonLoad() {\n\t\t\tthis.getCategories();\n\t\t\tthis.getAdPopupData();\n\t\t},\n\t\tonShow() {\n\t\t\tthis.updateLikesStatus();\n\t\t},\n\n\t\t// 分享功能\n\t\tonShareAppMessage(res) {\n\t\t\tconsole.log('分享给好友', res);\n\t\t\treturn ShareUtils.getDefaultShareConfig({\n\t\t\t\ttitle: '熙迈科技工业服务 - 专业产品展示',\n\t\t\t\tpath: 'pages/index/index',\n\t\t\t\timageUrl: '/static/熙迈LOGO.png'\n\t\t\t});\n\t\t},\n\n\t\tonShareTimeline(res) {\n\t\t\tconsole.log('分享到朋友圈', res);\n\t\t\treturn ShareUtils.getDefaultShareConfig({\n\t\t\t\ttitle: '熙迈科技工业服务 - 专业产品展示',\n\t\t\t\tpath: 'pages/index/index',\n\t\t\t\timageUrl: '/static/熙迈LOGO.png'\n\t\t\t});\n\t\t},\n\n\t\tmethods: {\n\t\t\t// 获取广告弹窗数据\n\t\t\tasync getAdPopupData() {\n\t\t\t\ttry {\n\t\t\t\t\tconst requestData = {\n\t\t\t\t\t\tappKey: \"984e1ff028f80150\",\n\t\t\t\t\t\tsign: \"NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==\",\n\t\t\t\t\t\tworksheetId: \"xcxdcwh\",\n\t\t\t\t\t\tviewId: \"\",\n\t\t\t\t\t\tpageSize: 1,\n\t\t\t\t\t\tpageIndex: 1,\n\t\t\t\t\t\tlistType: 0,\n\t\t\t\t\t\tcontrols: [],\n\t\t\t\t\t\tfilters: [\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcontrolId: \"6870869ba849420e13f654c3\", // isActive字段\n\t\t\t\t\t\t\t\tdataType: 36,\n\t\t\t\t\t\t\t\tspliceType: 1,\n\t\t\t\t\t\t\t\tfilterType: 2,\n\t\t\t\t\t\t\t\tvalue: \"1\" // 只获取启用的弹窗\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t};\n\n\t\t\t\t\tconst response = await uni.request({\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\tdata: requestData,\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (response.data && response.data.data && response.data.data.rows && response.data.data.rows.length > 0) {\n\t\t\t\t\t\tconst adRow = response.data.data.rows[0];\n\t\t\t\t\t\tthis.adData = {\n\t\t\t\t\t\t\tid: adRow['6870869ba849420e13f654bf'], // id\n\t\t\t\t\t\t\ttitle: adRow['687085d1a849420e13f654b5'], // title\n\t\t\t\t\t\t\tjumpUrl: adRow['6870869ba849420e13f654c0'], // jumpUrl\n\t\t\t\t\t\t\timageUrl: this.getImageUrl(adRow['687085d1a849420e13f654b7']), // imageUrl\n\t\t\t\t\t\t\tstartDate: adRow['6870869ba849420e13f654c1'], // startDate\n\t\t\t\t\t\t\tendDate: adRow['6870869ba849420e13f654c2'], // endDate\n\t\t\t\t\t\t\tisActive: adRow['6870869ba849420e13f654c3'] // isActive\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\t// 检查是否在显示时间范围内\n\t\t\t\t\t\tif (this.shouldShowPopup()) {\n\t\t\t\t\t\t\tthis.showAdPopup = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取广告弹窗数据失败:', error);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 处理图片URL（附件类型字段）\n\t\t\tgetImageUrl(attachmentData) {\n\t\t\t\tif (!attachmentData) return '';\n\t\t\t\ttry {\n\t\t\t\t\tconst parsed = typeof attachmentData === 'string' ? JSON.parse(attachmentData) : attachmentData;\n\t\t\t\t\tif (parsed && parsed.length > 0 && parsed[0].previewUrl) {\n\t\t\t\t\t\treturn parsed[0].previewUrl;\n\t\t\t\t\t}\n\t\t\t\t} catch (e) {\n\t\t\t\t\tconsole.error('解析图片数据失败:', e);\n\t\t\t\t}\n\t\t\t\treturn '';\n\t\t\t},\n\n\t\t\t// 判断是否应该显示弹窗\n\t\t\tshouldShowPopup() {\n\t\t\t\tif (!this.adData.isActive) return false;\n\n\t\t\t\tconst today = new Date();\n\t\t\t\tconst todayStr = today.toISOString().split('T')[0];\n\n\t\t\t\t// 检查日期范围\n\t\t\t\tif (this.adData.startDate && todayStr < this.adData.startDate) return false;\n\t\t\t\tif (this.adData.endDate && todayStr > this.adData.endDate) return false;\n\n\t\t\t\t// 检查今日是否已显示过\n\t\t\t\tconst lastShown = uni.getStorageSync('adPopupLastShown');\n\t\t\t\tif (lastShown === todayStr) return false;\n\n\t\t\t\treturn true;\n\t\t\t},\n\n\t\t\t// 关闭广告弹窗\n\t\t\tcloseAdPopup() {\n\t\t\t\tthis.showAdPopup = false;\n\t\t\t\t// 记录今日已显示\n\t\t\t\tconst today = new Date().toISOString().split('T')[0];\n\t\t\t\tuni.setStorageSync('adPopupLastShown', today);\n\t\t\t},\n\n\t\t\t// 处理广告点击\n\t\t\thandleAdClick() {\n\t\t\t\tif (this.adData.jumpUrl) {\n\t\t\t\t\t// 记录今日已显示\n\t\t\t\t\tconst today = new Date().toISOString().split('T')[0];\n\t\t\t\t\tuni.setStorageSync('adPopupLastShown', today);\n\n\t\t\t\t\t// 关闭弹窗\n\t\t\t\t\tthis.showAdPopup = false;\n\n\t\t\t\t\t// 跳转页面\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: this.adData.jumpUrl,\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('页面跳转失败:', err);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '页面跳转失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tupdateLikesStatus() {\n\t\t\t\tconst likesLog = uni.getStorageSync('likes_log') || {};\n\t\t\t\tthis.likesLog = likesLog;\n\t\t\t\tif (this.categories.length > 0) {\n\t\t\t\t\tthis.categories.forEach(item => {\n\t\t\t\t\t\tif (likesLog[item.rowId]) {\n\t\t\t\t\t\t\tthis.$set(item, 'isLiked', true);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.$set(item, 'isLiked', false);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tif (item.children && item.children.length > 0) {\n\t\t\t\t\t\t\titem.children.forEach(child => {\n\t\t\t\t\t\t\t\tif (likesLog[child.rowId]) {\n\t\t\t\t\t\t\t\t\tthis.$set(child, 'isLiked', true);\n\t\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t\tthis.$set(child, 'isLiked', false);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\thandleLike(item) {\n\t\t\t\t// 1. Toggle the UI state immediately (Optimistic Update)\n\t\t\t\titem.isLiked = !item.isLiked;\n\t\t\t\t\n\t\t\t\tlet webhookAction = '';\n\t\t\t\n\t\t\t\tif (item.isLiked) {\n\t\t\t\t\t// --- UI & Local State Update for LIKE ---\n\t\t\t\t\titem.likeCount++;\n\t\t\t\t\twebhookAction = 'increment';\n\t\t\t\t\tthis.$set(this.likesLog, item.rowId, true);\n\n\t\t\t\t\t// Trigger animation\n\t\t\t\t\tthis.$set(item, 'showHeart', true);\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.$set(item, 'showHeart', false);\n\t\t\t\t\t}, 600);\n\t\t\t\t} else {\n\t\t\t\t\t// --- UI & Local State Update for UNLIKE ---\n\t\t\t\t\titem.likeCount--;\n\t\t\t\t\twebhookAction = 'decrement';\n\t\t\t\t\tthis.$delete(this.likesLog, item.rowId);\n\t\t\t\t}\n\t\t\t\n\t\t\t\t// Update local storage for persistence across sessions\n\t\t\t\tuni.setStorageSync('likes_log', this.likesLog);\n\t\t\t\n\t\t\t\t// 2. Send the corresponding command to the webhook\n\t\t\t\tuni.request({\n\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/workflow/hooks/Njg2Y2ZhNDk3MDMwNzAyNDViNDMxOTcx',\n\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\theader: {\n\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t},\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: webhookAction,\n\t\t\t\t\t\tid: item.rowId\n\t\t\t\t\t},\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log(`Webhook '${webhookAction}' for rowid ${item.rowId} sent successfully. Response:`, res.data);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error(`Webhook '${webhookAction}' for rowid ${item.rowId} failed:`, err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\tformatAPIData(row) {\n\t\t\t\tconst formattedItem = {\n\t\t\t\t\trowId: row.rowid || '',\n\t\t\t\t\tchildren: [],\n\t\t\t\t\tlikeCount: parseInt(row['DZS']) || 0,\n\t\t\t\t\tisLiked: false,\n\t\t\t\t\tshowHeart: false\n\t\t\t\t};\n\t\t\t\tObject.keys(FIELD_MAPPING).forEach(key => {\n\t\t\t\t\tconst apiFieldId = FIELD_MAPPING[key];\n\t\t\t\t\tformattedItem[key] = row[apiFieldId] || '';\n\t\t\t\t});\n\n\t\t\t\t// Ensure caseStudiesCount is parsed as a number\n\t\t\t\tformattedItem.caseStudiesCount = parseInt(row[FIELD_MAPPING.caseStudiesCount]) || 0;\n\n\t\t\t\t// 添加图片URL处理\n\t\t\t\tif (formattedItem.imageUrl || row.imageUrl) {\n\t\t\t\t\tformattedItem.imageUrl = this.getImageUrl(formattedItem.imageUrl || row.imageUrl);\n\t\t\t\t}\n\n\t\t\t\t// 打印解析后的数据\n\t\t\t\t// console.log('解析后的数据:', formattedItem);\n\n\t\t\t\treturn formattedItem;\n\t\t\t},\n\t\t\tasync handleSearch() {\n\t\t\t\tif (!this.searchKeyword.trim()) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请输入搜索关键词',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tconst requestData = {\n\t\t\t\t\t\tappKey: '984e1ff028f80150',\n\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\n\t\t\t\t\t\tworksheetId: 'fenlei',\n\t\t\t\t\t\tviewId: '',\n\t\t\t\t\t\tpageSize: 50,\n\t\t\t\t\t\tpageIndex: 1,\n\t\t\t\t\t\tlistType: 0,\n\t\t\t\t\t\tcontrols: [],\n\t\t\t\t\t\tfilters: [{\n\t\t\t\t\t\t\t\tcontrolId: '67b2da03ef727a4cd047da1b',\n\t\t\t\t\t\t\t\tdataType: 2,\n\t\t\t\t\t\t\t\tspliceType: 2,\n\t\t\t\t\t\t\t\tfilterType: 1,\n\t\t\t\t\t\t\t\tvalue: this.searchKeyword\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tcontrolId: '67b2dd25ef727a4cd047da2a',\n\t\t\t\t\t\t\t\tdataType: 2,\n\t\t\t\t\t\t\t\tspliceType: 2,\n\t\t\t\t\t\t\t\tfilterType: 1,\n\t\t\t\t\t\t\t\tvalue: this.searchKeyword\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t]\n\t\t\t\t\t};\n\n\t\t\t\t\tconst response = await uni.request({\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\tdata: requestData,\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (response.statusCode !== 200) {\n\t\t\t\t\t\tconsole.error('请求失败:', response);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (response.data && response.data.data) {\n\t\t\t\t\t\tif (response.data.data.rows && response.data.data.rows.length > 0) {\n\t\t\t\t\t\t\tconst formattedData = response.data.data.rows.map(row => this.formatAPIData(row));\n\t\t\t\t\t\t\tconsole.log('Formatted Data:', formattedData);\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: `/pages/category/category?title=搜索结果&children=${encodeURIComponent(JSON.stringify(formattedData))}`\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else if (response.data.data.total === 0 || response.data.error_code === 1) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '没有找到相关产品',\n\t\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('接口返回数据格式异常:', response.data);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '数据格式异常',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取分类失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取分类失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync getCategories() {\n\t\t\t\ttry {\n\t\t\t\t\tconst response = await uni.request({\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\n\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\n\t\t\t\t\t\t\tworksheetId: 'fenlei',\n\t\t\t\t\t\t\tviewId: '',\n\t\t\t\t\t\t\tpageSize: 50,\n\t\t\t\t\t\t\tpageIndex: 1,\n\t\t\t\t\t\t\tlistType: 0,\n\t\t\t\t\t\t\tcontrols: [],\n\t\t\t\t\t\t\tfilters: [{\n\t\t\t\t\t\t\t\tcontrolId: '67b2dd3aef727a4cd047da36',\n\t\t\t\t\t\t\t\tdataType: 29,\n\t\t\t\t\t\t\t\tspliceType: 1,\n\t\t\t\t\t\t\t\tfilterType: 7\n\t\t\t\t\t\t\t}],\n\t\t\t\t\t\t\tsortId: '67b2dd25ef727a4cd047da2a'\n\t\t\t\t\t\t},\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tconsole.log('API Response:', response);\n\n\t\t\t\t\tif (response.statusCode !== 200) {\n\t\t\t\t\t\tconsole.error('请求失败:', response);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '请求失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (response.data && response.data.data && response.data.data.rows) {\n\t\t\t\t\t\tconsole.log('Raw Data:', response.data.data.rows);\n\t\t\t\t\t\tthis.categories = response.data.data.rows.map(row => this.formatAPIData(row));\n\t\t\t\t\t\t// Sort by like count descending on initial load\n\t\t\t\t\t\tthis.categories.sort((a, b) => b.likeCount - a.likeCount);\n\t\t\t\t\t\t// console.log('Formatted Categories:', this.categories);\n\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('接口返回数据格式异常:', response.data);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '数据格式异常',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取分类失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取分类失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tgetImageUrl(imageData) {\n\t\t\t\ttry {\n\t\t\t\t\t// 支持直接传入数组或JSON字符串\n\t\t\t\t\tconst parsedData = Array.isArray(imageData) ? imageData : JSON.parse(imageData);\n\n\t\t\t\t\tif (!Array.isArray(parsedData)) {\n\t\t\t\t\t\tconsole.warn(\"图片数据格式错误\");\n\t\t\t\t\t\treturn '/static/熙迈LOGO.png'; // 返回默认图\n\t\t\t\t\t}\n\n\t\t\t\t\t// 查找第一个有效URL（优先fileUrl，其次thumbnail_full_path）\n\t\t\t\t\tconst targetItem = parsedData.find(item =>\n\t\t\t\t\t\titem.fileUrl?.startsWith('http') ||\n\t\t\t\t\t\titem.thumbnail_full_path?.startsWith('http')\n\t\t\t\t\t);\n\n\t\t\t\t\t// 返回优先级：fileUrl > thumbnail_full_path > 默认图\n\t\t\t\t\treturn targetItem?.fileUrl ||\n\t\t\t\t\t\ttargetItem?.thumbnail_full_path ||\n\t\t\t\t\t\t'/static/熙迈LOGO.png';\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"解析失败:\", error.message);\n\t\t\t\t\treturn '/static/熙迈LOGO.png'; // 始终返回字符串\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync handleCategoryClick(item) {\n\t\t\t\tlet recommendations = [];\n\t\t\t\tlet parentMaterialsWithDownloadUrl = [];\n\n\t\t\t\ttry {\n\t\t\t\t\t// 检查是否有相关资料需要获取完整信息\n\t\t\t\t\tconst hasIntroductionMaterials = item.introductionMaterials && item.introductionMaterials.length > 0;\n\n\t\t\t\t\t// 如果有相关资料，先调用 getFilterRows 获取包含 DownloadUrl 的完整信息\n\t\t\t\t\tif (hasIntroductionMaterials) {\n\t\t\t\t\t\tconsole.log('检测到相关资料，获取完整附件信息...');\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\tconst materialResponse = await uni.request({\n\t\t\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',\n\t\t\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\n\t\t\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\n\t\t\t\t\t\t\t\t\tworksheetId: 'fenlei',\n\t\t\t\t\t\t\t\t\tpageSize: 1,\n\t\t\t\t\t\t\t\t\tpageIndex: 1,\n\t\t\t\t\t\t\t\t\tlistType: 0,\n\t\t\t\t\t\t\t\t\tcontrols: [],\n\t\t\t\t\t\t\t\t\tfilters: [{\n\t\t\t\t\t\t\t\t\t\tcontrolId: 'rowid',\n\t\t\t\t\t\t\t\t\t\tdataType: 2,\n\t\t\t\t\t\t\t\t\t\tspliceType: 1,\n\t\t\t\t\t\t\t\t\t\tfilterType: 2,\n\t\t\t\t\t\t\t\t\t\tvalue: item.rowId\n\t\t\t\t\t\t\t\t\t}]\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\n\t\t\t\t\t\t\tif (materialResponse.data && materialResponse.data.data && materialResponse.data.data.rows && materialResponse.data.data.rows.length > 0) {\n\t\t\t\t\t\t\t\tconst productData = materialResponse.data.data.rows[0];\n\t\t\t\t\t\t\t\tif (productData.DLPP_JSZL) {\n\t\t\t\t\t\t\t\t\tconst materialsData = JSON.parse(productData.DLPP_JSZL);\n\t\t\t\t\t\t\t\t\tconsole.log('原始附件数据:', materialsData);\n\t\t\t\t\t\t\t\t\tparentMaterialsWithDownloadUrl = materialsData.map(material => {\n\t\t\t\t\t\t\t\t\t\tconsole.log('处理单个附件:', material);\n\t\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\t\t// 使用正确的字段名\n\t\t\t\t\t\t\t\t\t\t\tname: material.original_file_name || '未知文件',\n\t\t\t\t\t\t\t\t\t\t\turl: material.DownloadUrl || material.original_file_full_path || '',\n\t\t\t\t\t\t\t\t\t\t\tsize: material.file_size || 0,\n\t\t\t\t\t\t\t\t\t\t\t// 保留完整的字段信息（与 form 页面一致）\n\t\t\t\t\t\t\t\t\t\t\tDownloadUrl: material.DownloadUrl || '',\n\t\t\t\t\t\t\t\t\t\t\toriginal_file_full_path: material.original_file_full_path || '',\n\t\t\t\t\t\t\t\t\t\t\toriginal_file_name: material.original_file_name || '未知文件',\n\t\t\t\t\t\t\t\t\t\t\tfile_size: material.file_size || 0\n\t\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tconsole.log('映射后的附件信息:', parentMaterialsWithDownloadUrl);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error('获取完整附件信息失败:', error);\n\t\t\t\t\t\t\t// 如果获取失败，使用原有的资料信息\n\t\t\t\t\t\t\tparentMaterialsWithDownloadUrl = item.introductionMaterials || [];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tconst subCategoryResponse = await uni.request({\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\n\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\n\t\t\t\t\t\t\tworksheetId: 'fenlei',\n\t\t\t\t\t\t\tpageSize: 50,\n\t\t\t\t\t\t\tpageIndex: 1,\n\t\t\t\t\t\t\trowId: item.rowId,\n\t\t\t\t\t\t\tcontrolId: '67b2dd3aef727a4cd047da37',\n\t\t\t\t\t\t\tgetSystemControl: false\n\t\t\t\t\t\t},\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tconst recommendResponse = await uni.request({\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\n\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\n\t\t\t\t\t\t\tworksheetId: 'fenlei',\n\t\t\t\t\t\t\tpageSize: 50,\n\t\t\t\t\t\t\tpageIndex: 1,\n\t\t\t\t\t\t\trowId: item.rowId,\n\t\t\t\t\t\t\tcontrolId: 'GLCP',\n\t\t\t\t\t\t\tgetSystemControl: false\n\t\t\t\t\t\t},\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (subCategoryResponse.statusCode !== 200) {\n\t\t\t\t\t\tconsole.error('请求子分类失败:', response);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '请求子分类失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\n\t\t\t\t\tif (!subCategoryResponse.data || !subCategoryResponse.data.data) {\n\t\t\t\t\t\tconsole.error('接口返回数据格式异常:', subCategoryResponse.data);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '数据格式异常',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (!subCategoryResponse.data.data.rows || subCategoryResponse.data.data.rows.length === 0) {\n\t\t\t\t\t\trecommendations = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));\n\n\t\t\t\t\t\tlet caseStudies = [];\n\t\t\t\t\t\tif (item.caseStudiesCount > 0) {\n\t\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\t\tconst caseStudiesResponse = await uni.request({\n\t\t\t\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',\n\t\t\t\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\n\t\t\t\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\n\t\t\t\t\t\t\t\t\t\tworksheetId: 'fenlei',\n\t\t\t\t\t\t\t\t\t\tpageSize: 50,\n\t\t\t\t\t\t\t\t\t\tpageIndex: 1,\n\t\t\t\t\t\t\t\t\t\trowId: item.rowId,\n\t\t\t\t\t\t\t\t\t\tcontrolId: 'ALQK',\n\t\t\t\t\t\t\t\t\t\tgetSystemControl: false\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\tif (caseStudiesResponse.data && caseStudiesResponse.data.data && caseStudiesResponse.data.data.rows) {\n\t\t\t\t\t\t\t\t\tcaseStudies = caseStudiesResponse.data.data.rows.map(row => ({\n\t\t\t\t\t\t\t\t\t\tclientName: row[FIELD_MAPPING.caseClientName] || '',\n\t\t\t\t\t\t\t\t\t\tdetails: row[FIELD_MAPPING.caseDetails] || ''\n\t\t\t\t\t\t\t\t\t}));\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\t\t\tconsole.error('获取案例情况失败:', e);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 正常页面跳转：传递基本数据，但不包含推荐产品（避免URL过长）\n\t\t\t\t\t\tconst formUrl = `/pages/form/form?${objectToParams(item)}&caseStudies=${encodeURIComponent(JSON.stringify(caseStudies))}`;\n\n\t\t\t\t\t\tconsole.log('=== Index页面跳转到Form页面 ===');\n\t\t\t\t\t\tconsole.log('完整的item对象:', item);\n\t\t\t\t\t\tconsole.log('跳转的产品信息:', {\n\t\t\t\t\t\t\tproductName: item.productName,\n\t\t\t\t\t\t\tproductCode: item.productCode,\n\t\t\t\t\t\t\tserviceDescription: item.serviceDescription,\n\t\t\t\t\t\t\tproductManager: item.productManager,\n\t\t\t\t\t\t\tcaseStudiesCount: caseStudies.length\n\t\t\t\t\t\t});\n\t\t\t\t\t\tconsole.log('构建的URL:', formUrl);\n\t\t\t\t\t\tconsole.log('跳转URL长度:', formUrl.length);\n\n\t\t\t\t\t\t// 检查URL长度，如果仍然过长则使用简化模式\n\t\t\t\t\t\tif (formUrl.length > 1500) { // 提高阈值，减少使用简化模式的情况\n\t\t\t\t\t\t\tconsole.log('⚠️ URL过长（' + formUrl.length + '字符），使用简化模式');\n\t\t\t\t\t\t\tconst simpleUrl = item.productCode ?\n\t\t\t\t\t\t\t\t`/pages/form/form?productCode=${encodeURIComponent(item.productCode)}&shareMode=1` :\n\t\t\t\t\t\t\t\t`/pages/form/form?productName=${encodeURIComponent(item.productName)}&shareMode=1`;\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: simpleUrl\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.log('✅ URL长度正常（' + formUrl.length + '字符），使用完整数据传递');\n\t\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\t\turl: formUrl\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tconst formattedData = subCategoryResponse.data.data.rows.map(row => this.formatAPIData(row));\n\t\t\t\t\trecommendations = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));\n\n\t\t\t\t\t// 传递父级分类的介绍资料（使用包含 DownloadUrl 的完整信息）\n\t\t\t\t\tconst parentMaterials = parentMaterialsWithDownloadUrl.length > 0 ? parentMaterialsWithDownloadUrl : (item.introductionMaterials || []);\n\t\t\t\t\tconsole.log('Index页面传递父级介绍资料:', parentMaterials);\n\n\t\t\t\t\t// 传递父级分类的相关链接\n\t\t\t\t\tconst parentRelatedLinks = item.relatedLinks || [];\n\t\t\t\t\tconsole.log('Index页面传递父级相关链接:', parentRelatedLinks);\n\n\t\t\t\t\t// 由于URL长度限制，改为只传递核心参数，让category页面重新获取数据\n\t\t\t\t\tconst categoryUrl = `/pages/category/category?title=${encodeURIComponent(item.productName)}&pm=${encodeURIComponent(item.productManager)}&parentImage=${encodeURIComponent(item.imageUrl || '/static/熙迈LOGO.png')}&parentContactPhone=${encodeURIComponent(item.contactPhone || '')}&parentMaterials=${encodeURIComponent(JSON.stringify(parentMaterials))}&parentRelatedLinks=${encodeURIComponent(parentRelatedLinks.join(','))}&shareMode=1`;\n\n\t\t\t\t\tconsole.log('=== Index页面跳转到Category页面 ===');\n\t\t\t\t\tconsole.log('跳转的产品信息:', {\n\t\t\t\t\t\tproductName: item.productName,\n\t\t\t\t\t\tproductManager: item.productManager,\n\t\t\t\t\t\timageUrl: item.imageUrl,\n\t\t\t\t\t\tformattedDataCount: formattedData.length,\n\t\t\t\t\t\trecommendationsCount: recommendations.length\n\t\t\t\t\t});\n\t\t\t\t\tconsole.log('跳转URL:', categoryUrl);\n\t\t\t\t\tconsole.log('URL长度:', categoryUrl.length);\n\t\t\t\t\tconsole.log('=== 开始跳转（使用分享模式避免URL过长）===');\n\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: categoryUrl\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取子分类失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取子分类失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t/* 广告弹窗样式 */\n\t.ad-popup-overlay {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 9999;\n\t}\n\n\t.ad-popup-content {\n\t\tbackground-color: #fff;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx;\n\t\tmargin: 40rpx;\n\t\tmax-width: 600rpx;\n\t\twidth: 80%;\n\t\tposition: relative;\n\t\tbox-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);\n\t}\n\n\t.ad-popup-close {\n\t\tposition: absolute;\n\t\ttop: 20rpx;\n\t\tright: 30rpx;\n\t\tfont-size: 40rpx;\n\t\tcolor: #999;\n\t\tcursor: pointer;\n\t\tz-index: 10000;\n\t}\n\n\t.ad-popup-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\ttext-align: center;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.ad-popup-image {\n\t\twidth: 100%;\n\t\tmax-height: 400rpx;\n\t\tborder-radius: 10rpx;\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.ad-popup-button {\n\t\tbackground-color: #5a7fb8;\n\t\tcolor: #fff;\n\t\ttext-align: center;\n\t\tpadding: 20rpx 40rpx;\n\t\tborder-radius: 50rpx;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcursor: pointer;\n\t}\n\n\t.ad-popup-button:active {\n\t\tbackground-color: #4a6fa8;\n\t}\n\n\t.search-box {\n\t\tpadding: 20rpx;\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t\tbackground-color: #fff;\n\t\tborder-bottom: 1rpx solid #eee;\n\t}\n\n\t.search-input {\n\t\tflex: 1;\n\t\theight: 70rpx;\n\t\tpadding: 0 20rpx;\n\t\tborder: 1rpx solid #ddd;\n\t\tborder-radius: 35rpx;\n\t}\n\n\t.search-btn {\n\t\twidth: 140rpx;\n\t\theight: 70rpx;\n\t\tline-height: 70rpx;\n\t\tborder-radius: 35rpx;\n\t\tbackground-color: #007AFF;\n\t\tcolor: white;\n\t}\n\n\t.container {\n\t\tposition: relative;\n\t\tbackground: #42f3f933;\n\t\tanimation: bg-flow 25s linear infinite;\n\t\tmin-height: 100vh;\n\t\tpadding: 0;\n\t\tpadding-bottom: calc(200rpx + env(safe-area-inset-bottom));\n\t}\n\n\t/* 提升内容层级 */\n\t.search-box,\n\t.category-list,\n\t.no-result {\n\t\tposition: relative;\n\t\tz-index: 1;\n\t}\n\n\t.category-list {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tjustify-content: space-between;\n\t\tpadding: 24rpx;\n\t}\n\t\n\t.category-item {\n\t\twidth: calc(50% - 12rpx);\n\t\tmargin-bottom: 24rpx;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n\t\toverflow: hidden;\n\t\tposition: relative;\n\t}\n\t\n\t.img {\n\t\twidth: 100%;\n\t\theight: 250rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\toverflow: hidden;\n\t}\n\t\n\t.img .icon {\n\t\theight: 100%;\n\t\twidth: 100%;\n\t\tdisplay: block;\n\t\tobject-fit: contain;\n\t}\n\t\n\t.banner {\n\t\tpadding: 10rpx;\n\t}\n\t\n\t.banner-left {\n\t\twidth: 100%;\n\t}\n\t\n\t.top-blue {\n\t\theight: 60rpx;\n\t\twidth: 100%;\n\t\tborder-radius: 15rpx;\n\t\tbackground-color: #6d92cc;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 8rpx 12rpx;\n\t\tbox-sizing: border-box;\n\t}\n\t\n\t.english-name {\n\t\tcolor: #ffffff;\n\t\tfont-size: 16rpx;\n\t\ttext-align: center;\n\t\tline-height: 1.4;\n\t\tword-wrap: break-word;\n\t\tword-break: break-all;\n\t\twidth: 100%;\n\t}\n\t\n\t.bottom-white {\n\t\tcolor: #6d92cc;\n\t\tmin-height: 80rpx;\n\t\twidth: 100%;\n\t\tbackground-color: #fff;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 10rpx;\n\t\tbox-sizing: border-box;\n\t}\n\t\n\t.bottom-white-text {\n\t\tflex: 1;\n\t\tmin-width: 0;\n\t\tfont-size: 22rpx;\n\t\ttext-align: center;\n\t\twhite-space: normal;\n\t\tline-height: 1.4;\n\t\tmargin: 0 8rpx;\n\t}\n\t\n\t.logo {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tflex-shrink: 0;\n\t\twidth: 70rpx;\n\t\theight: 70rpx;\n\t}\n\t\n\t.logo .icon {\n\t\tmax-width: 100%;\n\t\tmax-height: 100%;\n\t\tobject-fit: contain;\n\t\tpadding: 0;\n\t}\n\t\n\t.like-section {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 0 10rpx;\n\t\tflex-shrink: 0;\n\t}\n\t\n\t.like-icon {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t}\n\n\t.red-heart-animation {\n\t\tposition: absolute;\n\t\tright: 12rpx;\n\t\tbottom: 30rpx;\n\t\tfont-size: 30rpx;\n\t\tanimation: pop-heart 0.6s ease-out forwards;\n\t\tpointer-events: none;\n\t}\n\t\n\t.pop-animation {\n\t\tposition: absolute;\n\t\tbottom: 100%; /* 从按钮上方开始 */\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-direction: column;\n\t\tanimation: pop-up 0.8s ease-out forwards;\n\t\tpointer-events: none;\n\t}\n\t\n\t.pop-heart {\n\t\tfont-size: 30rpx;\n\t}\n\t\n\t.pop-count {\n\t\tfont-size: 24rpx;\n\t\tcolor: #ff6a6a;\n\t\tfont-weight: bold;\n\t}\n\n\t@keyframes pop-up {\n\t\t0% {\n\t\t\ttransform: translateX(-50%) scale(0.5);\n\t\t\topacity: 0;\n\t\t\tbottom: 100%;\n\t\t}\n\t\t50% {\n\t\t\ttransform: translateX(-50%) scale(1.2);\n\t\t\topacity: 1;\n\t\t}\n\t\t100% {\n\t\t\ttransform: translateX(-50%) scale(1);\n\t\t\topacity: 0;\n\t\t\tbottom: 150%; /* 向上飘动 */\n\t\t}\n\t}\n\n\t@keyframes pop-heart {\n\t\t0% {\n\t\t\ttransform: scale(0.5);\n\t\t\topacity: 0;\n\t\t}\n\n\t\t50% {\n\t\t\ttransform: scale(1.6);\n\t\t\topacity: 1;\n\t\t}\n\n\t\t100% {\n\t\t\ttransform: scale(1);\n\t\t\topacity: 0;\n\t\t}\n\t}\n\n\t.no-result {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 40rpx;\n\t\tmargin: 40rpx;\n\t\tbackground-color: #f9f9f9;\n\t\tborder-radius: 20rpx;\n\t\ttext-align: center;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n\t}\n\n\t.no-result-icon {\n\t\tfont-size: 100rpx;\n\t\tcolor: #e0e0e0;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.no-result-text {\n\t\tfont-size: 32rpx;\n\t\tcolor: #666;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.no-result-subtext {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\n\t.service-btn {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 10rpx;\n\t\tbackground: linear-gradient(135deg, #2979FF, #00B4FF);\n\t\tborder-radius: 24rpx;\n\t\tbox-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);\n\t\ttransition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n\t\tbackdrop-filter: blur(10px);\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.1);\n\t}\n\n\t.text-container {\n\t\tflex: 1;\n\t}\n\n\t.btn-text {\n\t\tcolor: #fff;\n\t\tfont-size: 38rpx;\n\t\tfont-weight: 600;\n\t\ttext-align: center;\n\t\tdisplay: block;\n\t\tmargin-bottom: 0;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.btn-subtext {\n\t\tcolor: rgba(255, 255, 255, 0.9);\n\t\tfont-size: 24rpx;\n\t\tline-height: 1.6;\n\t\ttext-align: center;\n\t\tdisplay: block;\n\t\topacity: 0.9;\n\t\tmargin-bottom: 16rpx;\n\t}\n\n\t/* 交互动画 */\n\t.service-btn:active {\n\t\ttransform: scale(0.98) translateY(2rpx);\n\t\tbox-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);\n\t}\n\n\n\t/* 悬浮呼吸动画 */\n\t@keyframes float {\n\t\t0% {\n\t\t\ttransform: translateY(0);\n\t\t}\n\n\t\t50% {\n\t\t\ttransform: translateY(-10rpx);\n\t\t}\n\n\t\t100% {\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t.service-btn {\n\t\tanimation: float 3s ease-in-out infinite;\n\t}\n\n\t/* 流光边框效果 */\n\t.service-btn::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: -1rpx;\n\t\tleft: -1rpx;\n\t\tright: -1rpx;\n\t\tbottom: -1rpx;\n\t\tbackground: linear-gradient(45deg,\n\t\t\t\trgba(255, 255, 255, 0) 0%,\n\t\t\t\trgba(255, 255, 255, 0.3) 50%,\n\t\t\t\trgba(255, 255, 255, 0) 100%);\n\t\tborder-radius: 24rpx;\n\t\tanimation: shine 3s infinite;\n\t\tz-index: -1;\n\t}\n\n\t@keyframes shine {\n\t\t0% {\n\t\t\topacity: 0;\n\t\t\tleft: -50%;\n\t\t}\n\n\t\t50% {\n\t\t\topacity: 0.4;\n\t\t}\n\n\t\t100% {\n\t\t\topacity: 0;\n\t\t\tleft: 150%;\n\t\t}\n\t}\n\n</style>", "import MiniProgramPage from 'D:/小程序项目/熙迈门户/XMMH/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ShareUtils", "response", "uni", "FIELD_MAPPING"], "mappings": ";;;;;;EAqFE,OAAA;AACC,WAAA;AAAA,MACC,eAAA;AAAA;;;MAIA,aAAA;AAAA,MACA,QAAA,CAAA;AAAA,IACD;AAAA;EAED,SAAA;AACC,SAAA,cAAA;AACA,SAAA,eAAA;AAAA;EAED,SAAA;;;;;;AAOC,WAAAA,YAAAA,WAAA,sBAAA;AAAA;;;IAIA,CAAA;AAAA;EAGD,gBAAA,KAAA;;AAEC,WAAAA,YAAAA,WAAA,sBAAA;AAAA;;;IAIA,CAAA;AAAA;EAGD,SAAA;AAAA;AAAA,IAEC,MAAA,iBAAA;AACC,UAAA;AACC,cAAA,cAAA;AAAA;;UAGC,aAAA;AAAA,UACA,QAAA;AAAA;;;;UAKA,SAAA;AAAA,YACC;AAAA;;;;;cAKC,OAAA;AAAA;AAAA,YACD;AAAA,UACD;AAAA;AAGD,cAAAC,YAAA,MAAAC,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;UAEA,MAAA;AAAA,UACA,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;;AAGC,gBAAA,QAAAD,UAAA,KAAA,KAAA,KAAA,CAAA;;YAEC,IAAA,MAAA,0BAAA;AAAA;AAAA;;;;YAGA,UAAA,KAAA,YAAA,MAAA,0BAAA,CAAA;AAAA;AAAA;;;;;;;;AAQA,iBAAA,cAAA;AAAA,UACD;AAAA,QACD;AAAA;AAEAC,sBAAA,MAAA,MAAA,SAAA,gCAAA,eAAA,KAAA;AAAA,MACD;AAAA;;;;;AAMA,UAAA;AACC,cAAA,SAAA,OAAA,mBAAA,WAAA,KAAA,MAAA,cAAA,IAAA;;;QAGA;AAAA,MACD,SAAA,GAAA;AACCA,sBAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,CAAA;AAAA,MACD;AACA,aAAA;AAAA;;IAID,kBAAA;AACC,UAAA,CAAA,KAAA,OAAA;AAAA,eAAA;AAEA,YAAA,QAAA,oBAAA;;AAIA,UAAA,KAAA,OAAA,aAAA,WAAA,KAAA,OAAA;AAAA,eAAA;AACA,UAAA,KAAA,OAAA,WAAA,WAAA,KAAA,OAAA;AAAA,eAAA;;AAIA,UAAA,cAAA;AAAA,eAAA;;;;IAMD,eAAA;;;AAICA,oBAAAA,MAAA,eAAA,oBAAA,KAAA;AAAA;;IAID,gBAAA;;;AAIEA,sBAAAA,MAAA,eAAA,oBAAA,KAAA;;AAMAA,sBAAAA,MAAA,WAAA;AAAA;;AAGEA,0BAAA,MAAA,MAAA,SAAA,gCAAA,WAAA,GAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACC,OAAA;AAAA;YAED,CAAA;AAAA,UACD;AAAA,QACD,CAAA;AAAA,MACD;AAAA;IAGD,oBAAA;;;;AAIE,aAAA,WAAA,QAAA,UAAA;;AAEE,iBAAA,KAAA,MAAA,WAAA,IAAA;AAAA;AAEA,iBAAA,KAAA,MAAA,WAAA,KAAA;AAAA,UACD;AAEA,cAAA,KAAA,YAAA,KAAA,SAAA,SAAA,GAAA;;;AAGG,qBAAA,KAAA,OAAA,WAAA,IAAA;AAAA;AAEA,qBAAA,KAAA,OAAA,WAAA,KAAA;AAAA,cACD;AAAA,YACD,CAAA;AAAA,UACD;AAAA,QACD,CAAA;AAAA,MACD;AAAA;IAED,WAAA,MAAA;;AAIC,UAAA,gBAAA;AAEA,UAAA,KAAA,SAAA;AAEC,aAAA;;AAEA,aAAA,KAAA,KAAA,UAAA,KAAA,OAAA,IAAA;AAGA,aAAA,KAAA,MAAA,aAAA,IAAA;AACA,mBAAA,MAAA;AACC,eAAA,KAAA,MAAA,aAAA,KAAA;AAAA,QACD,GAAA,GAAA;AAAA;AAGA,aAAA;;AAEA,aAAA,QAAA,KAAA,UAAA,KAAA,KAAA;AAAA,MACD;AAGAA,oBAAAA,MAAA,eAAA,aAAA,KAAA,QAAA;;QAIC,KAAA;AAAA;QAEA,QAAA;AAAA,UACC,gBAAA;AAAA;;UAGA,QAAA;AAAA;;QAGD,SAAA,CAAA,QAAA;AACCA,wBAAAA,MAAA,MAAA,OAAA,gCAAA,YAAA,aAAA,eAAA,KAAA,KAAA,iCAAA,IAAA,IAAA;AAAA;;;QAID;AAAA,MACD,CAAA;AAAA;IAED,cAAA,KAAA;AACC,YAAA,gBAAA;AAAA,QACC,OAAA,IAAA,SAAA;AAAA;QAEA,WAAA,SAAA,IAAA,KAAA,CAAA,KAAA;AAAA;QAEA,WAAA;AAAA;AAED,aAAA,KAAAC,cAAAA,aAAA,EAAA,QAAA,SAAA;AACC,cAAA,aAAAA,4BAAA,GAAA;AACA,sBAAA,GAAA,IAAA,IAAA,UAAA,KAAA;AAAA,MACD,CAAA;AAGA,oBAAA,mBAAA,SAAA,IAAAA,cAAAA,cAAA,gBAAA,CAAA,KAAA;AAGA,UAAA,cAAA,YAAA,IAAA,UAAA;AACC,sBAAA,WAAA,KAAA,YAAA,cAAA,YAAA,IAAA,QAAA;AAAA,MACD;AAKA,aAAA;AAAA;IAED,MAAA,eAAA;AACC,UAAA,CAAA,KAAA,cAAA,QAAA;AACCD,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AACA;AAAA,MACD;AAEA,UAAA;AACC,cAAA,cAAA;AAAA;;UAGC,aAAA;AAAA,UACA,QAAA;AAAA;;;;UAKA,SAAA;AAAA,YAAA;AAAA,cACE,WAAA;AAAA;;;;;YAMD;AAAA,cACC,WAAA;AAAA;;;;YAKD;AAAA,UACD;AAAA;AAGD,cAAAD,YAAA,MAAAC,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;UAEA,MAAA;AAAA,UACA,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;;AAGCA,wBAAA,MAAA,MAAA,SAAA,gCAAA,SAAAD,SAAA;AACAC,wBAAAA,MAAA,UAAA;AAAA;;UAGA,CAAA;AACA;AAAA,QACD;AAEA,YAAAD,UAAA,QAAAA,UAAA,KAAA,MAAA;;AAEE,kBAAA,gBAAAA,UAAA,KAAA,KAAA,KAAA,IAAA,SAAA,KAAA,cAAA,GAAA,CAAA;AACAC,0BAAA,MAAA,MAAA,OAAA,gCAAA,mBAAA,aAAA;AACAA,0BAAAA,MAAA,WAAA;AAAA;YAEA,CAAA;AAAA,UACD,WAAAD,UAAA,KAAA,KAAA,UAAA,KAAAA,UAAA,KAAA,eAAA,GAAA;AACCC,0BAAAA,MAAA,UAAA;AAAA,cACC,OAAA;AAAA;;YAGD,CAAA;AAAA,UACD;AAAA;AAEAA,wBAAA,MAAA,MAAA,SAAA,gCAAA,eAAAD,UAAA,IAAA;AACAC,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AAAA,QACD;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,gCAAA,WAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AAAA,MACD;AAAA;IAED,MAAA,gBAAA;AACC,UAAA;AACC,cAAAD,YAAA,MAAAC,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;;;;YAKC,aAAA;AAAA,YACA,QAAA;AAAA;;;;YAKA,SAAA,CAAA;AAAA,cACC,WAAA;AAAA;;;YAID,CAAA;AAAA,YACA,QAAA;AAAA;UAED,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;AAEAA,sBAAA,MAAA,MAAA,OAAA,gCAAA,iBAAAD,SAAA;;AAGCC,wBAAA,MAAA,MAAA,SAAA,gCAAA,SAAAD,SAAA;AACAC,wBAAAA,MAAA,UAAA;AAAA;;UAGA,CAAA;AACA;AAAA,QACD;AAEA,YAAAD,UAAA,QAAAA,UAAA,KAAA,QAAAA,UAAA,KAAA,KAAA,MAAA;AACCC,8BAAA,MAAA,OAAA,gCAAA,aAAAD,UAAA,KAAA,KAAA,IAAA;AACA,eAAA,aAAAA,UAAA,KAAA,KAAA,KAAA,IAAA,SAAA,KAAA,cAAA,GAAA,CAAA;;;AAMAC,wBAAA,MAAA,MAAA,SAAA,gCAAA,eAAAD,UAAA,IAAA;AACAC,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AAAA,QACD;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,gCAAA,WAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AAAA,MACD;AAAA;;AAGA,UAAA;AAEC,cAAA,aAAA,MAAA,QAAA,SAAA,IAAA,YAAA,KAAA,MAAA,SAAA;AAEA,YAAA,CAAA,MAAA,QAAA,UAAA,GAAA;;AAEC,iBAAA;AAAA,QACD;AAGA,cAAA,aAAA,WAAA;AAAA,UAAA,UAAA;;AACC,+BAAA,YAAA,mBAAA,WAAA,cACA,UAAA,wBAAA,mBAAA,WAAA;AAAA;AAAA;sEAKA,yCAAA,wBACA;AAAA;AAGDA,sBAAA,MAAA,MAAA,SAAA,gCAAA,SAAA,MAAA,OAAA;AACA,eAAA;AAAA,MACD;AAAA;IAED,MAAA,oBAAA,MAAA;AACC,UAAA,kBAAA,CAAA;AACA,UAAA,iCAAA,CAAA;AAEA,UAAA;AAEC,cAAA,2BAAA,KAAA,yBAAA,KAAA,sBAAA,SAAA;;AAICA,wBAAAA,MAAA,MAAA,OAAA,gCAAA,qBAAA;AACA,cAAA;AACC,kBAAA,mBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,cACC,KAAA;AAAA;;;;gBAKC,aAAA;AAAA;;;;gBAKA,SAAA,CAAA;AAAA,kBACC,WAAA;AAAA;;;kBAIA,OAAA,KAAA;AAAA;;cAGF,QAAA;AAAA,gBACC,gBAAA;AAAA,cACD;AAAA,YACD,CAAA;AAEA,gBAAA,iBAAA,QAAA,iBAAA,KAAA,QAAA,iBAAA,KAAA,KAAA,QAAA,iBAAA,KAAA,KAAA,KAAA,SAAA,GAAA;;;;AAIEA,8BAAA,MAAA,MAAA,OAAA,gCAAA,WAAA,aAAA;;AAECA,gCAAA,MAAA,MAAA,OAAA,gCAAA,WAAA,QAAA;AACA,yBAAA;AAAA;AAAA,oBAEC,MAAA,SAAA,sBAAA;AAAA,oBACA,KAAA,SAAA,eAAA,SAAA,2BAAA;AAAA;;oBAGA,aAAA,SAAA,eAAA;AAAA;;oBAGA,WAAA,SAAA,aAAA;AAAA;gBAEF,CAAA;AACAA,8BAAA,MAAA,MAAA,OAAA,gCAAA,aAAA,8BAAA;AAAA,cACD;AAAA,YACD;AAAA;AAEAA,0BAAA,MAAA,MAAA,SAAA,gCAAA,eAAA,KAAA;;UAGD;AAAA,QACD;AAEA,cAAA,sBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;;;;YAKC,aAAA;AAAA;;YAGA,OAAA,KAAA;AAAA,YACA,WAAA;AAAA,YACA,kBAAA;AAAA;UAED,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;AACA,cAAA,oBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;;;;YAKC,aAAA;AAAA;;YAGA,OAAA,KAAA;AAAA,YACA,WAAA;AAAA,YACA,kBAAA;AAAA;UAED,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;AAEA,YAAA,oBAAA,eAAA,KAAA;AACCA,wBAAA,MAAA,MAAA,SAAA,gCAAA,YAAA,QAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AACA;AAAA,QACD;;AAICA,wBAAA,MAAA,MAAA,SAAA,gCAAA,eAAA,oBAAA,IAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AACA;AAAA,QACD;AAEA,YAAA,CAAA,oBAAA,KAAA,KAAA,QAAA,oBAAA,KAAA,KAAA,KAAA,WAAA,GAAA;AACC,4BAAA,kBAAA,KAAA,KAAA,KAAA,IAAA,SAAA,KAAA,cAAA,GAAA,CAAA;AAEA,cAAA,cAAA,CAAA;;AAEC,gBAAA;AACC,oBAAA,sBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,gBACC,KAAA;AAAA;;;;kBAKC,aAAA;AAAA;;kBAGA,OAAA,KAAA;AAAA,kBACA,WAAA;AAAA,kBACA,kBAAA;AAAA;gBAED,QAAA;AAAA,kBACC,gBAAA;AAAA,gBACD;AAAA,cACD,CAAA;;;;kBAIE,SAAA,IAAAC,4BAAA,WAAA,KAAA;AAAA,gBACD,EAAA;AAAA,cACD;AAAA,YACD,SAAA,GAAA;AACCD,4BAAA,MAAA,MAAA,SAAA,gCAAA,aAAA,CAAA;AAAA,YACD;AAAA,UACD;;AAKAA,wBAAAA,MAAA,MAAA,OAAA,gCAAA,0BAAA;AACAA,wBAAA,MAAA,MAAA,OAAA,gCAAA,cAAA,IAAA;;;;YAIC,oBAAA,KAAA;AAAA,YACA,gBAAA,KAAA;AAAA,YACA,kBAAA,YAAA;AAAA,UACD,CAAA;AACAA,wBAAA,MAAA,MAAA,OAAA,gCAAA,WAAA,OAAA;AACAA,wBAAA,MAAA,MAAA,OAAA,gCAAA,YAAA,QAAA,MAAA;AAGA,cAAA,QAAA,SAAA,MAAA;AACCA,gCAAA,MAAA,OAAA,gCAAA,cAAA,QAAA,SAAA,YAAA;AACA,kBAAA,YAAA,KAAA,cACC,gCAAA,mBAAA,KAAA,WAAA,CAAA,iBACA,gCAAA,mBAAA,KAAA,WAAA,CAAA;AACDA,0BAAAA,MAAA,WAAA;AAAA;YAEA,CAAA;AAAA;AAEAA,gCAAA,MAAA,OAAA,gCAAA,eAAA,QAAA,SAAA,cAAA;AACAA,0BAAAA,MAAA,WAAA;AAAA;YAEA,CAAA;AAAA,UACD;AACA;AAAA,QACD;AAEA,cAAA,gBAAA,oBAAA,KAAA,KAAA,KAAA,IAAA,SAAA,KAAA,cAAA,GAAA,CAAA;AACA,0BAAA,kBAAA,KAAA,KAAA,KAAA,IAAA,SAAA,KAAA,cAAA,GAAA,CAAA;;AAIAA,sBAAA,MAAA,MAAA,OAAA,gCAAA,oBAAA,eAAA;AAGA,cAAA,qBAAA,KAAA,gBAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,gCAAA,oBAAA,kBAAA;;AAKAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,8BAAA;;;UAGC,gBAAA,KAAA;AAAA;UAEA,oBAAA,cAAA;AAAA,UACA,sBAAA,gBAAA;AAAA,QACD,CAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,gCAAA,UAAA,WAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,gCAAA,UAAA,YAAA,MAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,gCAAA,4BAAA;AAEAA,sBAAAA,MAAA,WAAA;AAAA,UACC,KAAA;AAAA,QACD,CAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,gCAAA,YAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxtBD,GAAG,WAAW,eAAe;"}