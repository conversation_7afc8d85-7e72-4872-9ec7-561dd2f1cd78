{"version": 3, "file": "home.js", "sources": ["pages/home/<USER>", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaG9tZS9ob21lLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<view class=\"logo-container\">\r\n\t\t\t<image class=\"logo\" src=\"/static/DN&XM.png\" mode=\"aspectFit\"></image>\r\n\t\t</view>\r\n\t\t<view class=\"main-button-group\">\r\n\t\t\t<view class=\"button-item\" v-for=\"(item, index) in mainButtons\" :key=\"index\"\r\n\t\t\t\t@click=\"handleButtonClick(item)\">\r\n\t\t\t\t<uni-icons :type=\"item.icon\" size=\"30\" color=\"#2979FF\"></uni-icons>\r\n\t\t\t\t<view class=\"button-text-row\">\r\n\t\t\t\t\t<text class=\"button-text-main\">{{ item.text }}</text>\r\n\t\t\t\t\t<image src=\"/static/小手.svg\" class=\"hand-icon\" mode=\"widthFix\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"sub-button-group\">\r\n\t\t\t<view class=\"button-item\" v-for=\"(item, index) in subButtons\" :key=\"index\" @click=\"handleButtonClick(item)\">\r\n\t\t\t\t<uni-icons :type=\"item.icon\" size=\"30\" color=\"#2979FF\"></uni-icons>\r\n\t\t\t\t<text class=\"button-text\">{{ item.text }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 轮播图 -->\r\n\t\t<swiper class=\"banner\" circular autoplay interval=\"5000\" indicator-dots=\"true\" indicator-color=\"#007aff\">\r\n\t\t\t<swiper-item v-for=\"(item, index) in banners\" :key=\"index\">\r\n\t\t\t\t<image :src=\"item.image\" mode=\"aspectFill\" class=\"banner-image\"\r\n\t\t\t\t\t@click=\"handleBannerClick($event,item.url)\"></image>\r\n\t\t\t</swiper-item>\r\n\t\t</swiper>\r\n\t\t<!-- 公告栏 -->\r\n\t\t<view class=\"notice\">\r\n\t\t\t<uni-icons type=\"sound\" size=\"18\" color=\"#666\"></uni-icons>\r\n\t\t\t<swiper class=\"notice-swiper\" vertical autoplay circular interval=\"3000\">\r\n\t\t\t\t<swiper-item v-for=\"(item, index) in notices\" :key=\"index\">\r\n\t\t\t\t\t<text class=\"notice-text\">{{ item }}</text>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t\t<!-- 联系我们 -->\r\n\t\t<view class=\"contact\" v-for=\"(info, index) in contactInfos\" :key=\"index\">\r\n\t\t\t<text class=\"contact-title\">{{ info.title }}</text>\r\n\t\t\t<text class=\"contact-info\" v-for=\"(phone, i) in info.phones\" :key=\"i\" @click=\"callnum(phone)\">\r\n\t\t\t\t电话{{ i + 1 }}：{{ phone }}\r\n\t\t\t</text>\r\n\t\t\t<text class=\"contact-info\"\r\n\t\t\t\t@click=\"Location(info.latitude, info.longitude, info.address)\">地址：{{ info.address }}</text>\r\n\t\t\t<text class=\"contact-info\">邮箱：{{ info.email }}</text>\r\n\t\t</view>\r\n\t</view>\r\n\r\n\t<view class=\"fixed-customer-service\">\r\n\t\t<button class=\"service-btn\"  @click=\"handleContact\" >\r\n\t\t\t<view class=\"text-container\">\r\n\t\t\t\t<text class=\"btn-text\">微信客服</text>\r\n\t\t\t\t<text class=\"btn-subtext\">如有需求，请点我联系</text>\r\n\t\t\t</view>\r\n\t\t</button>\r\n\t</view>\r\n\r\n\t<!-- 弹窗轮播图 -->\r\n\t<view v-if=\"showPopup\" class=\"popup-overlay\" @click=\"closePopup\">\r\n\t\t<view class=\"popup-content\" @click.stop>\r\n\t\t\t<view class=\"popup-close\" @click=\"closePopup\">\r\n\t\t\t\t<text class=\"close-icon\">×</text>\r\n\t\t\t</view>\r\n\t\t\t<swiper\r\n\t\t\t\tclass=\"popup-swiper\"\r\n\t\t\t\t:indicator-dots=\"popupList.length > 1\"\r\n\t\t\t\t:autoplay=\"false\"\r\n\t\t\t\t:circular=\"true\"\r\n\t\t\t\tindicator-color=\"rgba(41, 121, 255, 0.3)\"\r\n\t\t\t\tindicator-active-color=\"#2979FF\"\r\n\t\t\t\t@change=\"onSwiperChange\"\r\n\t\t\t>\r\n\t\t\t\t<swiper-item\r\n\t\t\t\t\tv-for=\"(popup, index) in popupList\"\r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\tclass=\"popup-swiper-item\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<image\r\n\t\t\t\t\t\t:src=\"popup.imageUrl\"\r\n\t\t\t\t\t\tclass=\"popup-image\"\r\n\t\t\t\t\t\tmode=\"aspectFit\"\r\n\t\t\t\t\t\t@click=\"handlePopupClick(popup)\"\r\n\t\t\t\t\t\t@load=\"handleImageLoad\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<view v-if=\"popup.title\" class=\"popup-title\">{{ popup.title }}</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t\t<!-- 轮播图指示器文字 -->\r\n\t\t\t<view v-if=\"popupList.length > 1\" class=\"popup-indicator\">\r\n\t\t\t\t{{ currentPopupIndex + 1 }} / {{ popupList.length }}\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n</template>\r\n\r\n<script>\r\n\timport ShareUtils from '@/utils/share.js';\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowPopup: false,\r\n\t\t\t\tpopupList: [], // 弹窗列表\r\n\t\t\t\tcurrentPopupIndex: 0, // 当前弹窗索引\r\n\t\t\t\tpopupData: {\r\n\t\t\t\t\ttitle: '',\r\n\t\t\t\t\timageUrl: '',\r\n\t\t\t\t\tjumpUrl: '',\r\n\t\t\t\t\tisActive: false\r\n\t\t\t\t},\r\n\t\t\t\tnotices: [\r\n\t\t\t\t\t\"欢迎使用本小程序，有问题请及时联系我们\"\r\n\t\t\t\t],\r\n\t\t\t\t// 修改为两个按钮数组\r\n\t\t\t\tmainButtons: [{\r\n\t\t\t\t\ticon: \"index\",\r\n\t\t\t\t\ttext: \"熙迈科技工业服务-点击了解更多\",\r\n\t\t\t\t\turl: \"/pages/index/index\"\r\n\t\t\t\t}],\r\n\t\t\t\tsubButtons: [{\r\n\t\t\t\t\t\ticon: \"GZH\",\r\n\t\t\t\t\t\ttext: \"公众号\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: \"SPH\",\r\n\t\t\t\t\t\ttext: \"视频号\"\r\n\t\t\t\t\t},\r\n\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \ticon: \"PMS\",\r\n\t\t\t\t\t// \ttext: \"PMS\",\r\n\t\t\t\t\t// \turl: \"https://dmit.duoningbio.com/app/3f976431-6007-4fa7-ad78-14cc163f5b66?ch=no&ac=no\"\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \ticon: \"BBS\",\r\n\t\t\t\t\t// \ttext: \"BBS\",\r\n\t\t\t\t\t// \turl: \"http://cmbbs.duoningbio.com:5443/\"\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \ticon: \"GSGW\",\r\n\t\t\t\t\t// \ttext: \"公司官网\",\r\n\t\t\t\t\t// \turl: \"http://www.smile-tech.top/\"\r\n\t\t\t\t\t// }\r\n\t\t\t\t],\r\n\t\t\t\tbanners: [{\r\n\t\t\t\t\t\timage: \"https://cdn.yun.sooce.cn/6/45743/jpg/17081760583573cc2409e377f7e2779502a46c7c54cb0.jpg?imageMogr2/thumbnail/1800x&version=1708176059\",\r\n\t\t\t\t\t\turl: \"https://cdn.yun.sooce.cn/6/45743/jpg/17081760583573cc2409e377f7e2779502a46c7c54cb0.jpg?imageMogr2/thumbnail/1800x&version=1708176059\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\timage: \"https://cdn.yun.sooce.cn/6/45743/jpg/1708175809120f6c32e3ee03340d47e26d93bf4dad34b.jpg?imageMogr2/thumbnail/1800x&version=1708175811\",\r\n\t\t\t\t\t\turl: \"https://cdn.yun.sooce.cn/6/45743/jpg/1708175809120f6c32e3ee03340d47e26d93bf4dad34b.jpg?imageMogr2/thumbnail/1800x&version=1708175811\" // 添加跳转路径\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\timage: \"https://cdn.yun.sooce.cn/6/45743/jpg/1708175809193fc9e25a917635857492cc13c0f6b7173.jpg?imageMogr2/thumbnail/1800x&version=1708175811\",\r\n\t\t\t\t\t\turl: \"https://cdn.yun.sooce.cn/6/45743/jpg/1708175809193fc9e25a917635857492cc13c0f6b7173.jpg?imageMogr2/thumbnail/1800x&version=1708175811\" // 添加跳转路径\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\timage: \"https://cdn.yun.sooce.cn/6/45743/jpg/1708176038714046bd3b4360f5077916b30961fe86281.jpg?imageMogr2/thumbnail/1800x&version=1708176040\",\r\n\t\t\t\t\t\turl: \"https://cdn.yun.sooce.cn/6/45743/jpg/1708176038714046bd3b4360f5077916b30961fe86281.jpg?imageMogr2/thumbnail/1800x&version=1708176040\" // 添加跳转路径\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\timage: \"https://cdn.yun.sooce.cn/6/45743/jpg/17081758091926bc682f48a953fa738b953441c01be09.jpg?imageMogr2/thumbnail/1800x&version=1708175811\",\r\n\t\t\t\t\t\turl: \"https://cdn.yun.sooce.cn/6/45743/jpg/17081758091926bc682f48a953fa738b953441c01be09.jpg?imageMogr2/thumbnail/1800x&version=1708175811\" // 添加跳转路径\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tcontactInfos: [{\r\n\t\t\t\t\t\ttitle: \"上海松江总部\",\r\n\t\t\t\t\t\tphones: [\"************\", \"021-68775023\"],\r\n\t\t\t\t\t\taddress: \"上海市松江区新桥镇民强路1525号30幢3层\",\r\n\t\t\t\t\t\temail: \"<EMAIL>\",\r\n\t\t\t\t\t\tlatitude: 31.038129,\r\n\t\t\t\t\t\tlongitude: 121.290108\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: \"华东服务中心\",\r\n\t\t\t\t\t\tphones: [\"************\", \"021-68775023\"],\r\n\t\t\t\t\t\taddress: \"上海市浦东新区川沙路669号曹路创意空间622-626室\",\r\n\t\t\t\t\t\temail: \"<EMAIL>\",\r\n\t\t\t\t\t\tlatitude: 31.275257,\r\n\t\t\t\t\t\tlongitude: 121.670465\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: \"西南服务中心\",\r\n\t\t\t\t\t\tphones: [\"************\", \"18980586458\"],\r\n\t\t\t\t\t\taddress: \"四川省成都市武侯区桂溪街道蜀都中心二期1号楼3单元701室\",\r\n\t\t\t\t\t\temail: \"<EMAIL>\",\r\n\t\t\t\t\t\tlatitude: 30.553102,\r\n\t\t\t\t\t\tlongitude: 104.065304\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \ttitle: \"华南服务中心\",\r\n\t\t\t\t\t// \tphones: [\"400-0000-000\", \"13450476532\"],\r\n\t\t\t\t\t// \taddress: \"深圳市龙华区民治街道民乐社区星河WORLD2期E栋22层EA10\",\r\n\t\t\t\t\t// \temail: \"<EMAIL>\",\r\n\t\t\t\t\t// \tlatitude: 22.604259,\r\n\t\t\t\t\t// \tlongitude: 114.057841\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: \"华北服务中心\",\r\n\t\t\t\t\t\tphones: [\"4000885153\", \"18686480987\"],\r\n\t\t\t\t\t\taddress: \"沈阳市和平区红椿路38-6号1-1-2\",\r\n\t\t\t\t\t\temail: \"<EMAIL>\",\r\n\t\t\t\t\t\tlatitude: 41.697049,\r\n\t\t\t\t\t\tlongitude: 123.385829\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t};\r\n\t\t},\r\n\t\t// 分享功能\r\n\t\tonShareAppMessage(res) {\r\n\t\t\tconsole.log('分享给好友', res);\r\n\t\t\treturn ShareUtils.getDefaultShareConfig({\r\n\t\t\t\ttitle: '熙迈科技服务有限公司 - SMILE',\r\n\t\t\t\tpath: 'pages/home/<USER>',\r\n\t\t\t\timageUrl: '/static/熙迈LOGO.png'\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\tonShareTimeline(res) {\r\n\t\t\tconsole.log('分享到朋友圈', res);\r\n\t\t\treturn ShareUtils.getDefaultShareConfig({\r\n\t\t\t\ttitle: '熙迈科技服务有限公司 - SMILE',\r\n\t\t\t\tpath: 'pages/home/<USER>',\r\n\t\t\t\timageUrl: '/static/熙迈LOGO.png'\r\n\t\t\t});\r\n\t\t},\r\n\r\n\t\tonLoad() {\r\n\t\t\t// 页面加载时获取弹窗数据\r\n\t\t\tthis.loadPopupData();\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 加载弹窗数据\r\n\t\t\tasync loadPopupData() {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log('开始加载弹窗数据');\r\n\t\t\t\t\tconst response = await uni.request({\r\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',\r\n\t\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\r\n\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\r\n\t\t\t\t\t\t\tworksheetId: 'xcxdcwh',\r\n\t\t\t\t\t\t\tpageSize: 10, // 获取更多弹窗数据\r\n\t\t\t\t\t\t\tpageIndex: 1,\r\n\t\t\t\t\t\t\tlistType: 0,\r\n\t\t\t\t\t\t\tcontrols: [],\r\n\t\t\t\t\t\t\tfilters: [{\r\n\t\t\t\t\t\t\t\tcontrolId: 'isActive',\r\n\t\t\t\t\t\t\t\tdataType: 36, // 复选框类型\r\n\t\t\t\t\t\t\t\tspliceType: 1,\r\n\t\t\t\t\t\t\t\tfilterType: 2, // 等于\r\n\t\t\t\t\t\t\t\tvalue: '1' // 选中状态\r\n\t\t\t\t\t\t\t}]\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\r\n\r\n\t\t\t\t\tif (response.data && response.data.data && response.data.data.rows && response.data.data.rows.length > 0) {\r\n\t\t\t\t\t\t// 获取所有激活的弹窗数据，并过滤出在有效期内的\r\n\t\t\t\t\t\tconst activePopups = response.data.data.rows.filter(row => {\r\n\t\t\t\t\t\t\tconst now = new Date();\r\n\t\t\t\t\t\t\tconst startDate = new Date(row.startDate);\r\n\t\t\t\t\t\t\tconst endDate = new Date(row.endDate);\r\n\t\t\t\t\t\t\treturn now >= startDate && now <= endDate;\r\n\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\tif (activePopups.length > 0) {\r\n\t\t\t\t\t\t\t// 处理所有有效的弹窗数据\r\n\t\t\t\t\t\t\tthis.popupList = activePopups.map(row => {\r\n\t\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t\ttitle: row.title || '',\r\n\t\t\t\t\t\t\t\t\tjumpUrl: row.jumpUrl || '',\r\n\t\t\t\t\t\t\t\t\timageUrl: this.getImageUrl(row.imageUrl),\r\n\t\t\t\t\t\t\t\t\tstartDate: row.startDate || '',\r\n\t\t\t\t\t\t\t\t\tendDate: row.endDate || '',\r\n\t\t\t\t\t\t\t\t\tisActive: row.isActive === '1'\r\n\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t// 设置第一个弹窗为当前弹窗（兼容旧逻辑）\r\n\t\t\t\t\t\t\tthis.popupData = this.popupList[0];\r\n\t\t\t\t\t\t\tthis.currentPopupIndex = 0;\r\n\t\t\t\t\t\t\tthis.showPopup = true;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('没有找到有效的弹窗数据');\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('加载弹窗数据失败:', error);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 检查弹窗是否在有效期内\r\n\t\t\tisPopupValid() {\r\n\t\t\t\tif (!this.popupData.isActive) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst now = new Date();\r\n\t\t\t\tconst startDate = this.popupData.startDate ? new Date(this.popupData.startDate) : null;\r\n\t\t\t\tconst endDate = this.popupData.endDate ? new Date(this.popupData.endDate) : null;\r\n\r\n\t\t\t\t// 如果有开始时间，检查是否已开始\r\n\t\t\t\tif (startDate && now < startDate) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 如果有结束时间，检查是否已结束\r\n\t\t\t\tif (endDate && now > endDate) {\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn true;\r\n\t\t\t},\r\n\r\n\t\t\t// 处理图片URL\r\n\t\t\tgetImageUrl(imageData) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 支持直接传入数组或JSON字符串\r\n\t\t\t\t\tconst parsedData = Array.isArray(imageData) ? imageData : JSON.parse(imageData);\r\n\r\n\t\t\t\t\tif (!Array.isArray(parsedData)) {\r\n\t\t\t\t\t\tconsole.warn(\"图片数据格式错误\");\r\n\t\t\t\t\t\treturn ''; // 弹窗没有图片时返回空字符串\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 获取第一个图片对象\r\n\t\t\t\t\tif (parsedData.length > 0) {\r\n\t\t\t\t\t\tconst firstImage = parsedData[0];\r\n\r\n\t\t\t\t\t\t// 直接使用 preview_url\r\n\t\t\t\t\t\tif (firstImage.preview_url) {\r\n\t\t\t\t\t\t\treturn firstImage.preview_url;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\treturn '';\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error(\"解析图片URL失败:\", error.message);\r\n\t\t\t\t\treturn ''; // 弹窗解析失败时返回空字符串\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭弹窗\r\n\t\t\tclosePopup() {\r\n\t\t\t\tthis.showPopup = false;\r\n\t\t\t},\r\n\r\n\t\t\t// 轮播图切换事件\r\n\t\t\tonSwiperChange(e) {\r\n\t\t\t\tthis.currentPopupIndex = e.detail.current;\r\n\t\t\t\t// 更新当前弹窗数据（兼容旧逻辑）\r\n\t\t\t\tthis.popupData = this.popupList[this.currentPopupIndex];\r\n\t\t\t},\r\n\r\n\t\t\t// 处理弹窗点击\r\n\t\t\thandlePopupClick(popup) {\r\n\t\t\t\t// 如果传入了popup参数，使用传入的数据，否则使用当前弹窗数据\r\n\t\t\t\tconst currentPopup = popup || this.popupData;\r\n\r\n\t\t\t\tif (!currentPopup.jumpUrl) {\r\n\t\t\t\t\tconsole.log('没有跳转链接');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\r\n\r\n\t\t\t\t// 检查是否是小程序链接，如果是就直接处理\r\n\t\t\t\tif (currentPopup.jumpUrl.startsWith('#小程序://')) {\r\n\t\t\t\t\t// 解析小程序链接并直接跳转\r\n\t\t\t\t\tconst match = currentPopup.jumpUrl.match(/#小程序:\\/\\/([^\\/]+)\\/(.+)/);\r\n\t\t\t\t\tif (match) {\r\n\t\t\t\t\t\tconst appName = match[1];\r\n\t\t\t\t\t\tconst path = match[2];\r\n\r\n\t\t\t\t\t\t// 使用wx.navigateTo跳转\r\n\t\t\t\t\t\twx.navigateTo({\r\n\t\t\t\t\t\t\turl: `${path}`,\r\n\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\tconsole.log('跳转成功:', res);\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\tconsole.error('跳转失败:', err);\r\n\t\t\t\t\t\t\t\t// 降级方案：跳转到首页\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (currentPopup.jumpUrl.startsWith('/pages/')) {\r\n\t\t\t\t\t// 内部页面跳转\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: currentPopup.jumpUrl\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (currentPopup.jumpUrl.startsWith('http')) {\r\n\t\t\t\t\t// 外部链接\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/WebView/WebView?url=${encodeURIComponent(currentPopup.jumpUrl)}`\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 点击后关闭弹窗\r\n\t\t\t\tthis.closePopup();\r\n\t\t\t},\r\n\r\n\t\t\t// 处理小程序链接\r\n\t\t\thandleMiniProgramLink(link) {\r\n\t\t\t\tconsole.log('处理小程序链接:', link);\r\n\r\n\t\t\t\t// 解析小程序链接格式：#小程序://小程序名称/path\r\n\t\t\t\tconst match = link.match(/#小程序:\\/\\/([^\\/]+)\\/(.+)/);\r\n\r\n\t\t\t\tif (match) {\r\n\t\t\t\t\tconst appName = match[1];\r\n\t\t\t\t\tconst path = match[2];\r\n\r\n\t\t\t\t\tconsole.log('解析结果:', { appName, path });\r\n\r\n\t\t\t\t\t// 显示确认对话框\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '跳转小程序',\r\n\t\t\t\t\t\tcontent: `即将跳转到\"${appName}\"小程序，请确认是否继续？`,\r\n\t\t\t\t\t\tconfirmText: '确认跳转',\r\n\t\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t// 用户确认后，尝试生成短链接并跳转\r\n\t\t\t\t\t\t\t\tthis.generateShortLinkAndJump(appName, path, link);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.error('小程序链接格式不正确:', link);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '链接格式错误',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 生成短链接并跳转\r\n\t\t\tasync generateShortLinkAndJump(appName, path, originalLink) {\r\n\t\t\t\t// 根据小程序名称获取对应的appId\r\n\t\t\t\tconst appIdMap = {\r\n\t\t\t\t\t'熙迈科技-SMILETECH': 'wx6ebba6c9d8c21fb1',\r\n\t\t\t\t\t// 可以添加更多小程序的映射\r\n\t\t\t\t};\r\n\r\n\t\t\t\tconst appId = appIdMap[appName];\r\n\r\n\t\t\t\tif (!appId) {\r\n\t\t\t\t\tconsole.error('未配置小程序appId:', appName);\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\tcontent: '该小程序暂未配置，请联系管理员添加配置。',\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tconfirmText: '知道了'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log('开始生成短链接...');\r\n\r\n\t\t\t\t\t// 使用微信API生成短链接\r\n\t\t\t\t\tconst shortLinkRes = await new Promise((resolve, reject) => {\r\n\t\t\t\t\t\twx.generateShortLink({\r\n\t\t\t\t\t\t\tpageUrl: `pages/index/index?shareCode=${encodeURIComponent(path)}`,\r\n\t\t\t\t\t\t\tpageTitle: `${appName}产品详情`,\r\n\t\t\t\t\t\t\tisPermanent: false, // 临时链接\r\n\t\t\t\t\t\t\tsuccess: resolve,\r\n\t\t\t\t\t\t\tfail: reject\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tconsole.log('短链接生成成功:', shortLinkRes);\r\n\r\n\t\t\t\t\tif (shortLinkRes.link) {\r\n\t\t\t\t\t\t// 复制短链接到剪贴板\r\n\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\tdata: shortLinkRes.link,\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle: '链接已生成',\r\n\t\t\t\t\t\t\t\t\tcontent: '短链接已复制到剪贴板，请在微信中打开。',\r\n\t\t\t\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\t\t\t\tconfirmText: '知道了'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthrow new Error('短链接生成失败');\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('生成短链接失败:', error);\r\n\r\n\t\t\t\t\t// 降级方案：直接尝试小程序跳转\r\n\t\t\t\t\tconsole.log('降级为直接跳转方案');\r\n\t\t\t\t\tthis.attemptMiniProgramJump(appName, path);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 尝试跳转小程序\r\n\t\t\tattemptMiniProgramJump(appName, path) {\r\n\t\t\t\t// 根据小程序名称获取对应的appId\r\n\t\t\t\tconst appIdMap = {\r\n\t\t\t\t\t'熙迈科技-SMILETECH': 'wx6ebba6c9d8c21fb1', // 熙迈科技小程序的真实appId\r\n\t\t\t\t\t// 可以添加更多小程序的映射\r\n\t\t\t\t};\r\n\r\n\t\t\t\tconst appId = appIdMap[appName];\r\n\r\n\t\t\t\tif (!appId) {\r\n\t\t\t\t\tconsole.error('未配置小程序appId:', appName);\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\tcontent: '该小程序暂未配置，请联系管理员添加配置。',\r\n\t\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\t\tconfirmText: '知道了'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconsole.log('使用appId:', appId);\r\n\t\t\t\tconsole.log('原始path:', path);\r\n\r\n\t\t\t\t// 处理特殊的分享码格式\r\n\t\t\t\tlet targetPath = '';\r\n\r\n\t\t\t\t// 如果path看起来像分享码（短字符串），则跳转到首页\r\n\t\t\t\tif (path && path.length < 20 && !/^pages\\//.test(path)) {\r\n\t\t\t\t\tconsole.log('检测到分享码格式，跳转到首页');\r\n\t\t\t\t\ttargetPath = path; // 跳转到首页\r\n\t\t\t\t} else if (path && path.startsWith('pages/')) {\r\n\t\t\t\t\ttargetPath = path;\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 默认跳转到首页\r\n\t\t\t\t\ttargetPath = 'pages/index/index';\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconsole.log('最终跳转路径:', targetPath);\r\n\r\n\t\t\t\t// 使用微信API打开小程序\r\n\t\t\t\tconsole.log('准备跳转小程序，参数:', {\r\n\t\t\t\t\tappId: appId,\r\n\t\t\t\t\tpath: targetPath,\r\n\t\t\t\t\tenvVersion: 'release'\r\n\t\t\t\t});\r\n\r\n\t\t\t\twx.navigateToMiniProgram({\r\n\t\t\t\t\tappId: appId,\r\n\t\t\t\t\tpath: targetPath,\r\n\t\t\t\t\textraData: {\r\n\t\t\t\t\t\t// 可以传递额外数据\r\n\t\t\t\t\t\tshareCode: path // 将原始分享码作为额外数据传递\r\n\t\t\t\t\t},\r\n\t\t\t\t\tenvVersion: 'release', // 正式版\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log('跳转小程序成功:', res);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('跳转小程序失败:', err);\r\n\t\t\t\t\t\tconsole.error('错误详情:', JSON.stringify(err));\r\n\r\n\t\t\t\t\t\t// 提供更友好的错误提示\r\n\t\t\t\t\t\tlet errorMsg = '跳转失败';\r\n\t\t\t\t\t\tlet content = '无法跳转到目标小程序';\r\n\r\n\t\t\t\t\t\tif (err.errMsg.includes('appId') || err.errMsg.includes('invalid')) {\r\n\t\t\t\t\t\t\terrorMsg = '配置错误';\r\n\t\t\t\t\t\t\tcontent = '小程序配置有误，请联系管理员检查appId配置。';\r\n\t\t\t\t\t\t} else if (err.errMsg.includes('permission')) {\r\n\t\t\t\t\t\t\terrorMsg = '权限不足';\r\n\t\t\t\t\t\t\tcontent = '当前小程序没有跳转到目标小程序的权限。';\r\n\t\t\t\t\t\t} else if (err.errMsg.includes('not exist')) {\r\n\t\t\t\t\t\t\terrorMsg = '小程序不存在';\r\n\t\t\t\t\t\t\tcontent = '目标小程序不存在或已下线。';\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t// 提供备用方案：复制链接\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: errorMsg,\r\n\t\t\t\t\t\t\tcontent: content + '\\n\\n是否复制小程序链接到剪贴板？',\r\n\t\t\t\t\t\t\tconfirmText: '复制链接',\r\n\t\t\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\t\t\tsuccess: (modalRes) => {\r\n\t\t\t\t\t\t\t\tif (modalRes.confirm) {\r\n\t\t\t\t\t\t\t\t\t// 复制原始链接到剪贴板\r\n\t\t\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\t\t\tdata: link,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '链接已复制',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 图片加载成功处理\r\n\t\t\thandleImageLoad(e) {\r\n\t\t\t\t// 图片加载成功，无需特殊处理\r\n\t\t\t},\r\n\r\n\t\t\t// 图片加载失败处理\r\n\t\t\thandleImageError(e) {\r\n\t\t\t\tconsole.log('图片加载失败:', e);\r\n\t\t\t\tconsole.log('失败的图片URL:', this.popupData.imageUrl);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '图片加载失败',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 2000\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\thandleContact(e) {\r\n\t\t\t\twx.openCustomerServiceChat({\r\n\t\t\t\t\textInfo: {\r\n\t\t\t\t\t\turl: 'https://work.weixin.qq.com/kfid/kfcaf7fbb93aa905a54'\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcorpId: 'wwa76e36d25343b6b9',\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\thandleButtonClick(item) {\r\n\t\t\t\tif (item.text === \"公众号\") {\r\n\t\t\t\t\twx.openOfficialAccountProfile({\r\n\t\t\t\t\t\tusername: 'gh_42bacb18625e',\r\n\t\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: res => {\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t} else if (item.icon === \"PMS\" || item.icon === \"BBS\" || item.icon === \"GSGW\") {\r\n\t\t\t\t\tif (!item.url) {\r\n\t\t\t\t\t\tconsole.error('URL is undefined for button:', item.text);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 修改为跳转到 WebView 页面\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/WebView/WebView?url=${encodeURIComponent(item.url)}`\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (item.text === \"视频号\") {\r\n\t\t\t\t\twx.openChannelsUserProfile({\r\n\t\t\t\t\t\tfinderUserName: \"sph1phDRUOAw9ds\",\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (item.icon === \"index\") {\r\n\r\n\t\t\t\t\tif (!item.url) {\r\n\t\t\t\t\t\tconsole.error('url is undefined for button:', item.text);\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: item.url\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcallnum(phone) {\r\n\t\t\t\tif (phone) {\r\n\t\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\t\tphoneNumber: phone\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tLocation(latitude, longitude, name) {\r\n\t\t\t\twx.openLocation({\r\n\t\t\t\t\tlatitude: latitude,\r\n\t\t\t\t\tlongitude: longitude,\r\n\t\t\t\t\tscale: 18,\r\n\t\t\t\t\tname: name\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\thandleBannerClick(e, url) {\r\n\t\t\t\tconsole.log(e, url, 'url')\r\n\t\t\t\t// 获取当前点击的轮播图索引\r\n\t\t\t\tconst current = e.detail.current;\r\n\t\t\t\t// 跳转到对应页面\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped>\r\n\tpage {\r\n\t\tfont-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", Arial, sans-serif;\r\n\t}\r\n\r\n\t.logo-container {\r\n\t\tpadding: 20rpx 0 0 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.logo {\r\n\t\twidth: 500rpx;\r\n\t\theight: 80rpx;\r\n\t\tfilter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));\r\n\t}\r\n\r\n\t/* 调整原有容器间距 */\r\n\t.container {\r\n\t\tpadding: 20rpx;\r\n\t\tpadding-bottom: 240rpx;\r\n\t\tpadding-top: 120rpx;\r\n\t\tbackground: linear-gradient(180deg, #f8f9ff 0%, #f0f2ff 100%);\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.container {\r\n\t\tpadding: 20rpx;\r\n\t\tpadding-bottom: 240rpx;\r\n\t\tbackground: linear-gradient(180deg, #f8f9ff 0%, #f0f2ff 100%);\r\n\t}\r\n\r\n\t.main-button-group .button-item {\r\n\t\tposition: relative;\r\n\t\toverflow: hidden;\r\n\t\tbackground: linear-gradient(135deg, #2979FF, #00B4FF);\r\n\t\tbox-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 32rpx 0;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tanimation: buttonFloat 3s ease-in-out infinite;\r\n\t}\r\n\r\n\t.main-button-group .button-text,\r\n\t.main-button-group .uni-icons {\r\n\t\tcolor: #fff !important;\r\n\t\tz-index: 2;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t/* 添加呼吸动画 */\r\n\r\n\t@keyframes buttonFloat {\r\n\r\n\t\t0%,\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: translateY(-10rpx);\r\n\t\t}\r\n\t}\r\n\r\n\t/* 流光边框效果 */\r\n\t.main-button-group .button-item::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: -1rpx;\r\n\t\tleft: -1rpx;\r\n\t\tright: -1rpx;\r\n\t\tbottom: -1rpx;\r\n\t\tbackground: linear-gradient(45deg,\r\n\t\t\t\trgba(255, 255, 255, 0) 0%,\r\n\t\t\t\trgba(255, 255, 255, 0.4) 50%,\r\n\t\t\t\trgba(255, 255, 255, 0) 100%);\r\n\t\tanimation: buttonShine 3s infinite;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t@keyframes buttonShine {\r\n\t\t0% {\r\n\t\t\topacity: 0;\r\n\t\t\tleft: -50%;\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\topacity: 0.4;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\topacity: 0;\r\n\t\t\tleft: 150%;\r\n\t\t}\r\n\t}\r\n\r\n\t.main-button-group .button-item:hover {\r\n\t\ttransform: scale(1.05);\r\n\t\tbox-shadow: 0 16rpx 40rpx rgba(41, 121, 255, 0.4);\r\n\t}\r\n\r\n\t/* 点击效果优化 */\r\n\t.main-button-group .button-item:active {\r\n\t\ttransform: scale(0.98) translateY(2rpx);\r\n\t\tbox-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);\r\n\t}\r\n\r\n\t/* 公告栏 - 添加闪烁动画 */\r\n\t.notice {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #fffbe6;\r\n\t\tborder-radius: 10rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.notice-swiper {\r\n\t\theight: 40rpx;\r\n\t\tflex: 1;\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.notice-text {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\r\n\t/* 按钮组 - 使用网格布局 */\r\n\t.main-button-group {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: 1fr;\r\n\t\tgap: 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.sub-button-group {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: repeat(4, 1fr);\r\n\t\tgap: 20rpx;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n\r\n\t.button-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 32rpx 0;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tbox-shadow: 0 6rpx 20rpx rgba(41, 121, 255, 0.1);\r\n\t}\r\n\r\n\t.button-item:active {\r\n\t\ttransform: scale(0.98);\r\n\t\tbackground: #f5f7ff;\r\n\t}\r\n\r\n\t.uni-icons {\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\r\n\t.button-text-main {\r\n\t\tfont-size: 40rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.button-text {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t/* 轮播图 - 优化指示器 */\r\n\t.banner {\r\n\t\theight: 320rpx;\r\n\t\tborder-radius: 24rpx;\r\n\t\toverflow: hidden;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tmargin-top: 40rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.banner-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\ttransition: transform 0.3s ease;\r\n\t}\r\n\r\n\t.banner swiper-item:hover .banner-image {\r\n\t\ttransform: scale(1.02);\r\n\t}\r\n\r\n\t/* 联系我们 - 卡片式设计 */\r\n\t.contact {\r\n\t\tbackground: #fff;\r\n\t\tpadding: 32rpx;\r\n\t\tborder-radius: 24rpx;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.contact-title {\r\n\t\tfont-size: 34rpx;\r\n\t\tcolor: #1a1a1a;\r\n\t\tfont-weight: 600;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tposition: relative;\r\n\t\tpadding-left: 20rpx;\r\n\t}\r\n\r\n\t.contact-title::before {\r\n\t\tcontent: \"\";\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t\twidth: 8rpx;\r\n\t\theight: 32rpx;\r\n\t\tbackground: #2979FF;\r\n\t\tborder-radius: 4rpx;\r\n\t}\r\n\r\n\t.contact-info {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 16rpx;\r\n\t\tpadding: 12rpx 0;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t}\r\n\r\n\t.contact-info:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n\t.fixed-customer-service {\r\n\t\tposition: fixed;\r\n\t\tbottom: 40rpx;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tz-index: 999;\r\n\t\twidth: 700rpx;\r\n\t}\r\n\r\n\r\n\r\n\t.service-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 10rpx;\r\n\t\tbackground: linear-gradient(135deg, #2979FF, #00B4FF);\r\n\t\tborder-radius: 24rpx;\r\n\t\tbox-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);\r\n\t\ttransition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.1);\r\n\t}\r\n\r\n\t.text-container {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.btn-text {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 38rpx;\r\n\t\tfont-weight: 600;\r\n\t\ttext-align: center;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 0;\r\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.btn-subtext {\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 1.6;\r\n\t\ttext-align: center;\r\n\t\tdisplay: block;\r\n\t\topacity: 0.9;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\r\n\t/* 交互动画 */\r\n\t.service-btn:active {\r\n\t\ttransform: scale(0.98) translateY(2rpx);\r\n\t\tbox-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);\r\n\t}\r\n\r\n\r\n\t/* 悬浮呼吸动画 */\r\n\t@keyframes float {\r\n\t\t0% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: translateY(-10rpx);\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\r\n\t.service-btn {\r\n\t\tanimation: float 3s ease-in-out infinite;\r\n\t}\r\n\r\n\t/* 流光边框效果 */\r\n\t.service-btn::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: -1rpx;\r\n\t\tleft: -1rpx;\r\n\t\tright: -1rpx;\r\n\t\tbottom: -1rpx;\r\n\t\tbackground: linear-gradient(45deg,\r\n\t\t\t\trgba(255, 255, 255, 0) 0%,\r\n\t\t\t\trgba(255, 255, 255, 0.3) 50%,\r\n\t\t\t\trgba(255, 255, 255, 0) 100%);\r\n\t\tborder-radius: 24rpx;\r\n\t\tanimation: shine 3s infinite;\r\n\t\tz-index: -1;\r\n\t}\r\n\r\n\t@keyframes shine {\r\n\t\t0% {\r\n\t\t\topacity: 0;\r\n\t\t\tleft: -50%;\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\topacity: 0.4;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\topacity: 0;\r\n\t\t\tleft: 150%;\r\n\t\t}\r\n\t}\r\n\r\n\t.service-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 10rpx;\r\n\t\tbackground: linear-gradient(135deg, #2979FF, #00B4FF);\r\n\t\tborder-radius: 24rpx;\r\n\t\tbox-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);\r\n\t\ttransition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.1);\r\n\t}\r\n\r\n\t.text-container {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.btn-text {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 38rpx;\r\n\t\tfont-weight: 600;\r\n\t\ttext-align: center;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 0;\r\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.btn-subtext {\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 1.6;\r\n\t\ttext-align: center;\r\n\t\tdisplay: block;\r\n\t\topacity: 0.9;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\r\n\t/* 交互动画 */\r\n\t.service-btn:active {\r\n\t\ttransform: scale(0.98) translateY(2rpx);\r\n\t\tbox-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);\r\n\t}\r\n\r\n\r\n\t/* 悬浮呼吸动画 */\r\n\t@keyframes float {\r\n\t\t0% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: translateY(-10rpx);\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\r\n\t.service-btn {\r\n\t\tanimation: float 3s ease-in-out infinite;\r\n\t}\r\n\r\n\t/* 流光边框效果 */\r\n\t.service-btn::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: -1rpx;\r\n\t\tleft: -1rpx;\r\n\t\tright: -1rpx;\r\n\t\tbottom: -1rpx;\r\n\t\tbackground: linear-gradient(45deg,\r\n\t\t\t\trgba(255, 255, 255, 0) 0%,\r\n\t\t\t\trgba(255, 255, 255, 0.3) 50%,\r\n\t\t\t\trgba(255, 255, 255, 0) 100%);\r\n\t\tborder-radius: 24rpx;\r\n\t\tanimation: shine 3s infinite;\r\n\t\tz-index: -1;\r\n\t}\r\n\r\n\t@keyframes shine {\r\n\t\t0% {\r\n\t\t\topacity: 0;\r\n\t\t\tleft: -50%;\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\topacity: 0.4;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\topacity: 0;\r\n\t\t\tleft: 150%;\r\n\t\t}\r\n\t}\r\n\r\n\t/* 弹窗样式 - 符合页面风格 */\r\n\t.popup-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground: linear-gradient(180deg, rgba(41, 121, 255, 0.1) 0%, rgba(0, 180, 255, 0.1) 100%);\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 9999;\r\n\t\tanimation: fadeIn 0.3s ease-out;\r\n\t}\r\n\r\n\t.popup-content {\r\n\t\tposition: relative;\r\n\t\tbackground: linear-gradient(180deg, #ffffff 0%, #f8f9ff 100%);\r\n\t\tborder-radius: 24rpx;\r\n\t\tmax-width: 640rpx;\r\n\t\twidth: 90%;\r\n\t\theight: 55vh;\r\n\t\tmargin: 40rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 20rpx 60rpx rgba(41, 121, 255, 0.2);\r\n\t\tborder: 1rpx solid rgba(41, 121, 255, 0.1);\r\n\t\tanimation: popupSlideIn 0.3s ease-out;\r\n\t}\r\n\r\n\t.popup-close {\r\n\t\tposition: absolute;\r\n\t\ttop: 20rpx;\r\n\t\tright: 20rpx;\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tbackground: linear-gradient(135deg, #2979FF, #00B4FF);\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 10;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(41, 121, 255, 0.3);\r\n\t\ttransition: all 0.3s ease;\r\n\t}\r\n\r\n\t.popup-close:active {\r\n\t\ttransform: scale(0.95);\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.4);\r\n\t}\r\n\r\n\t.close-icon {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 40rpx;\r\n\t\tfont-weight: bold;\r\n\t\tline-height: 1;\r\n\t}\r\n\r\n\t/* 轮播图样式 */\r\n\t.popup-swiper {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.popup-swiper-item {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.popup-image {\r\n\t\twidth: 100%;\r\n\t\tflex: 1;\r\n\t\tdisplay: block;\r\n\t\tobject-fit: contain;\r\n\t\tbackground: linear-gradient(180deg, #f8f9ff 0%, #f0f2ff 100%);\r\n\t\tborder-radius: 20rpx 20rpx 0 0;\r\n\t}\r\n\r\n\t.popup-title {\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tfont-weight: 600;\r\n\t\ttext-align: center;\r\n\t\tline-height: 1.4;\r\n\t\tbackground: linear-gradient(135deg, rgba(41, 121, 255, 0.9), rgba(0, 180, 255, 0.9));\r\n\t\tcolor: #fff;\r\n\t\tposition: absolute;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tz-index: 2;\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t\tborder-top: 1rpx solid rgba(255, 255, 255, 0.2);\r\n\t}\r\n\r\n\t/* 轮播图指示器 */\r\n\t.popup-indicator {\r\n\t\tposition: absolute;\r\n\t\ttop: 20rpx;\r\n\t\tleft: 20rpx;\r\n\t\tbackground: linear-gradient(135deg, rgba(41, 121, 255, 0.9), rgba(0, 180, 255, 0.9));\r\n\t\tcolor: #fff;\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tz-index: 3;\r\n\t\tbackdrop-filter: blur(10rpx);\r\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.2);\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.2);\r\n\t}\r\n\r\n\t.popup-no-image {\r\n\t\twidth: 100%;\r\n\t\theight: 400rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder: 2rpx dashed #ddd;\r\n\t}\r\n\r\n\t.no-image-text {\r\n\t\tcolor: #999;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.debug-info {\r\n\t\tmargin-top: 20rpx;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #f0f0f0;\r\n\t\tborder-radius: 8rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t@keyframes fadeIn {\r\n\t\tfrom {\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\t\tto {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t}\r\n\r\n\t@keyframes popupSlideIn {\r\n\t\tfrom {\r\n\t\t\ttransform: scale(0.8) translateY(-50rpx);\r\n\t\t\topacity: 0;\r\n\t\t}\r\n\t\tto {\r\n\t\t\ttransform: scale(1) translateY(0);\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t}\r\n\r\n\t.button-text-row {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t.hand-icon {\r\n\t\twidth: 36rpx;\r\n\t\theight: 36rpx;\r\n\t\tmargin-left: 12rpx;\r\n\t\tdisplay: inline-block;\r\n\t\tvertical-align: middle;\r\n\t}\r\n</style>", "import MiniProgramPage from 'D:/小程序项目/熙迈门户/XMMH/pages/home/<USER>'\nwx.createPage(MiniProgramPage)"], "names": ["ShareUtils", "uni", "wx", "link"], "mappings": ";;;;;EAuGE,OAAA;AACC,WAAA;AAAA,MACC,WAAA;AAAA,MACA,WAAA,CAAA;AAAA;AAAA;;;QAGC,OAAA;AAAA;;;;MAKD,SAAA;AAAA,QACC;AAAA;;;;;;MAOD,CAAA;AAAA;;;UAGE,MAAA;AAAA;QAED;AAAA;UAEC,MAAA;AAAA;;;;;;;;;;;;;;;;;MAmBF,SAAA;AAAA,QAAA;AAAA,UACE,OAAA;AAAA,UACA,KAAA;AAAA;QAED;AAAA,UACC,OAAA;AAAA,UACA,KAAA;AAAA;AAAA;QAED;AAAA,UACC,OAAA;AAAA,UACA,KAAA;AAAA;AAAA;QAED;AAAA,UACC,OAAA;AAAA,UACA,KAAA;AAAA;AAAA;QAED;AAAA,UACC,OAAA;AAAA,UACA,KAAA;AAAA;AAAA,QACD;AAAA;MAED,cAAA;AAAA,QAAA;AAAA,UACE,OAAA;AAAA,UACA,QAAA,CAAA,gBAAA,cAAA;AAAA,UACA,SAAA;AAAA,UACA,OAAA;AAAA,UACA,UAAA;AAAA,UACA,WAAA;AAAA;QAED;AAAA,UACC,OAAA;AAAA,UACA,QAAA,CAAA,gBAAA,cAAA;AAAA,UACA,SAAA;AAAA,UACA,OAAA;AAAA,UACA,UAAA;AAAA,UACA,WAAA;AAAA;QAED;AAAA,UACC,OAAA;AAAA,UACA,QAAA,CAAA,gBAAA,aAAA;AAAA,UACA,SAAA;AAAA,UACA,OAAA;AAAA,UACA,UAAA;AAAA,UACA,WAAA;AAAA;;;;;;;;;QAUD;AAAA,UACC,OAAA;AAAA,UACA,QAAA,CAAA,cAAA,aAAA;AAAA;UAEA,OAAA;AAAA,UACA,UAAA;AAAA,UACA,WAAA;AAAA,QACD;AAAA,MACD;AAAA;;;;;AAMD,WAAAA,YAAAA,WAAA,sBAAA;AAAA;;;IAIA,CAAA;AAAA;EAGD,gBAAA,KAAA;;AAEC,WAAAA,YAAAA,WAAA,sBAAA;AAAA;;;IAIA,CAAA;AAAA;EAGD,SAAA;AAEC,SAAA,cAAA;AAAA;EAGD,SAAA;AAAA;AAAA,IAEC,MAAA,gBAAA;AACC,UAAA;;AAEC,cAAA,WAAA,MAAAC,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;;;;YAKC,aAAA;AAAA,YACA,UAAA;AAAA;AAAA;;;YAIA,SAAA,CAAA;AAAA,cACC,WAAA;AAAA,cACA,UAAA;AAAA;AAAA;cAEA,YAAA;AAAA;AAAA,cACA,OAAA;AAAA;AAAA;;UAGF,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;;;AAOE,kBAAA,MAAA,oBAAA;AACA,kBAAA,YAAA,IAAA,KAAA,IAAA,SAAA;AACA,kBAAA,UAAA,IAAA,KAAA,IAAA,OAAA;AACA,mBAAA,OAAA,aAAA,OAAA;AAAA,UACD,CAAA;;AAIC,iBAAA,YAAA,aAAA,IAAA,SAAA;AACC,qBAAA;AAAA,gBACC,OAAA,IAAA,SAAA;AAAA;gBAEA,UAAA,KAAA,YAAA,IAAA,QAAA;AAAA;;;;YAKF,CAAA;AAGA,iBAAA,YAAA,KAAA,UAAA,CAAA;;AAEA,iBAAA,YAAA;AAAA,UACD;AAAA;;QAGD;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,8BAAA,aAAA,KAAA;AAAA,MACD;AAAA;;IAID,eAAA;;;MAGC;AAEA,YAAA,MAAA,oBAAA;AACA,YAAA,YAAA,KAAA,UAAA,YAAA,IAAA,KAAA,KAAA,UAAA,SAAA,IAAA;AACA,YAAA,UAAA,KAAA,UAAA,UAAA,IAAA,KAAA,KAAA,UAAA,OAAA,IAAA;;;MAKA;;;MAKA;;;;;AAOA,UAAA;AAEC,cAAA,aAAA,MAAA,QAAA,SAAA,IAAA,YAAA,KAAA,MAAA,SAAA;AAEA,YAAA,CAAA,MAAA,QAAA,UAAA,GAAA;;;QAGA;;;;;UASC;AAAA,QACD;AAEA,eAAA;AAAA;AAGAA,sBAAA,MAAA,MAAA,SAAA,8BAAA,cAAA,MAAA,OAAA;;MAED;AAAA;;;AAKA,WAAA,YAAA;AAAA;;IAID,eAAA,GAAA;AACC,WAAA,oBAAA,EAAA,OAAA;;;;;AAQA,YAAA,eAAA,SAAA,KAAA;;;AAIC;AAAA,MACD;;AAOC,cAAA,QAAA,aAAA,QAAA,MAAA,yBAAA;AACA,YAAA,OAAA;AACC,gBAAA,CAAA;AACA,gBAAA,OAAA,MAAA,CAAA;AAGAC,wBAAAA,KAAA,WAAA;AAAA;YAEC,SAAA,CAAA,QAAA;;;;;AAMCD,4BAAAA,MAAA,WAAA;AAAA;cAEA,CAAA;AAAA,YACD;AAAA,UACD,CAAA;AAAA,QACD;AAAA;AAGAA,sBAAAA,MAAA,WAAA;AAAA;QAEA,CAAA;AAAA;AAGAA,sBAAAA,MAAA,WAAA;AAAA,UACC,KAAA,8BAAA,mBAAA,aAAA,OAAA,CAAA;AAAA,QACD,CAAA;AAAA,MACD;AAGA,WAAA,WAAA;AAAA;;;;;AAUA,UAAA,OAAA;AACC,cAAA,UAAA,MAAA,CAAA;AACA,cAAA,OAAA,MAAA,CAAA;AAEAA,4BAAA,MAAA,OAAA,8BAAA,SAAA,EAAA,SAAA,KAAA,CAAA;AAGAA,sBAAAA,MAAA,UAAA;AAAA;UAEC,SAAA,SAAA,OAAA;AAAA,UACA,aAAA;AAAA,UACA,YAAA;AAAA,UACA,SAAA,CAAA,QAAA;AACC,gBAAA,IAAA,SAAA;;YAGA;AAAA,UACD;AAAA,QACD,CAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,8BAAA,eAAAE,KAAA;AACAF,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AAAA,MACD;AAAA;;;AAMA,YAAA,WAAA;AAAA,QACC,kBAAA;AAAA;AAAA;;;AAOAA,sBAAA,MAAA,MAAA,SAAA,8BAAA,gBAAA,OAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA;UAEC,SAAA;AAAA,UACA,YAAA;AAAA,UACA,aAAA;AAAA,QACD,CAAA;AACA;AAAA,MACD;AAEA,UAAA;;;AAKEC,wBAAAA,KAAA,kBAAA;AAAA,YACC,SAAA,+BAAA,mBAAA,IAAA,CAAA;AAAA;;;YAGA,SAAA;AAAA;UAED,CAAA;AAAA,QACD,CAAA;AAEAD,sBAAA,MAAA,MAAA,OAAA,8BAAA,YAAA,YAAA;AAEA,YAAA,aAAA,MAAA;AAECA,wBAAAA,MAAA,iBAAA;AAAA;;AAGEA,4BAAAA,MAAA,UAAA;AAAA;;gBAGC,YAAA;AAAA,gBACA,aAAA;AAAA,cACD,CAAA;AAAA,YACD;AAAA,UACD,CAAA;AAAA;;QAGD;AAAA;AAGAA,sBAAA,MAAA,MAAA,SAAA,8BAAA,YAAA,KAAA;;AAIA,aAAA,uBAAA,SAAA,IAAA;AAAA,MACD;AAAA;;IAID,uBAAA,SAAA,MAAA;AAEC,YAAA,WAAA;AAAA;;;;;;AAQCA,sBAAA,MAAA,MAAA,SAAA,8BAAA,gBAAA,OAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA;UAEC,SAAA;AAAA,UACA,YAAA;AAAA,UACA,aAAA;AAAA,QACD,CAAA;AACA;AAAA,MACD;;;AAMA,UAAA,aAAA;;;;MAMA,WAAA,QAAA,KAAA,WAAA,QAAA,GAAA;AACC,qBAAA;AAAA;AAGA,qBAAA;AAAA,MACD;AAEAA,oBAAA,MAAA,MAAA,OAAA,8BAAA,WAAA,UAAA;;;QAKC,MAAA;AAAA,QACA,YAAA;AAAA,MACD,CAAA;;;QAIC,MAAA;AAAA;;;;;;;QAMA,SAAA,CAAA,QAAA;;;;;AAKCA,8BAAA,MAAA,SAAA,8BAAA,SAAA,KAAA,UAAA,GAAA,CAAA;AAGA,cAAA,WAAA;;AAGA,cAAA,IAAA,OAAA,SAAA,OAAA,KAAA,IAAA,OAAA,SAAA,SAAA,GAAA;AACC,uBAAA;AACA,sBAAA;AAAA,UACD,WAAA,IAAA,OAAA,SAAA,YAAA,GAAA;AACC,uBAAA;;UAED,WAAA,IAAA,OAAA,SAAA,WAAA,GAAA;AACC,uBAAA;;UAED;AAGAA,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA,YACA,SAAA,UAAA;AAAA,YACA,aAAA;AAAA,YACA,YAAA;AAAA,YACA,SAAA,CAAA,aAAA;AACC,kBAAA,SAAA,SAAA;AAECA,8BAAAA,MAAA,iBAAA;AAAA,kBACC,MAAA;AAAA;AAECA,kCAAAA,MAAA,UAAA;AAAA;;oBAGA,CAAA;AAAA,kBACD;AAAA,gBACD,CAAA;AAAA,cACD;AAAA,YACD;AAAA,UACD,CAAA;AAAA,QACD;AAAA,MACD,CAAA;AAAA;;IAID,gBAAA,GAAA;AAAA;;IAKA,iBAAA,GAAA;;AAECA,0BAAA,MAAA,OAAA,8BAAA,aAAA,KAAA,UAAA,QAAA;AACAA,oBAAAA,MAAA,UAAA;AAAA,QACC,OAAA;AAAA;;MAGD,CAAA;AAAA;IAGD,cAAA,GAAA;;QAEE,SAAA;AAAA;;;;QAKA;AAAA;;;AAID,UAAA,KAAA,SAAA,OAAA;AACCC,sBAAAA,KAAA,2BAAA;AAAA;;;;UAKC;AAAA;MAEF,WAAA,KAAA,SAAA,SAAA,KAAA,SAAA,SAAA,KAAA,SAAA,QAAA;;AAEED,wBAAA,MAAA,MAAA,SAAA,8BAAA,gCAAA,KAAA,IAAA;AACA;AAAA,QACD;AAEAA,sBAAAA,MAAA,WAAA;AAAA,UACC,KAAA,8BAAA,mBAAA,KAAA,GAAA,CAAA;AAAA,QACD,CAAA;AAAA;;UAGC,gBAAA;AAAA,QACD,CAAA;AAAA;;AAICA,wBAAA,MAAA,MAAA,SAAA,8BAAA,gCAAA,KAAA,IAAA;AACA;AAAA,QACD;AACAA,sBAAAA,MAAA,WAAA;AAAA;QAEA,CAAA;AAAA,MACD;AAAA;IAED,QAAA,OAAA;AACC,UAAA,OAAA;AACCA,sBAAAA,MAAA,cAAA;AAAA,UACC,aAAA;AAAA,QACD,CAAA;AAAA,MACD;AAAA;IAED,SAAA,UAAA,WAAA,MAAA;AACCC,oBAAAA,KAAA,aAAA;AAAA,QACC;AAAA,QACA;AAAA,QACA,OAAA;AAAA,QACA;AAAA,MACD,CAAA;AAAA;;;;AAOAD,oBAAAA,MAAA,WAAA;AAAA,QACC;AAAA,MACD,CAAA;AAAA,IACD;AAAA,EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3rBF,GAAG,WAAW,eAAe;"}