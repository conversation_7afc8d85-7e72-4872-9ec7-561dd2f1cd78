# 微信小程序分享功能说明

## 功能概述

已为您的熙迈科技小程序集成了微信原生分享功能，用户可以通过微信小程序右上角的"..."菜单进行分享。

## 已集成的页面

1. **首页** (`pages/home/<USER>
2. **产品展示页** (`pages/index/index.vue`) - 支持分享给好友和朋友圈  
3. **分类页** (`pages/category/category.vue`) - 支持分享给好友和朋友圈

## 分享配置

每个页面都配置了以下分享信息：

- **分享标题**: 根据页面内容定制
- **分享路径**: 当前页面路径
- **分享图片**: 使用熙迈LOGO (`/static/熙迈LOGO.png`)

## 如何使用

### 用户操作
1. 在小程序任意页面点击右上角的"..."按钮
2. 选择"转发"分享给微信好友
3. 选择"分享到朋友圈"分享到朋友圈

### 开发者添加新页面分享
如果需要为新页面添加分享功能，只需在页面中添加以下代码：

```javascript
// 导入分享工具类
import ShareUtils from '@/utils/share.js';

export default {
  // 分享给好友
  onShareAppMessage(res) {
    return ShareUtils.getDefaultShareConfig({
      title: '您的页面标题',
      path: 'pages/your-page/your-page',
      imageUrl: '/static/熙迈LOGO.png'
    });
  },
  
  // 分享到朋友圈
  onShareTimeline(res) {
    return ShareUtils.getDefaultShareConfig({
      title: '您的页面标题',
      path: 'pages/your-page/your-page',
      imageUrl: '/static/熙迈LOGO.png'
    });
  }
}
```

## 文件说明

- `utils/share.js` - 分享工具类，提供默认分享配置
- `App.vue` - 全局分享配置
- `manifest.json` - 小程序权限配置

## 分享效果

- **分享给好友**: 显示自定义标题、图片，点击后直接打开对应页面
- **分享到朋友圈**: 显示自定义标题和图片，朋友圈好友点击后打开小程序

## 注意事项

1. 分享图片建议使用小程序内的静态资源
2. 分享标题建议控制在30个字符以内
3. 确保分享路径在 `pages.json` 中已注册

现在您的小程序已经具备完整的微信原生分享功能！
