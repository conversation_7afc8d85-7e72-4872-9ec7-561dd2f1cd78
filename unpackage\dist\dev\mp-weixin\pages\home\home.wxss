
page.data-v-07e72d3c {
		font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Aria<PERSON>, sans-serif;
}
.logo-container.data-v-07e72d3c {
		padding: 20rpx 0 0 20rpx;
		margin-bottom: 20rpx;
		text-align: center;
}
.logo.data-v-07e72d3c {
		width: 500rpx;
		height: 80rpx;
		filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

	/* 调整原有容器间距 */
.container.data-v-07e72d3c {
		padding: 20rpx;
		padding-bottom: 240rpx;
		padding-top: 120rpx;
		background: linear-gradient(180deg, #f8f9ff 0%, #f0f2ff 100%);
		position: relative;
}
.container.data-v-07e72d3c {
		padding: 20rpx;
		padding-bottom: 240rpx;
		background: linear-gradient(180deg, #f8f9ff 0%, #f0f2ff 100%);
}
.main-button-group .button-item.data-v-07e72d3c {
		position: relative;
		overflow: hidden;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		box-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);
		border-radius: 24rpx;
		padding: 32rpx 0;
		transition: all 0.3s ease;
		animation: buttonFloat-07e72d3c 3s ease-in-out infinite;
}
.main-button-group .button-text.data-v-07e72d3c,
	.main-button-group .uni-icons.data-v-07e72d3c {
		color: #fff !important;
		z-index: 2;
		position: relative;
}

	/* 添加呼吸动画 */
@keyframes buttonFloat-07e72d3c {
0%,
		100% {
			transform: translateY(0);
}
50% {
			transform: translateY(-10rpx);
}
}

	/* 流光边框效果 */
.main-button-group .button-item.data-v-07e72d3c::after {
		content: '';
		position: absolute;
		top: -1rpx;
		left: -1rpx;
		right: -1rpx;
		bottom: -1rpx;
		background: linear-gradient(45deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.4) 50%,
				rgba(255, 255, 255, 0) 100%);
		animation: buttonShine-07e72d3c 3s infinite;
		z-index: 1;
}
@keyframes buttonShine-07e72d3c {
0% {
			opacity: 0;
			left: -50%;
}
50% {
			opacity: 0.4;
}
100% {
			opacity: 0;
			left: 150%;
}
}
.main-button-group .button-item.data-v-07e72d3c:hover {
		transform: scale(1.05);
		box-shadow: 0 16rpx 40rpx rgba(41, 121, 255, 0.4);
}

	/* 点击效果优化 */
.main-button-group .button-item.data-v-07e72d3c:active {
		transform: scale(0.98) translateY(2rpx);
		box-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);
}

	/* 公告栏 - 添加闪烁动画 */
.notice.data-v-07e72d3c {
		display: flex;
		align-items: center;
		padding: 20rpx;
		background-color: #fffbe6;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
}
.notice-swiper.data-v-07e72d3c {
		height: 40rpx;
		flex: 1;
		margin-left: 20rpx;
}
.notice-text.data-v-07e72d3c {
		font-size: 26rpx;
		color: #666;
}


	/* 按钮组 - 使用网格布局 */
.main-button-group.data-v-07e72d3c {
		display: grid;
		grid-template-columns: 1fr;
		gap: 20rpx;
		margin-bottom: 20rpx;
}
.sub-button-group.data-v-07e72d3c {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 20rpx;
		margin-bottom: 40rpx;
}
.button-item.data-v-07e72d3c {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: #fff;
		border-radius: 24rpx;
		padding: 32rpx 0;
		transition: all 0.3s ease;
		box-shadow: 0 6rpx 20rpx rgba(41, 121, 255, 0.1);
}
.button-item.data-v-07e72d3c:active {
		transform: scale(0.98);
		background: #f5f7ff;
}
.uni-icons.data-v-07e72d3c {
		margin-bottom: 16rpx;
}
.button-text-main.data-v-07e72d3c {
		font-size: 40rpx;
		color: #ffffff;
		font-weight: 500;
}
.button-text.data-v-07e72d3c {
		font-size: 26rpx;
		color: #333;
		font-weight: 500;
}

	/* 轮播图 - 优化指示器 */
.banner.data-v-07e72d3c {
		height: 320rpx;
		border-radius: 24rpx;
		overflow: hidden;
		margin-bottom: 24rpx;
		margin-top: 40rpx;
		position: relative;
}
.banner-image.data-v-07e72d3c {
		width: 100%;
		height: 100%;
		transition: transform 0.3s ease;
}
.banner swiper-item:hover .banner-image.data-v-07e72d3c {
		transform: scale(1.02);
}

	/* 联系我们 - 卡片式设计 */
.contact.data-v-07e72d3c {
		background: #fff;
		padding: 32rpx;
		border-radius: 24rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 20rpx;
}
.contact-title.data-v-07e72d3c {
		font-size: 34rpx;
		color: #1a1a1a;
		font-weight: 600;
		margin-bottom: 24rpx;
		position: relative;
		padding-left: 20rpx;
}
.contact-title.data-v-07e72d3c::before {
		content: "";
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 8rpx;
		height: 32rpx;
		background: #2979FF;
		border-radius: 4rpx;
}
.contact-info.data-v-07e72d3c {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: #666;
		margin-bottom: 16rpx;
		padding: 12rpx 0;
		border-bottom: 1rpx solid #eee;
}
.contact-info.data-v-07e72d3c:last-child {
		border-bottom: none;
}
.fixed-customer-service.data-v-07e72d3c {
		position: fixed;
		bottom: 40rpx;
		left: 50%;
		transform: translateX(-50%);
		z-index: 999;
		width: 700rpx;
}
.service-btn.data-v-07e72d3c {
		display: flex;
		align-items: center;
		padding: 10rpx;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		border-radius: 24rpx;
		box-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		-webkit-backdrop-filter: blur(10px);
		        backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
}
.text-container.data-v-07e72d3c {
		flex: 1;
}
.btn-text.data-v-07e72d3c {
		color: #fff;
		font-size: 38rpx;
		font-weight: 600;
		text-align: center;
		display: block;
		margin-bottom: 0;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.btn-subtext.data-v-07e72d3c {
		color: rgba(255, 255, 255, 0.9);
		font-size: 24rpx;
		line-height: 1.6;
		text-align: center;
		display: block;
		opacity: 0.9;
		margin-bottom: 16rpx;
}

	/* 交互动画 */
.service-btn.data-v-07e72d3c:active {
		transform: scale(0.98) translateY(2rpx);
		box-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);
}


	/* 悬浮呼吸动画 */
@keyframes float-07e72d3c {
0% {
			transform: translateY(0);
}
50% {
			transform: translateY(-10rpx);
}
100% {
			transform: translateY(0);
}
}
.service-btn.data-v-07e72d3c {
		animation: float-07e72d3c 3s ease-in-out infinite;
}

	/* 流光边框效果 */
.service-btn.data-v-07e72d3c::after {
		content: '';
		position: absolute;
		top: -1rpx;
		left: -1rpx;
		right: -1rpx;
		bottom: -1rpx;
		background: linear-gradient(45deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.3) 50%,
				rgba(255, 255, 255, 0) 100%);
		border-radius: 24rpx;
		animation: shine-07e72d3c 3s infinite;
		z-index: -1;
}
@keyframes shine-07e72d3c {
0% {
			opacity: 0;
			left: -50%;
}
50% {
			opacity: 0.4;
}
100% {
			opacity: 0;
			left: 150%;
}
}
.service-btn.data-v-07e72d3c {
		display: flex;
		align-items: center;
		padding: 10rpx;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		border-radius: 24rpx;
		box-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		-webkit-backdrop-filter: blur(10px);
		        backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
}
.text-container.data-v-07e72d3c {
		flex: 1;
}
.btn-text.data-v-07e72d3c {
		color: #fff;
		font-size: 38rpx;
		font-weight: 600;
		text-align: center;
		display: block;
		margin-bottom: 0;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.btn-subtext.data-v-07e72d3c {
		color: rgba(255, 255, 255, 0.9);
		font-size: 24rpx;
		line-height: 1.6;
		text-align: center;
		display: block;
		opacity: 0.9;
		margin-bottom: 16rpx;
}

	/* 交互动画 */
.service-btn.data-v-07e72d3c:active {
		transform: scale(0.98) translateY(2rpx);
		box-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);
}


	/* 悬浮呼吸动画 */
@keyframes float-07e72d3c {
0% {
			transform: translateY(0);
}
50% {
			transform: translateY(-10rpx);
}
100% {
			transform: translateY(0);
}
}
.service-btn.data-v-07e72d3c {
		animation: float-07e72d3c 3s ease-in-out infinite;
}

	/* 流光边框效果 */
.service-btn.data-v-07e72d3c::after {
		content: '';
		position: absolute;
		top: -1rpx;
		left: -1rpx;
		right: -1rpx;
		bottom: -1rpx;
		background: linear-gradient(45deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.3) 50%,
				rgba(255, 255, 255, 0) 100%);
		border-radius: 24rpx;
		animation: shine-07e72d3c 3s infinite;
		z-index: -1;
}
@keyframes shine-07e72d3c {
0% {
			opacity: 0;
			left: -50%;
}
50% {
			opacity: 0.4;
}
100% {
			opacity: 0;
			left: 150%;
}
}

	/* 弹窗样式 - 符合页面风格 */
.popup-overlay.data-v-07e72d3c {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(180deg, rgba(41, 121, 255, 0.1) 0%, rgba(0, 180, 255, 0.1) 100%);
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		animation: fadeIn-07e72d3c 0.3s ease-out;
}
.popup-content.data-v-07e72d3c {
		position: relative;
		background: linear-gradient(180deg, #ffffff 0%, #f8f9ff 100%);
		border-radius: 24rpx;
		max-width: 640rpx;
		width: 90%;
		height: 55vh;
		margin: 40rpx;
		overflow: hidden;
		box-shadow: 0 20rpx 60rpx rgba(41, 121, 255, 0.2);
		border: 1rpx solid rgba(41, 121, 255, 0.1);
		animation: popupSlideIn-07e72d3c 0.3s ease-out;
}
.popup-close.data-v-07e72d3c {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		width: 60rpx;
		height: 60rpx;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;
		box-shadow: 0 8rpx 24rpx rgba(41, 121, 255, 0.3);
		transition: all 0.3s ease;
}
.popup-close.data-v-07e72d3c:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.4);
}
.close-icon.data-v-07e72d3c {
		color: #fff;
		font-size: 40rpx;
		font-weight: bold;
		line-height: 1;
}

	/* 轮播图样式 */
.popup-swiper.data-v-07e72d3c {
		width: 100%;
		height: 100%;
}
.popup-swiper-item.data-v-07e72d3c {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
}
.popup-image.data-v-07e72d3c {
		width: 100%;
		flex: 1;
		display: block;
		object-fit: contain;
		background: linear-gradient(180deg, #f8f9ff 0%, #f0f2ff 100%);
		border-radius: 20rpx 20rpx 0 0;
}
.popup-title.data-v-07e72d3c {
		padding: 20rpx 30rpx;
		font-size: 26rpx;
		font-weight: 600;
		text-align: center;
		line-height: 1.4;
		background: linear-gradient(135deg, rgba(41, 121, 255, 0.9), rgba(0, 180, 255, 0.9));
		color: #fff;
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 2;
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
		border-top: 1rpx solid rgba(255, 255, 255, 0.2);
}

	/* 轮播图指示器 */
.popup-indicator.data-v-07e72d3c {
		position: absolute;
		top: 20rpx;
		left: 20rpx;
		background: linear-gradient(135deg, rgba(41, 121, 255, 0.9), rgba(0, 180, 255, 0.9));
		color: #fff;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		z-index: 3;
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.2);
}
.popup-no-image.data-v-07e72d3c {
		width: 100%;
		height: 400rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f5f5f5;
		border: 2rpx dashed #ddd;
}
.no-image-text.data-v-07e72d3c {
		color: #999;
		font-size: 28rpx;
}
.debug-info.data-v-07e72d3c {
		margin-top: 20rpx;
		padding: 20rpx;
		background-color: #f0f0f0;
		border-radius: 8rpx;
		font-size: 24rpx;
		color: #666;
}
@keyframes fadeIn-07e72d3c {
from {
			opacity: 0;
}
to {
			opacity: 1;
}
}
@keyframes popupSlideIn-07e72d3c {
from {
			transform: scale(0.8) translateY(-50rpx);
			opacity: 0;
}
to {
			transform: scale(1) translateY(0);
			opacity: 1;
}
}
.button-text-row.data-v-07e72d3c {
		display: flex;
		align-items: center;
		justify-content: center;
}
.hand-icon.data-v-07e72d3c {
		width: 36rpx;
		height: 36rpx;
		margin-left: 12rpx;
		display: inline-block;
		vertical-align: middle;
}
