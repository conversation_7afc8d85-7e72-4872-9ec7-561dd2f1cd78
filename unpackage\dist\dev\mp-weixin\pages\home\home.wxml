<view class="container data-v-07e72d3c"><view class="logo-container data-v-07e72d3c"><image class="logo data-v-07e72d3c" src="{{a}}" mode="aspectFit"></image></view><view class="main-button-group data-v-07e72d3c"><view wx:for="{{b}}" wx:for-item="item" wx:key="d" class="button-item data-v-07e72d3c" bindtap="{{item.e}}"><uni-icons wx:if="{{item.b}}" class="data-v-07e72d3c" u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}"></uni-icons><view class="button-text-row data-v-07e72d3c"><text class="button-text-main data-v-07e72d3c">{{item.c}}</text><image src="{{c}}" class="hand-icon data-v-07e72d3c" mode="widthFix"/></view></view></view><view class="sub-button-group data-v-07e72d3c"><view wx:for="{{d}}" wx:for-item="item" wx:key="d" class="button-item data-v-07e72d3c" bindtap="{{item.e}}"><uni-icons wx:if="{{item.b}}" class="data-v-07e72d3c" u-i="{{item.a}}" bind:__l="__l" u-p="{{item.b}}"></uni-icons><text class="button-text data-v-07e72d3c">{{item.c}}</text></view></view><swiper class="banner data-v-07e72d3c" circular autoplay interval="5000" indicator-dots="true" indicator-color="#007aff"><swiper-item wx:for="{{e}}" wx:for-item="item" wx:key="c" class="data-v-07e72d3c"><image src="{{item.a}}" mode="aspectFill" class="banner-image data-v-07e72d3c" bindtap="{{item.b}}"></image></swiper-item></swiper><view class="notice data-v-07e72d3c"><uni-icons wx:if="{{f}}" class="data-v-07e72d3c" u-i="07e72d3c-2" bind:__l="__l" u-p="{{f}}"></uni-icons><swiper class="notice-swiper data-v-07e72d3c" vertical autoplay circular interval="3000"><swiper-item wx:for="{{g}}" wx:for-item="item" wx:key="b" class="data-v-07e72d3c"><text class="notice-text data-v-07e72d3c">{{item.a}}</text></swiper-item></swiper></view><view wx:for="{{h}}" wx:for-item="info" wx:key="f" class="contact data-v-07e72d3c"><text class="contact-title data-v-07e72d3c">{{info.a}}</text><text wx:for="{{info.b}}" wx:for-item="phone" wx:key="c" class="contact-info data-v-07e72d3c" bindtap="{{phone.d}}"> 电话{{phone.a}}：{{phone.b}}</text><text class="contact-info data-v-07e72d3c" bindtap="{{info.d}}">地址：{{info.c}}</text><text class="contact-info data-v-07e72d3c">邮箱：{{info.e}}</text></view></view><view class="fixed-customer-service data-v-07e72d3c"><button class="service-btn data-v-07e72d3c" bindtap="{{i}}"><view class="text-container data-v-07e72d3c"><text class="btn-text data-v-07e72d3c">微信客服</text><text class="btn-subtext data-v-07e72d3c">如有需求，请点我联系</text></view></button></view><view wx:if="{{j}}" class="popup-overlay data-v-07e72d3c" bindtap="{{s}}"><view class="popup-content data-v-07e72d3c" catchtap="{{r}}"><view class="popup-close data-v-07e72d3c" bindtap="{{k}}"><text class="close-icon data-v-07e72d3c">×</text></view><swiper class="popup-swiper data-v-07e72d3c" indicator-dots="{{m}}" autoplay="{{false}}" circular="{{true}}" indicator-color="rgba(41, 121, 255, 0.3)" indicator-active-color="#2979FF" bindchange="{{n}}"><swiper-item wx:for="{{l}}" wx:for-item="popup" wx:key="f" class="popup-swiper-item data-v-07e72d3c"><image src="{{popup.a}}" class="popup-image data-v-07e72d3c" mode="aspectFit" bindtap="{{popup.b}}" bindload="{{popup.c}}"/><view wx:if="{{popup.d}}" class="popup-title data-v-07e72d3c">{{popup.e}}</view></swiper-item></swiper><view wx:if="{{o}}" class="popup-indicator data-v-07e72d3c">{{p}} / {{q}}</view></view></view>