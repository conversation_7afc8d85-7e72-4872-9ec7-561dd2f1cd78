{"version": 3, "file": "ShareButton.js", "sources": ["components/ShareButton/ShareButton.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDov5bCP56iL5bqP6aG555uuL-eGmei_iOmXqOaIty9YTU1IL2NvbXBvbmVudHMvU2hhcmVCdXR0b24vU2hhcmVCdXR0b24udnVl"], "sourcesContent": ["<template>\n\t<view class=\"share-button-container\">\n\t\t<!-- 分享按钮 -->\n\t\t<view class=\"fixed-share-btn\" v-if=\"showButton\">\n\t\t\t<button class=\"share-btn\" @click=\"showSharePanel\">\n\t\t\t\t<uni-icons type=\"redo\" size=\"20\" color=\"#fff\"></uni-icons>\n\t\t\t\t<text class=\"share-text\">分享</text>\n\t\t\t</button>\n\t\t</view>\n\t\t\n\t\t<!-- 分享面板 -->\n\t\t<view class=\"share-panel\" v-if=\"showPanel\" @click=\"hidePanel\">\n\t\t\t<view class=\"panel-content\" @click.stop>\n\t\t\t\t<view class=\"panel-header\">\n\t\t\t\t\t<text class=\"panel-title\">分享到</text>\n\t\t\t\t\t<view class=\"close-btn\" @click=\"hidePanel\">\n\t\t\t\t\t\t<uni-icons type=\"close\" size=\"20\" color=\"#666\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"share-options\">\n\t\t\t\t\t<view class=\"share-option\" @click=\"shareToFriend\">\n\t\t\t\t\t\t<view class=\"option-icon friend-icon\">\n\t\t\t\t\t\t\t<uni-icons type=\"chat\" size=\"24\" color=\"#fff\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"option-text\">微信好友</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"share-option\" @click=\"generateLink\">\n\t\t\t\t\t\t<view class=\"option-icon link-icon\">\n\t\t\t\t\t\t\t<uni-icons type=\"link\" size=\"24\" color=\"#fff\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"option-text\">生成链接</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"share-option\" @click=\"copyPath\">\n\t\t\t\t\t\t<view class=\"option-icon copy-icon\">\n\t\t\t\t\t\t\t<uni-icons type=\"paperplane\" size=\"24\" color=\"#fff\"></uni-icons>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"option-text\">复制路径</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport ShareUtils from '@/utils/share.js';\n\t\n\texport default {\n\t\tname: 'ShareButton',\n\t\tprops: {\n\t\t\t// 是否显示分享按钮\n\t\t\tshowButton: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: true\n\t\t\t},\n\t\t\t// 分享配置\n\t\t\tshareConfig: {\n\t\t\t\ttype: Object,\n\t\t\t\tdefault: () => ({})\n\t\t\t},\n\t\t\t// 按钮位置\n\t\t\tposition: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'right' // right, left, center\n\t\t\t},\n\t\t\t// 按钮样式\n\t\t\tbuttonStyle: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: 'circle' // circle, square\n\t\t\t}\n\t\t},\n\t\t\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tshowPanel: false\n\t\t\t};\n\t\t},\n\t\t\n\t\tcomputed: {\n\t\t\tcurrentShareConfig() {\n\t\t\t\tconst currentPages = getCurrentPages();\n\t\t\t\tconst currentPage = currentPages[currentPages.length - 1];\n\t\t\t\tconst currentPath = currentPage.route;\n\t\t\t\tconst currentOptions = currentPage.options;\n\t\t\t\t\n\t\t\t\t// 构建当前页面的完整路径\n\t\t\t\tconst queryString = Object.keys(currentOptions)\n\t\t\t\t\t.map(key => `${key}=${encodeURIComponent(currentOptions[key])}`)\n\t\t\t\t\t.join('&');\n\t\t\t\tconst fullPath = queryString ? `${currentPath}?${queryString}` : currentPath;\n\t\t\t\t\n\t\t\t\treturn ShareUtils.getDefaultShareConfig({\n\t\t\t\t\tpath: fullPath,\n\t\t\t\t\t...this.shareConfig\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\tmethods: {\n\t\t\tshowSharePanel() {\n\t\t\t\tthis.showPanel = true;\n\t\t\t},\n\t\t\t\n\t\t\thidePanel() {\n\t\t\t\tthis.showPanel = false;\n\t\t\t},\n\t\t\t\n\t\t\tshareToFriend() {\n\t\t\t\tthis.hidePanel();\n\t\t\t\tShareUtils.shareToFriend(this.currentShareConfig);\n\t\t\t},\n\t\t\t\n\t\t\tasync generateLink() {\n\t\t\t\tthis.hidePanel();\n\t\t\t\twx.showLoading({\n\t\t\t\t\ttitle: '生成链接中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst urlScheme = await ShareUtils.generateUrlScheme({\n\t\t\t\t\t\tpath: this.currentShareConfig.path\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\twx.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\twx.showModal({\n\t\t\t\t\t\ttitle: '分享链接',\n\t\t\t\t\t\tcontent: `链接已生成，是否复制到剪贴板？\\n\\n${urlScheme}`,\n\t\t\t\t\t\tconfirmText: '复制',\n\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tShareUtils.copyToClipboard(urlScheme, '分享链接已复制');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\twx.hideLoading();\n\t\t\t\t\twx.showToast({\n\t\t\t\t\t\ttitle: '生成链接失败',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tcopyPath() {\n\t\t\t\tthis.hidePanel();\n\t\t\t\tconst link = `小程序页面：${this.currentShareConfig.path}`;\n\t\t\t\tShareUtils.copyToClipboard(link, '页面路径已复制');\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style scoped>\n\t.share-button-container {\n\t\tposition: relative;\n\t}\n\t\n\t/* 分享按钮样式 */\n\t.fixed-share-btn {\n\t\tposition: fixed;\n\t\tbottom: 140rpx;\n\t\tright: 40rpx;\n\t\tz-index: 999;\n\t}\n\t\n\t.share-btn {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\twidth: 120rpx;\n\t\theight: 120rpx;\n\t\tbackground: linear-gradient(135deg, #FF6B6B, #FF8E8E);\n\t\tborder-radius: 60rpx;\n\t\tbox-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);\n\t\ttransition: all 0.3s ease;\n\t\tborder: none;\n\t\tpadding: 0;\n\t}\n\t\n\t.share-btn:active {\n\t\ttransform: scale(0.95);\n\t\tbox-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.2);\n\t}\n\t\n\t.share-text {\n\t\tcolor: #fff;\n\t\tfont-size: 22rpx;\n\t\tmargin-top: 8rpx;\n\t\tfont-weight: 500;\n\t}\n\t\n\t/* 分享面板样式 */\n\t.share-panel {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\tright: 0;\n\t\tbottom: 0;\n\t\tbackground: rgba(0, 0, 0, 0.5);\n\t\tz-index: 9999;\n\t\tdisplay: flex;\n\t\talign-items: flex-end;\n\t\tjustify-content: center;\n\t}\n\t\n\t.panel-content {\n\t\tbackground: #fff;\n\t\tborder-radius: 24rpx 24rpx 0 0;\n\t\twidth: 100%;\n\t\tmax-width: 750rpx;\n\t\tpadding: 40rpx;\n\t\tanimation: slideUp 0.3s ease;\n\t}\n\t\n\t@keyframes slideUp {\n\t\tfrom {\n\t\t\ttransform: translateY(100%);\n\t\t}\n\t\tto {\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\t\n\t.panel-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 40rpx;\n\t}\n\t\n\t.panel-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t}\n\t\n\t.close-btn {\n\t\tpadding: 10rpx;\n\t}\n\t\n\t.share-options {\n\t\tdisplay: flex;\n\t\tjustify-content: space-around;\n\t\talign-items: center;\n\t}\n\t\n\t.share-option {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tpadding: 20rpx;\n\t}\n\t\n\t.option-icon {\n\t\twidth: 100rpx;\n\t\theight: 100rpx;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-bottom: 16rpx;\n\t}\n\t\n\t.friend-icon {\n\t\tbackground: linear-gradient(135deg, #07C160, #38D973);\n\t}\n\t\n\t.link-icon {\n\t\tbackground: linear-gradient(135deg, #2979FF, #00B4FF);\n\t}\n\t\n\t.copy-icon {\n\t\tbackground: linear-gradient(135deg, #FF9500, #FFB340);\n\t}\n\t\n\t.option-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n</style>\n", "import Component from 'D:/小程序项目/熙迈门户/XMMH/components/ShareButton/ShareButton.vue'\nwx.createComponent(Component)"], "names": ["ShareUtils", "wx"], "mappings": ";;;AAkDC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,OAAO;AAAA;AAAA,IAEN,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,OAAO,CAAA;AAAA,IAChB;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA;AAAA,IACT;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS;AAAA;AAAA,IACV;AAAA,EACA;AAAA,EAED,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA;EAEZ;AAAA,EAED,UAAU;AAAA,IACT,qBAAqB;AACpB,YAAM,eAAe;AACrB,YAAM,cAAc,aAAa,aAAa,SAAS,CAAC;AACxD,YAAM,cAAc,YAAY;AAChC,YAAM,iBAAiB,YAAY;AAGnC,YAAM,cAAc,OAAO,KAAK,cAAc,EAC5C,IAAI,SAAO,GAAG,GAAG,IAAI,mBAAmB,eAAe,GAAG,CAAC,CAAC,EAAE,EAC9D,KAAK,GAAG;AACV,YAAM,WAAW,cAAc,GAAG,WAAW,IAAI,WAAW,KAAK;AAEjE,aAAOA,YAAAA,WAAW,sBAAsB;AAAA,QACvC,MAAM;AAAA,QACN,GAAG,KAAK;AAAA,MACT,CAAC;AAAA,IACF;AAAA,EACA;AAAA,EAED,SAAS;AAAA,IACR,iBAAiB;AAChB,WAAK,YAAY;AAAA,IACjB;AAAA,IAED,YAAY;AACX,WAAK,YAAY;AAAA,IACjB;AAAA,IAED,gBAAgB;AACf,WAAK,UAAS;AACdA,kBAAAA,WAAW,cAAc,KAAK,kBAAkB;AAAA,IAChD;AAAA,IAED,MAAM,eAAe;AACpB,WAAK,UAAS;AACdC,oBAAAA,KAAG,YAAY;AAAA,QACd,OAAO;AAAA,MACR,CAAC;AAED,UAAI;AACH,cAAM,YAAY,MAAMD,YAAU,WAAC,kBAAkB;AAAA,UACpD,MAAM,KAAK,mBAAmB;AAAA,QAC/B,CAAC;AAEDC,sBAAE,KAAC,YAAW;AAEdA,sBAAAA,KAAG,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA;AAAA,EAAsB,SAAS;AAAA,UACxC,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAChBD,0BAAAA,WAAW,gBAAgB,WAAW,SAAS;AAAA,YAChD;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACA,SAAO,OAAO;AACfC,sBAAE,KAAC,YAAW;AACdA,sBAAAA,KAAG,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,WAAW;AACV,WAAK,UAAS;AACd,YAAM,OAAO,SAAS,KAAK,mBAAmB,IAAI;AAClDD,kBAAAA,WAAW,gBAAgB,MAAM,SAAS;AAAA,IAC3C;AAAA,EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxJF,GAAG,gBAAgB,SAAS;"}