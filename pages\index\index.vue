<template>
	<view class="container">
		<!-- 广告弹窗 -->
		<view v-if="showAdPopup" class="ad-popup-overlay" @click="closeAdPopup">
			<view class="ad-popup-content" @click.stop>
				<view class="ad-popup-close" @click="closeAdPopup">✕</view>
				<view class="ad-popup-title" v-if="adData.title">{{ adData.title }}</view>
				<image v-if="adData.imageUrl" :src="adData.imageUrl" class="ad-popup-image" @click="handleAdClick" mode="aspectFit"></image>
				<view class="ad-popup-button" @click="handleAdClick">
					{{ adData.buttonText || '立即查看' }}
				</view>
			</view>
		</view>

		<view class="search-box">
			<input v-model="searchKeyword" placeholder="请输入您感兴趣的服务" class="search-input" @confirm="handleSearch" />
			<button class="search-btn" @click="handleSearch">搜索</button>
		</view>
		<view class="category-list">
			<view v-for="(item, index) in categories" :key="index" class="category-item"
				@click="handleCategoryClick(item)">
				<view class="img">
					<image class="icon" :src="item.imageUrl || '/static/熙迈LOGO.png'" mode="heightFix"></image>
				</view>
				<view class="banner">
					<view class="banner-left">
						<view class="top-blue">
							<text class="english-name">
								{{ item.englishName }}
								<span style="display:inline-block;width:16rpx;"></span>
								<text class="more-text" style="margin-left: 12rpx;">更多&gt;&gt;</text>
							</text>
						</view>
						<view class="bottom-white">
							<view class="logo">
								<image v-if="item.YWLX === '代理'" class="icon"
									:src="'/static/代理图标.png'" mode="aspectFit"></image>
								<image v-else class="icon"
									:src="'/static/熙迈LOGO.png'" mode="aspectFit"></image>
							</view>
							<view class="bottom-white-text">{{item.productName}}</view>
							<view class="like-section" @click.stop="handleLike(item)">
								<image class="like-icon" :src="item.isLiked ? '/static/红色小红心.svg' : '/static/灰色小红心.svg'"></image>
								<view v-if="item.showHeart" class="pop-animation">
									<text class="pop-heart">❤️</text>
									<text class="pop-count">{{ item.likeCount }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="fixed-customer-service">
			<button class="service-btn"  @click="handleContact" >
				<view class="text-container">
					<text class="btn-text">微信客服</text>
					<text class="btn-subtext">如有需求，请点我联系</text>
				</view>
			</button>
		</view>



		<view v-if="categories.length === 0" class="no-result">
			<text class="no-result-icon">&#9785;</text>
			<text class="no-result-text">没有找到相关产品</text>
			<text class="no-result-subtext">尝试更改搜索关键词或稍后再试</text>
		</view>
	</view>


</template>

<script>
	import {
		FIELD_MAPPING,
		PRODUCT_MANAGER_SERVICE_LINKS
	} from '@/config/fields.js';
	import {
		objectToParams
	} from '@/utils/url.js';
	import ShareUtils from '@/utils/share.js';

	export default {
		data() {
			return {
				searchKeyword: '',
				categories: [],
				likesLog: {},
				// 广告弹窗相关
				showAdPopup: false,
				adData: {}
			}
		},
		onLoad() {
			this.getCategories();
			this.getAdPopupData();
		},
		onShow() {
			this.updateLikesStatus();
		},

		// 分享功能
		onShareAppMessage(res) {
			console.log('分享给好友', res);
			return ShareUtils.getDefaultShareConfig({
				title: '熙迈科技工业服务 - 专业产品展示',
				path: 'pages/index/index',
				imageUrl: '/static/熙迈LOGO.png'
			});
		},

		onShareTimeline(res) {
			console.log('分享到朋友圈', res);
			return ShareUtils.getDefaultShareConfig({
				title: '熙迈科技工业服务 - 专业产品展示',
				path: 'pages/index/index',
				imageUrl: '/static/熙迈LOGO.png'
			});
		},

		methods: {
			// 获取广告弹窗数据
			async getAdPopupData() {
				try {
					const requestData = {
						appKey: "984e1ff028f80150",
						sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
						worksheetId: "xcxdcwh",
						viewId: "",
						pageSize: 1,
						pageIndex: 1,
						listType: 0,
						controls: [],
						filters: [
							{
								controlId: "6870869ba849420e13f654c3", // isActive字段
								dataType: 36,
								spliceType: 1,
								filterType: 2,
								value: "1" // 只获取启用的弹窗
							}
						]
					};

					const response = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',
						method: 'POST',
						data: requestData,
						header: {
							'Content-Type': 'application/json'
						}
					});

					if (response.data && response.data.data && response.data.data.rows && response.data.data.rows.length > 0) {
						const adRow = response.data.data.rows[0];
						this.adData = {
							id: adRow['6870869ba849420e13f654bf'], // id
							title: adRow['687085d1a849420e13f654b5'], // title
							jumpUrl: adRow['6870869ba849420e13f654c0'], // jumpUrl
							imageUrl: this.getImageUrl(adRow['687085d1a849420e13f654b7']), // imageUrl
							startDate: adRow['6870869ba849420e13f654c1'], // startDate
							endDate: adRow['6870869ba849420e13f654c2'], // endDate
							isActive: adRow['6870869ba849420e13f654c3'] // isActive
						};

						// 检查是否在显示时间范围内
						if (this.shouldShowPopup()) {
							this.showAdPopup = true;
						}
					}
				} catch (error) {
					console.error('获取广告弹窗数据失败:', error);
				}
			},

			// 处理图片URL（附件类型字段）
			getImageUrl(attachmentData) {
				if (!attachmentData) return '';
				try {
					const parsed = typeof attachmentData === 'string' ? JSON.parse(attachmentData) : attachmentData;
					if (parsed && parsed.length > 0 && parsed[0].previewUrl) {
						return parsed[0].previewUrl;
					}
				} catch (e) {
					console.error('解析图片数据失败:', e);
				}
				return '';
			},

			// 判断是否应该显示弹窗
			shouldShowPopup() {
				if (!this.adData.isActive) return false;

				const today = new Date();
				const todayStr = today.toISOString().split('T')[0];

				// 检查日期范围
				if (this.adData.startDate && todayStr < this.adData.startDate) return false;
				if (this.adData.endDate && todayStr > this.adData.endDate) return false;

				// 检查今日是否已显示过
				const lastShown = uni.getStorageSync('adPopupLastShown');
				if (lastShown === todayStr) return false;

				return true;
			},

			// 关闭广告弹窗
			closeAdPopup() {
				this.showAdPopup = false;
				// 记录今日已显示
				const today = new Date().toISOString().split('T')[0];
				uni.setStorageSync('adPopupLastShown', today);
			},

			// 处理广告点击
			handleAdClick() {
				if (this.adData.jumpUrl) {
					// 记录今日已显示
					const today = new Date().toISOString().split('T')[0];
					uni.setStorageSync('adPopupLastShown', today);

					// 关闭弹窗
					this.showAdPopup = false;

					// 跳转页面
					uni.navigateTo({
						url: this.adData.jumpUrl,
						fail: (err) => {
							console.error('页面跳转失败:', err);
							uni.showToast({
								title: '页面跳转失败',
								icon: 'none'
							});
						}
					});
				}
			},

			updateLikesStatus() {
				const likesLog = uni.getStorageSync('likes_log') || {};
				this.likesLog = likesLog;
				if (this.categories.length > 0) {
					this.categories.forEach(item => {
						if (likesLog[item.rowId]) {
							this.$set(item, 'isLiked', true);
						} else {
							this.$set(item, 'isLiked', false);
						}
						
						if (item.children && item.children.length > 0) {
							item.children.forEach(child => {
								if (likesLog[child.rowId]) {
									this.$set(child, 'isLiked', true);
								} else {
									this.$set(child, 'isLiked', false);
								}
							});
						}
					});
				}
			},
			handleLike(item) {
				// 1. Toggle the UI state immediately (Optimistic Update)
				item.isLiked = !item.isLiked;
				
				let webhookAction = '';
			
				if (item.isLiked) {
					// --- UI & Local State Update for LIKE ---
					item.likeCount++;
					webhookAction = 'increment';
					this.$set(this.likesLog, item.rowId, true);

					// Trigger animation
					this.$set(item, 'showHeart', true);
					setTimeout(() => {
						this.$set(item, 'showHeart', false);
					}, 600);
				} else {
					// --- UI & Local State Update for UNLIKE ---
					item.likeCount--;
					webhookAction = 'decrement';
					this.$delete(this.likesLog, item.rowId);
				}
			
				// Update local storage for persistence across sessions
				uni.setStorageSync('likes_log', this.likesLog);
			
				// 2. Send the corresponding command to the webhook
				uni.request({
					url: 'https://dmit.duoningbio.com/api/workflow/hooks/Njg2Y2ZhNDk3MDMwNzAyNDViNDMxOTcx',
					method: 'POST',
					header: {
						'Content-Type': 'application/json'
					},
					data: {
						action: webhookAction,
						id: item.rowId
					},
					success: (res) => {
						console.log(`Webhook '${webhookAction}' for rowid ${item.rowId} sent successfully. Response:`, res.data);
					},
					fail: (err) => {
						console.error(`Webhook '${webhookAction}' for rowid ${item.rowId} failed:`, err);
					}
				});
			},
			formatAPIData(row) {
				const formattedItem = {
					rowId: row.rowid || '',
					children: [],
					likeCount: parseInt(row['DZS']) || 0,
					isLiked: false,
					showHeart: false
				};
				Object.keys(FIELD_MAPPING).forEach(key => {
					const apiFieldId = FIELD_MAPPING[key];
					formattedItem[key] = row[apiFieldId] || '';
				});

				// Ensure caseStudiesCount is parsed as a number
				formattedItem.caseStudiesCount = parseInt(row[FIELD_MAPPING.caseStudiesCount]) || 0;

				// 添加图片URL处理
				if (formattedItem.imageUrl || row.imageUrl) {
					formattedItem.imageUrl = this.getImageUrl(formattedItem.imageUrl || row.imageUrl);
				}

				// 打印解析后的数据
				// console.log('解析后的数据:', formattedItem);

				return formattedItem;
			},
			async handleSearch() {
				if (!this.searchKeyword.trim()) {
					uni.showToast({
						title: '请输入搜索关键词',
						icon: 'none'
					});
					return;
				}

				try {
					const requestData = {
						appKey: '984e1ff028f80150',
						sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
						worksheetId: 'fenlei',
						viewId: '',
						pageSize: 50,
						pageIndex: 1,
						listType: 0,
						controls: [],
						filters: [{
								controlId: '67b2da03ef727a4cd047da1b',
								dataType: 2,
								spliceType: 2,
								filterType: 1,
								value: this.searchKeyword
							},
							{
								controlId: '67b2dd25ef727a4cd047da2a',
								dataType: 2,
								spliceType: 2,
								filterType: 1,
								value: this.searchKeyword
							}
						]
					};

					const response = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',
						method: 'POST',
						data: requestData,
						header: {
							'Content-Type': 'application/json'
						}
					});

					if (response.statusCode !== 200) {
						console.error('请求失败:', response);
						uni.showToast({
							title: '请求失败',
							icon: 'none'
						});
						return;
					}

					if (response.data && response.data.data) {
						if (response.data.data.rows && response.data.data.rows.length > 0) {
							const formattedData = response.data.data.rows.map(row => this.formatAPIData(row));
							console.log('Formatted Data:', formattedData);
							uni.navigateTo({
								url: `/pages/category/category?title=搜索结果&children=${encodeURIComponent(JSON.stringify(formattedData))}`
							});
						} else if (response.data.data.total === 0 || response.data.error_code === 1) {
							uni.showToast({
								title: '没有找到相关产品',
								icon: 'none',
								duration: 2000
							});
						}
					} else {
						console.error('接口返回数据格式异常:', response.data);
						uni.showToast({
							title: '数据格式异常',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取分类失败:', error);
					uni.showToast({
						title: '获取分类失败',
						icon: 'none'
					});
				}
			},
			async getCategories() {
				try {
					const response = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',
						method: 'POST',
						data: {
							appKey: '984e1ff028f80150',
							sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
							worksheetId: 'fenlei',
							viewId: '',
							pageSize: 50,
							pageIndex: 1,
							listType: 0,
							controls: [],
							filters: [{
								controlId: '67b2dd3aef727a4cd047da36',
								dataType: 29,
								spliceType: 1,
								filterType: 7
							}],
							sortId: '67b2dd25ef727a4cd047da2a'
						},
						header: {
							'Content-Type': 'application/json'
						}
					});

					console.log('API Response:', response);

					if (response.statusCode !== 200) {
						console.error('请求失败:', response);
						uni.showToast({
							title: '请求失败',
							icon: 'none'
						});
						return;
					}

					if (response.data && response.data.data && response.data.data.rows) {
						console.log('Raw Data:', response.data.data.rows);
						this.categories = response.data.data.rows.map(row => this.formatAPIData(row));
						// Sort by like count descending on initial load
						this.categories.sort((a, b) => b.likeCount - a.likeCount);
						// console.log('Formatted Categories:', this.categories);

					} else {
						console.error('接口返回数据格式异常:', response.data);
						uni.showToast({
							title: '数据格式异常',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取分类失败:', error);
					uni.showToast({
						title: '获取分类失败',
						icon: 'none'
					});
				}
			},
			getImageUrl(imageData) {
				try {
					// 支持直接传入数组或JSON字符串
					const parsedData = Array.isArray(imageData) ? imageData : JSON.parse(imageData);

					if (!Array.isArray(parsedData)) {
						console.warn("图片数据格式错误");
						return '/static/熙迈LOGO.png'; // 返回默认图
					}

					// 查找第一个有效URL（优先fileUrl，其次thumbnail_full_path）
					const targetItem = parsedData.find(item =>
						item.fileUrl?.startsWith('http') ||
						item.thumbnail_full_path?.startsWith('http')
					);

					// 返回优先级：fileUrl > thumbnail_full_path > 默认图
					return targetItem?.fileUrl ||
						targetItem?.thumbnail_full_path ||
						'/static/熙迈LOGO.png';

				} catch (error) {
					console.error("解析失败:", error.message);
					return '/static/熙迈LOGO.png'; // 始终返回字符串
				}
			},
			async handleCategoryClick(item) {
				let recommendations = [];
				let parentMaterialsWithDownloadUrl = [];

				try {
					// 检查是否有相关资料需要获取完整信息
					const hasIntroductionMaterials = item.introductionMaterials && item.introductionMaterials.length > 0;

					// 如果有相关资料，先调用 getFilterRows 获取包含 DownloadUrl 的完整信息
					if (hasIntroductionMaterials) {
						console.log('检测到相关资料，获取完整附件信息...');
						try {
							const materialResponse = await uni.request({
								url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',
								method: 'POST',
								data: {
									appKey: '984e1ff028f80150',
									sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
									worksheetId: 'fenlei',
									pageSize: 1,
									pageIndex: 1,
									listType: 0,
									controls: [],
									filters: [{
										controlId: 'rowid',
										dataType: 2,
										spliceType: 1,
										filterType: 2,
										value: item.rowId
									}]
								},
								header: {
									'Content-Type': 'application/json'
								}
							});

							if (materialResponse.data && materialResponse.data.data && materialResponse.data.data.rows && materialResponse.data.data.rows.length > 0) {
								const productData = materialResponse.data.data.rows[0];
								if (productData.DLPP_JSZL) {
									const materialsData = JSON.parse(productData.DLPP_JSZL);
									console.log('原始附件数据:', materialsData);
									parentMaterialsWithDownloadUrl = materialsData.map(material => {
										console.log('处理单个附件:', material);
										return {
											// 使用正确的字段名
											name: material.original_file_name || '未知文件',
											url: material.DownloadUrl || material.original_file_full_path || '',
											size: material.file_size || 0,
											// 保留完整的字段信息（与 form 页面一致）
											DownloadUrl: material.DownloadUrl || '',
											original_file_full_path: material.original_file_full_path || '',
											original_file_name: material.original_file_name || '未知文件',
											file_size: material.file_size || 0
										};
									});
									console.log('映射后的附件信息:', parentMaterialsWithDownloadUrl);
								}
							}
						} catch (error) {
							console.error('获取完整附件信息失败:', error);
							// 如果获取失败，使用原有的资料信息
							parentMaterialsWithDownloadUrl = item.introductionMaterials || [];
						}
					}

					const subCategoryResponse = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',
						method: 'POST',
						data: {
							appKey: '984e1ff028f80150',
							sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
							worksheetId: 'fenlei',
							pageSize: 50,
							pageIndex: 1,
							rowId: item.rowId,
							controlId: '67b2dd3aef727a4cd047da37',
							getSystemControl: false
						},
						header: {
							'Content-Type': 'application/json'
						}
					});
					const recommendResponse = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',
						method: 'POST',
						data: {
							appKey: '984e1ff028f80150',
							sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
							worksheetId: 'fenlei',
							pageSize: 50,
							pageIndex: 1,
							rowId: item.rowId,
							controlId: 'GLCP',
							getSystemControl: false
						},
						header: {
							'Content-Type': 'application/json'
						}
					});

					if (subCategoryResponse.statusCode !== 200) {
						console.error('请求子分类失败:', response);
						uni.showToast({
							title: '请求子分类失败',
							icon: 'none'
						});
						return;
					}
					

					if (!subCategoryResponse.data || !subCategoryResponse.data.data) {
						console.error('接口返回数据格式异常:', subCategoryResponse.data);
						uni.showToast({
							title: '数据格式异常',
							icon: 'none'
						});
						return;
					}

					if (!subCategoryResponse.data.data.rows || subCategoryResponse.data.data.rows.length === 0) {
						recommendations = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));

						let caseStudies = [];
						if (item.caseStudiesCount > 0) {
							try {
								const caseStudiesResponse = await uni.request({
									url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',
									method: 'POST',
									data: {
										appKey: '984e1ff028f80150',
										sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
										worksheetId: 'fenlei',
										pageSize: 50,
										pageIndex: 1,
										rowId: item.rowId,
										controlId: 'ALQK',
										getSystemControl: false
									},
									header: {
										'Content-Type': 'application/json'
									}
								});
								if (caseStudiesResponse.data && caseStudiesResponse.data.data && caseStudiesResponse.data.data.rows) {
									caseStudies = caseStudiesResponse.data.data.rows.map(row => ({
										clientName: row[FIELD_MAPPING.caseClientName] || '',
										details: row[FIELD_MAPPING.caseDetails] || ''
									}));
								}
							} catch (e) {
								console.error('获取案例情况失败:', e);
							}
						}

						// 正常页面跳转：传递基本数据，但不包含推荐产品（避免URL过长）
						const formUrl = `/pages/form/form?${objectToParams(item)}&caseStudies=${encodeURIComponent(JSON.stringify(caseStudies))}`;

						console.log('=== Index页面跳转到Form页面 ===');
						console.log('完整的item对象:', item);
						console.log('跳转的产品信息:', {
							productName: item.productName,
							productCode: item.productCode,
							serviceDescription: item.serviceDescription,
							productManager: item.productManager,
							caseStudiesCount: caseStudies.length
						});
						console.log('构建的URL:', formUrl);
						console.log('跳转URL长度:', formUrl.length);

						// 检查URL长度，如果仍然过长则使用简化模式
						if (formUrl.length > 1500) { // 提高阈值，减少使用简化模式的情况
							console.log('⚠️ URL过长（' + formUrl.length + '字符），使用简化模式');
							const simpleUrl = item.productCode ?
								`/pages/form/form?productCode=${encodeURIComponent(item.productCode)}&shareMode=1` :
								`/pages/form/form?productName=${encodeURIComponent(item.productName)}&shareMode=1`;
							uni.navigateTo({
								url: simpleUrl
							});
						} else {
							console.log('✅ URL长度正常（' + formUrl.length + '字符），使用完整数据传递');
							uni.navigateTo({
								url: formUrl
							});
						}
						return;
					}

					const formattedData = subCategoryResponse.data.data.rows.map(row => this.formatAPIData(row));
					recommendations = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));

					// 传递父级分类的介绍资料（使用包含 DownloadUrl 的完整信息）
					const parentMaterials = parentMaterialsWithDownloadUrl.length > 0 ? parentMaterialsWithDownloadUrl : (item.introductionMaterials || []);
					console.log('Index页面传递父级介绍资料:', parentMaterials);

					// 传递父级分类的相关链接
					const parentRelatedLinks = item.relatedLinks || [];
					console.log('Index页面传递父级相关链接:', parentRelatedLinks);

					// 由于URL长度限制，改为只传递核心参数，让category页面重新获取数据
					const categoryUrl = `/pages/category/category?title=${encodeURIComponent(item.productName)}&pm=${encodeURIComponent(item.productManager)}&parentImage=${encodeURIComponent(item.imageUrl || '/static/熙迈LOGO.png')}&parentContactPhone=${encodeURIComponent(item.contactPhone || '')}&parentMaterials=${encodeURIComponent(JSON.stringify(parentMaterials))}&parentRelatedLinks=${encodeURIComponent(parentRelatedLinks.join(','))}&shareMode=1`;

					console.log('=== Index页面跳转到Category页面 ===');
					console.log('跳转的产品信息:', {
						productName: item.productName,
						productManager: item.productManager,
						imageUrl: item.imageUrl,
						formattedDataCount: formattedData.length,
						recommendationsCount: recommendations.length
					});
					console.log('跳转URL:', categoryUrl);
					console.log('URL长度:', categoryUrl.length);
					console.log('=== 开始跳转（使用分享模式避免URL过长）===');

					uni.navigateTo({
						url: categoryUrl
					});
				} catch (error) {
					console.error('获取子分类失败:', error);
					uni.showToast({
						title: '获取子分类失败',
						icon: 'none'
					});
				}
			}
		}
	}
</script>

<style>
	/* 广告弹窗样式 */
	.ad-popup-overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
	}

	.ad-popup-content {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 40rpx;
		margin: 40rpx;
		max-width: 600rpx;
		width: 80%;
		position: relative;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
	}

	.ad-popup-close {
		position: absolute;
		top: 20rpx;
		right: 30rpx;
		font-size: 40rpx;
		color: #999;
		cursor: pointer;
		z-index: 10000;
	}

	.ad-popup-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		text-align: center;
		margin-bottom: 30rpx;
	}

	.ad-popup-image {
		width: 100%;
		max-height: 400rpx;
		border-radius: 10rpx;
		margin-bottom: 30rpx;
	}

	.ad-popup-button {
		background-color: #5a7fb8;
		color: #fff;
		text-align: center;
		padding: 20rpx 40rpx;
		border-radius: 50rpx;
		font-size: 28rpx;
		font-weight: bold;
		cursor: pointer;
	}

	.ad-popup-button:active {
		background-color: #4a6fa8;
	}

	.search-box {
		padding: 20rpx;
		display: flex;
		gap: 20rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #eee;
	}

	.search-input {
		flex: 1;
		height: 70rpx;
		padding: 0 20rpx;
		border: 1rpx solid #ddd;
		border-radius: 35rpx;
	}

	.search-btn {
		width: 140rpx;
		height: 70rpx;
		line-height: 70rpx;
		border-radius: 35rpx;
		background-color: #007AFF;
		color: white;
	}

	.container {
		position: relative;
		background: #42f3f933;
		animation: bg-flow 25s linear infinite;
		min-height: 100vh;
		padding: 0;
		padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
	}

	/* 提升内容层级 */
	.search-box,
	.category-list,
	.no-result {
		position: relative;
		z-index: 1;
	}

	.category-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 24rpx;
	}
	
	.category-item {
		width: calc(50% - 12rpx);
		margin-bottom: 24rpx;
		background-color: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
		overflow: hidden;
		position: relative;
	}
	
	.img {
		width: 100%;
		height: 250rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;
	}
	
	.img .icon {
		height: 100%;
		width: 100%;
		display: block;
		object-fit: contain;
	}
	
	.banner {
		padding: 10rpx;
	}
	
	.banner-left {
		width: 100%;
	}
	
	.top-blue {
		height: 60rpx;
		width: 100%;
		border-radius: 15rpx;
		background-color: #6d92cc;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 8rpx 12rpx;
		box-sizing: border-box;
	}
	
	.english-name {
		color: #ffffff;
		font-size: 16rpx;
		text-align: center;
		line-height: 1.4;
		word-wrap: break-word;
		word-break: break-all;
		width: 100%;
	}
	
	.bottom-white {
		color: #6d92cc;
		min-height: 80rpx;
		width: 100%;
		background-color: #fff;
		display: flex;
		align-items: center;
		padding: 10rpx;
		box-sizing: border-box;
	}
	
	.bottom-white-text {
		flex: 1;
		min-width: 0;
		font-size: 22rpx;
		text-align: center;
		white-space: normal;
		line-height: 1.4;
		margin: 0 8rpx;
	}
	
	.logo {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		width: 70rpx;
		height: 70rpx;
	}
	
	.logo .icon {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
		padding: 0;
	}
	
	.like-section {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 10rpx;
		flex-shrink: 0;
	}
	
	.like-icon {
		width: 40rpx;
		height: 40rpx;
	}

	.red-heart-animation {
		position: absolute;
		right: 12rpx;
		bottom: 30rpx;
		font-size: 30rpx;
		animation: pop-heart 0.6s ease-out forwards;
		pointer-events: none;
	}
	
	.pop-animation {
		position: absolute;
		bottom: 100%; /* 从按钮上方开始 */
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		flex-direction: column;
		animation: pop-up 0.8s ease-out forwards;
		pointer-events: none;
	}
	
	.pop-heart {
		font-size: 30rpx;
	}
	
	.pop-count {
		font-size: 24rpx;
		color: #ff6a6a;
		font-weight: bold;
	}

	@keyframes pop-up {
		0% {
			transform: translateX(-50%) scale(0.5);
			opacity: 0;
			bottom: 100%;
		}
		50% {
			transform: translateX(-50%) scale(1.2);
			opacity: 1;
		}
		100% {
			transform: translateX(-50%) scale(1);
			opacity: 0;
			bottom: 150%; /* 向上飘动 */
		}
	}

	@keyframes pop-heart {
		0% {
			transform: scale(0.5);
			opacity: 0;
		}

		50% {
			transform: scale(1.6);
			opacity: 1;
		}

		100% {
			transform: scale(1);
			opacity: 0;
		}
	}

	.no-result {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
		margin: 40rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		text-align: center;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}

	.no-result-icon {
		font-size: 100rpx;
		color: #e0e0e0;
		margin-bottom: 20rpx;
	}

	.no-result-text {
		font-size: 32rpx;
		color: #666;
		margin-bottom: 10rpx;
	}

	.no-result-subtext {
		font-size: 24rpx;
		color: #999;
	}

	.service-btn {
		display: flex;
		align-items: center;
		padding: 10rpx;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		border-radius: 24rpx;
		box-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
	}

	.text-container {
		flex: 1;
	}

	.btn-text {
		color: #fff;
		font-size: 38rpx;
		font-weight: 600;
		text-align: center;
		display: block;
		margin-bottom: 0;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.btn-subtext {
		color: rgba(255, 255, 255, 0.9);
		font-size: 24rpx;
		line-height: 1.6;
		text-align: center;
		display: block;
		opacity: 0.9;
		margin-bottom: 16rpx;
	}

	/* 交互动画 */
	.service-btn:active {
		transform: scale(0.98) translateY(2rpx);
		box-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);
	}


	/* 悬浮呼吸动画 */
	@keyframes float {
		0% {
			transform: translateY(0);
		}

		50% {
			transform: translateY(-10rpx);
		}

		100% {
			transform: translateY(0);
		}
	}

	.service-btn {
		animation: float 3s ease-in-out infinite;
	}

	/* 流光边框效果 */
	.service-btn::after {
		content: '';
		position: absolute;
		top: -1rpx;
		left: -1rpx;
		right: -1rpx;
		bottom: -1rpx;
		background: linear-gradient(45deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.3) 50%,
				rgba(255, 255, 255, 0) 100%);
		border-radius: 24rpx;
		animation: shine 3s infinite;
		z-index: -1;
	}

	@keyframes shine {
		0% {
			opacity: 0;
			left: -50%;
		}

		50% {
			opacity: 0.4;
		}

		100% {
			opacity: 0;
			left: 150%;
		}
	}

</style>