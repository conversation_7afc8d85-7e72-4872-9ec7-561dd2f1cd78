{"version": 3, "file": "WebView.js", "sources": ["pages/WebView/WebView.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvV2ViVmlldy9XZWJWaWV3LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 加载状态 -->\n\t\t<view v-if=\"loading\" class=\"loading-container\">\n\t\t\t<view class=\"loading-spinner\"></view>\n\t\t\t<text class=\"loading-text\">正在加载...</text>\n\t\t</view>\n\n\t\t<!-- Web-view 组件 -->\n\t\t<web-view\n\t\t\t:src=\"url\"\n\t\t\t@message=\"getMessage\"\n\t\t\t@load=\"onWebViewLoad\"\n\t\t\t@error=\"onWebViewError\"\n\t\t></web-view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\turl: '',\n\t\t\t\tloading: true,\n\t\t\t\ttitle: '相关链接'\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tconsole.log('WebView页面参数:', options);\n\n\t\t\t// 获取URL参数\n\t\t\tthis.url = decodeURIComponent(options.url || '');\n\t\t\tthis.title = decodeURIComponent(options.title || '相关链接');\n\n\t\t\tif (!this.url) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '错误',\n\t\t\t\t\tcontent: '缺少链接地址',\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tconfirmText: '返回',\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 设置导航栏标题\n\t\t\tuni.setNavigationBarTitle({\n\t\t\t\ttitle: this.title\n\t\t\t});\n\n\t\t\tconsole.log('即将加载链接:', this.url);\n\t\t},\n\t\tmethods: {\n\t\t\t// Web-view 加载完成\n\t\t\tonWebViewLoad(e) {\n\t\t\t\tconsole.log('Web-view 加载完成:', e);\n\t\t\t\tthis.loading = false;\n\t\t\t},\n\n\t\t\t// Web-view 加载错误\n\t\t\tonWebViewError(e) {\n\t\t\t\tconsole.error('Web-view 加载错误:', e);\n\t\t\t\tthis.loading = false;\n\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '加载失败',\n\t\t\t\t\tcontent: '页面加载失败，是否复制链接到剪贴板？',\n\t\t\t\t\tconfirmText: '复制链接',\n\t\t\t\t\tcancelText: '返回',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\t\t\tdata: this.url,\n\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\ttitle: '链接已复制',\n\t\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t// 接收 Web-view 消息\n\t\t\tgetMessage(e) {\n\t\t\t\tconsole.log('收到 Web-view 消息:', e);\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\twidth: 100%;\n\t\theight: 100vh;\n\t\tposition: relative;\n\t}\n\n\t.loading-container {\n\t\tposition: absolute;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackground-color: #fff;\n\t\tz-index: 1000;\n\t}\n\n\t.loading-spinner {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t\tborder: 3rpx solid #f3f3f3;\n\t\tborder-top: 3rpx solid #2979FF;\n\t\tborder-radius: 50%;\n\t\tanimation: spin 1s linear infinite;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.loading-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n\n\t@keyframes spin {\n\t\t0% { transform: rotate(0deg); }\n\t\t100% { transform: rotate(360deg); }\n\t}\n</style>\n", "import MiniProgramPage from 'D:/小程序项目/熙迈门户/XMMH/pages/WebView/WebView.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAmBC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,KAAK;AAAA,MACL,SAAS;AAAA,MACT,OAAO;AAAA,IACR;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACfA,kBAAA,MAAA,MAAA,OAAA,mCAAY,gBAAgB,OAAO;AAGnC,SAAK,MAAM,mBAAmB,QAAQ,OAAO,EAAE;AAC/C,SAAK,QAAQ,mBAAmB,QAAQ,SAAS,MAAM;AAEvD,QAAI,CAAC,KAAK,KAAK;AACdA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,MAAM;AACdA,wBAAG,MAAC,aAAY;AAAA,QACjB;AAAA,MACD,CAAC;AACD;AAAA,IACD;AAGAA,kBAAAA,MAAI,sBAAsB;AAAA,MACzB,OAAO,KAAK;AAAA,IACb,CAAC;AAEDA,kBAAY,MAAA,MAAA,OAAA,mCAAA,WAAW,KAAK,GAAG;AAAA,EAC/B;AAAA,EACD,SAAS;AAAA;AAAA,IAER,cAAc,GAAG;AAChBA,oBAAY,MAAA,MAAA,OAAA,mCAAA,kBAAkB,CAAC;AAC/B,WAAK,UAAU;AAAA,IACf;AAAA;AAAA,IAGD,eAAe,GAAG;AACjBA,oBAAc,MAAA,MAAA,SAAA,mCAAA,kBAAkB,CAAC;AACjC,WAAK,UAAU;AAEfA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChBA,0BAAAA,MAAI,iBAAiB;AAAA,cACpB,MAAM,KAAK;AAAA,cACX,SAAS,MAAM;AACdA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,gBACP,CAAC;AACD,2BAAW,MAAM;AAChBA,gCAAG,MAAC,aAAY;AAAA,gBAChB,GAAE,IAAI;AAAA,cACR;AAAA,YACD,CAAC;AAAA,iBACK;AACNA,0BAAG,MAAC,aAAY;AAAA,UACjB;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,WAAW,GAAG;AACbA,oBAAY,MAAA,MAAA,OAAA,mCAAA,mBAAmB,CAAC;AAAA,IACjC;AAAA,EACD;AACD;;;;;;;;;;;;AChGD,GAAG,WAAW,eAAe;"}