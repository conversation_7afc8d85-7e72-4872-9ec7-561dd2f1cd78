{"version": 3, "file": "WebView.js", "sources": ["pages/WebView/WebView.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvV2ViVmlldy9XZWJWaWV3LnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<web-view :src=\"url\"></web-view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\turl: ''\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\tthis.url = decodeURIComponent(options.url || '');\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\twidth: 100%;\n\t\theight: 100%;\n\t}\n</style>\n", "import MiniProgramPage from 'D:/小程序项目/熙迈门户/XMMH/pages/WebView/WebView.vue'\nwx.createPage(MiniProgramPage)"], "names": [], "mappings": ";;AAOC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,KAAK;AAAA,IACN;AAAA,EACA;AAAA,EACD,OAAO,SAAS;AACf,SAAK,MAAM,mBAAmB,QAAQ,OAAO,EAAE;AAAA,EAChD;AACD;;;;;;;ACfD,GAAG,WAAW,eAAe;"}