"use strict";
const common_vendor = require("../../common/vendor.js");
const config_fields = require("../../config/fields.js");
const utils_share = require("../../utils/share.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      contactPhone: "",
      productName: "",
      productCode: "",
      productManager: "",
      contactInfo: "",
      serviceDescription: "",
      imageUrl: "",
      highResImageUrl: "",
      YWLX: "",
      agentBrandImage: "",
      DLPPGW: "",
      chanpingtu: "",
      englishName: "",
      DLPPLOGO: "",
      recommendations: [],
      likesLog: {},
      // 新增点赞记录
      caseStudies: [],
      // 新增案例情况
      introductionMaterials: [],
      // 介绍资料
      relatedLinks: [],
      // 相关视频号/公众号链接
      showMaterialsModal: false,
      // 控制资料弹窗显示
      rowid: "",
      // 当前产品的rowid
      showVideoModal: false,
      // 控制视频预览弹窗显示
      currentVideoUrl: "",
      // 当前预览的视频URL
      currentVideoName: ""
      // 当前预览的视频文件名
    };
  },
  async onLoad(options) {
    if (options.shareMode === "1") {
      this.productName = decodeURIComponent(options.productName || "");
      this.productCode = decodeURIComponent(options.productCode || "");
      this.rowid = decodeURIComponent(options.rowid || "");
      if (!this.productName && !this.productCode) {
        common_vendor.index.__f__("log", "at pages/form/form.vue:243", "❌ 缺少必要的产品信息");
        common_vendor.index.showModal({
          title: "提示",
          content: "分享链接中缺少产品信息，可能是因为产品名称过长导致的。请从产品列表重新进入此页面。",
          showCancel: false,
          confirmText: "返回首页",
          success: () => {
            common_vendor.index.switchTab({
              url: "/pages/home/<USER>"
            });
          }
        });
        return;
      }
      this.loadDataFromAPI();
    } else {
      common_vendor.index.__f__("log", "at pages/form/form.vue:260", "📝 正常模式，解析URL参数中的完整数据");
      this.serviceDescription = decodeURIComponent(options.serviceDescription || "");
      this.productName = decodeURIComponent(options.productName || "");
      this.productCode = decodeURIComponent(options.productCode || "");
      this.productManager = decodeURIComponent(options.productManager || "");
      this.contactInfo = decodeURIComponent(options.contactInfo || "");
      this.YWLX = decodeURIComponent(options.YWLX || "");
      this.contactPhone = decodeURIComponent(options.contactPhone || "");
      this.agentBrandImage = decodeURIComponent(options.agentBrandImage || "");
      this.DLPPGW = decodeURIComponent(options.DLPPGW || "");
      this.chanpingtu = decodeURIComponent(options.chanpingtu || "");
      this.englishName = decodeURIComponent(options.englishName || "");
      this.DLPPLOGO = decodeURIComponent(options.DLPPLOGO || "");
      if (this.contactPhone.startsWith("+86")) {
        this.contactPhone = this.contactPhone.substring(3);
      }
      try {
        this.recommendations = JSON.parse(decodeURIComponent(options.recommendations || "[]"));
        this.recommendations.sort((a, b) => b.likeCount - a.likeCount);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/form/form.vue:282", "解析推荐数据失败:", error);
      }
      try {
        this.caseStudies = JSON.parse(decodeURIComponent(options.caseStudies || "[]"));
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/form/form.vue:288", "解析案例情况数据失败:", error);
      }
      try {
        this.introductionMaterials = JSON.parse(decodeURIComponent(options.introductionMaterials || "[]"));
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/form/form.vue:294", "解析介绍资料数据失败:", error);
      }
    }
    try {
      const imageData = decodeURIComponent(options.imageUrl || "");
      const chanpingtuData = decodeURIComponent(options.chanpingtu || "");
      if (imageData.startsWith("http")) {
        this.imageUrl = imageData;
      } else {
        this.imageUrl = this.getImageUrl(imageData);
      }
      if (chanpingtuData) {
        if (chanpingtuData.startsWith("http")) {
          this.highResImageUrl = chanpingtuData;
        } else {
          this.highResImageUrl = this.getImageUrl(chanpingtuData);
        }
      } else {
        this.highResImageUrl = this.imageUrl;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at pages/form/form.vue:320", "解析图片URL失败:", error);
      this.imageUrl = "/static/熙迈LOGO.png";
      this.highResImageUrl = this.imageUrl;
    }
    try {
      const dlppData = decodeURIComponent(options.agentBrandImage || "");
      if (dlppData.startsWith("/static/") || dlppData.startsWith("http://") || dlppData.startsWith("https://")) {
        this.agentBrandImage = dlppData;
      } else {
        const dlppArray = JSON.parse(dlppData || "[]");
        if (dlppArray.length > 0 && dlppArray[0].fileUrl) {
          this.agentBrandImage = dlppArray[0].fileUrl.startsWith("http") ? dlppArray[0].fileUrl : `https://${dlppArray[0].fileUrl}`;
        } else {
          this.agentBrandImage = "/static/代理图标.png";
        }
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at pages/form/form.vue:342", "解析代理品牌图片失败:", error);
      this.agentBrandImage = "/static/代理图标.png";
    }
    if (this.recommendations.length === 0) {
      await this.loadRecommendationsAndCases();
    }
  },
  // 分享功能
  onShareAppMessage(res) {
    common_vendor.index.__f__("log", "at pages/form/form.vue:356", "=== Form页面分享给好友 ===");
    common_vendor.index.__f__("log", "at pages/form/form.vue:357", "分享触发参数:", res);
    common_vendor.index.__f__("log", "at pages/form/form.vue:358", "当前页面数据:", {
      productName: this.productName,
      productCode: this.productCode,
      productManager: this.productManager,
      imageUrl: this.imageUrl,
      recommendationsCount: this.recommendations.length
    });
    const shareTitle = this.productName ? `${this.productName} - 熙迈科技` : "熙迈科技产品详情";
    let sharePath;
    if (this.productCode) {
      sharePath = `pages/form/form?productCode=${encodeURIComponent(this.productCode)}&shareMode=1`;
    } else if (this.productName) {
      sharePath = `pages/form/form?productName=${encodeURIComponent(this.productName)}&shareMode=1`;
    } else {
      sharePath = `pages/form/form?shareMode=1`;
    }
    if (sharePath.length > 200) {
      common_vendor.index.__f__("log", "at pages/form/form.vue:383", "⚠️ 分享路径过长，进一步简化");
      if (this.productCode && this.productCode.length < 50) {
        sharePath = `pages/form/form?productCode=${encodeURIComponent(this.productCode)}&shareMode=1`;
      } else {
        sharePath = `pages/form/form?shareMode=1`;
        common_vendor.index.__f__("log", "at pages/form/form.vue:390", "⚠️ 产品信息过长，分享链接将无法包含产品信息");
      }
    }
    common_vendor.index.__f__("log", "at pages/form/form.vue:394", "构建的分享路径:", sharePath);
    common_vendor.index.__f__("log", "at pages/form/form.vue:395", "分享路径长度:", sharePath.length);
    const shareConfig = utils_share.ShareUtils.getDefaultShareConfig({
      title: shareTitle,
      path: sharePath,
      imageUrl: this.highResImageUrl || this.imageUrl || "/static/熙迈LOGO.png"
    });
    common_vendor.index.__f__("log", "at pages/form/form.vue:403", "最终分享配置:", shareConfig);
    common_vendor.index.__f__("log", "at pages/form/form.vue:404", "=== Form页面分享配置完成 ===");
    return shareConfig;
  },
  onShareTimeline(res) {
    common_vendor.index.__f__("log", "at pages/form/form.vue:410", "=== Form页面分享到朋友圈 ===");
    common_vendor.index.__f__("log", "at pages/form/form.vue:411", "分享触发参数:", res);
    const shareTitle = this.productName ? `${this.productName} - 熙迈科技` : "熙迈科技产品详情";
    let sharePath;
    if (this.productCode) {
      sharePath = `pages/form/form?productCode=${encodeURIComponent(this.productCode)}&shareMode=1`;
    } else if (this.productName) {
      sharePath = `pages/form/form?productName=${encodeURIComponent(this.productName)}&shareMode=1`;
    } else {
      sharePath = `pages/form/form?shareMode=1`;
    }
    if (sharePath.length > 200) {
      common_vendor.index.__f__("log", "at pages/form/form.vue:430", "⚠️ 分享路径过长，进一步简化");
      if (this.productCode && this.productCode.length < 50) {
        sharePath = `pages/form/form?productCode=${encodeURIComponent(this.productCode)}&shareMode=1`;
      } else {
        sharePath = `pages/form/form?shareMode=1`;
        common_vendor.index.__f__("log", "at pages/form/form.vue:437", "⚠️ 产品信息过长，分享链接将无法包含产品信息");
      }
    }
    common_vendor.index.__f__("log", "at pages/form/form.vue:441", "构建的分享路径:", sharePath);
    const shareConfig = utils_share.ShareUtils.getDefaultShareConfig({
      title: shareTitle,
      path: sharePath,
      imageUrl: this.highResImageUrl || this.imageUrl || "/static/熙迈LOGO.png"
    });
    common_vendor.index.__f__("log", "at pages/form/form.vue:449", "最终分享配置:", shareConfig);
    common_vendor.index.__f__("log", "at pages/form/form.vue:450", "=== Form页面朋友圈分享配置完成 ===");
    return shareConfig;
  },
  methods: {
    // 从API重新加载数据（用于分享模式）
    async loadDataFromAPI() {
      try {
        const filters = [];
        if (this.productCode) {
          filters.push({
            controlId: config_fields.FIELD_MAPPING.productCode,
            // 使用配置中的产品编码字段ID
            dataType: 2,
            spliceType: 1,
            filterType: 2,
            // 等于（完全匹配）
            value: this.productCode
          });
        } else if (this.productName) {
          filters.push({
            controlId: config_fields.FIELD_MAPPING.productName,
            // 使用配置中的产品名称字段ID
            dataType: 2,
            spliceType: 1,
            filterType: 2,
            // 等于（完全匹配）
            value: this.productName
          });
        }
        if (filters.length === 0) {
          common_vendor.index.__f__("log", "at pages/form/form.vue:489", "❌ 没有有效的搜索条件");
          common_vendor.index.showToast({
            title: "缺少产品信息",
            icon: "none"
          });
          return;
        }
        const requestData = {
          appKey: "984e1ff028f80150",
          sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
          worksheetId: "fenlei",
          pageSize: 1,
          pageIndex: 1,
          listType: 0,
          controls: [],
          filters
        };
        const response2 = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows",
          method: "POST",
          data: requestData,
          header: {
            "Content-Type": "application/json"
          }
        });
        if (response2.data && response2.data.data) {
        }
        if (response2.data && response2.data.data && response2.data.data.rows && response2.data.data.rows.length > 0) {
          const productData = response2.data.data.rows[0];
          const formattedData = this.formatAPIData(productData);
          this.productName = formattedData.productName || "";
          this.productCode = formattedData.productCode || this.productCode;
          this.serviceDescription = formattedData.serviceDescription || "";
          this.productManager = formattedData.productManager || "";
          this.contactInfo = formattedData.contactInfo || "";
          this.YWLX = formattedData.YWLX || "";
          this.contactPhone = formattedData.contactPhone || "";
          this.agentBrandImage = formattedData.agentBrandImage || "/static/代理图标.png";
          this.DLPPGW = formattedData.DLPPGW || "";
          this.chanpingtu = formattedData.chanpingtu || "";
          this.englishName = formattedData.englishName || "";
          this.DLPPLOGO = formattedData.DLPPLOGO || "";
          this.imageUrl = formattedData.imageUrl || "/static/熙迈LOGO.png";
          this.highResImageUrl = formattedData.chanpingtu || formattedData.imageUrl || "/static/熙迈LOGO.png";
          this.introductionMaterials = formattedData.introductionMaterials || [];
          this.rowid = formattedData.rowId || "";
          if (this.contactPhone.startsWith("+86")) {
            this.contactPhone = this.contactPhone.substring(3);
          }
          await this.loadRecommendationsAndCases();
        } else {
          common_vendor.index.__f__("log", "at pages/form/form.vue:557", "❌ 未找到匹配的产品数据");
          common_vendor.index.showToast({
            title: "未找到产品信息",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/form/form.vue:564", "重新加载数据失败:", error);
        common_vendor.index.showToast({
          title: "加载数据失败",
          icon: "none"
        });
      }
    },
    // 加载推荐数据和案例数据
    async loadRecommendationsAndCases() {
      try {
        if (this.rowid) {
          common_vendor.index.__f__("log", "at pages/form/form.vue:577", "使用rowid获取关联数据:", this.rowid);
          const recommendResponse = await common_vendor.index.request({
            url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations",
            method: "POST",
            data: {
              appKey: "984e1ff028f80150",
              sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
              worksheetId: "fenlei",
              rowId: this.rowid,
              controlId: "GLCP",
              // 推荐产品字段ID
              pageSize: 10,
              pageIndex: 1
            },
            header: {
              "Content-Type": "application/json"
            }
          });
          if (recommendResponse.data && recommendResponse.data.data && recommendResponse.data.data.rows) {
            this.recommendations = recommendResponse.data.data.rows.map((row) => this.formatAPIData(row));
            this.recommendations.sort((a, b) => b.likeCount - a.likeCount);
          }
          const caseStudiesResponse = await common_vendor.index.request({
            url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations",
            method: "POST",
            data: {
              appKey: "984e1ff028f80150",
              sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
              worksheetId: "fenlei",
              rowId: this.rowid,
              controlId: "ALQK",
              // 案例字段ID
              pageSize: 10,
              pageIndex: 1
            },
            header: {
              "Content-Type": "application/json"
            }
          });
          if (caseStudiesResponse.data && caseStudiesResponse.data.data && caseStudiesResponse.data.data.rows) {
            this.caseStudies = caseStudiesResponse.data.data.rows.map((row) => ({
              clientName: row[config_fields.FIELD_MAPPING.caseClientName] || "",
              details: row[config_fields.FIELD_MAPPING.caseDetails] || ""
            }));
            common_vendor.index.__f__("log", "at pages/form/form.vue:625", "✅ 获取到案例数据，数量:", this.caseStudies.length);
          } else {
            this.caseStudies = [];
          }
        } else {
          common_vendor.index.__f__("log", "at pages/form/form.vue:630", "没有rowid，使用通用推荐数据");
          const recommendResponse = await common_vendor.index.request({
            url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows",
            method: "POST",
            data: {
              appKey: "984e1ff028f80150",
              sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
              worksheetId: "fenlei",
              pageSize: 6,
              pageIndex: 1,
              listType: 0,
              controls: [],
              filters: []
            },
            header: {
              "Content-Type": "application/json"
            }
          });
          if (recommendResponse.data && recommendResponse.data.data && recommendResponse.data.data.rows) {
            const allItems = recommendResponse.data.data.rows.map((row) => this.formatAPIData(row));
            const shuffled = allItems.sort(() => 0.5 - Math.random());
            this.recommendations = shuffled.slice(0, 4);
            this.recommendations.sort((a, b) => b.likeCount - a.likeCount);
          }
          this.caseStudies = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/form/form.vue:663", "❌ 加载推荐数据和案例数据失败:", error);
        this.recommendations = [];
        this.caseStudies = [];
      }
    },
    handleLike(item) {
      item.isLiked = !item.isLiked;
      let webhookAction = "";
      if (item.isLiked) {
        item.likeCount++;
        webhookAction = "increment";
        this.$set(this.likesLog, item.rowId, true);
        this.$set(item, "showHeart", true);
        setTimeout(() => {
          this.$set(item, "showHeart", false);
        }, 600);
      } else {
        item.likeCount--;
        webhookAction = "decrement";
        this.$delete(this.likesLog, item.rowId);
      }
      common_vendor.index.setStorageSync("likes_log", this.likesLog);
      common_vendor.index.request({
        url: "https://dmit.duoningbio.com/api/workflow/hooks/Njg2Y2ZhNDk3MDMwNzAyNDViNDMxOTcx",
        method: "POST",
        header: {
          "Content-Type": "application/json"
        },
        data: {
          action: webhookAction,
          id: item.rowId
        },
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/form/form.vue:708", `Webhook '${webhookAction}' for rowid ${item.rowId} sent successfully. Response:`, res.data);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/form/form.vue:711", `Webhook '${webhookAction}' for rowid ${item.rowId} failed:`, err);
        }
      });
    },
    handleContact(pm) {
      let serviceLink = "https://work.weixin.qq.com/kfid/kfcaf7fbb93aa905a54";
      const managerLink = config_fields.PRODUCT_MANAGER_SERVICE_LINKS.find((item) => item.manager === pm);
      if (managerLink) {
        serviceLink = managerLink.serviceLink;
      }
      common_vendor.wx$1.openCustomerServiceChat({
        extInfo: {
          url: serviceLink
        },
        corpId: "wwa76e36d25343b6b9",
        success(res) {
        }
      });
    },
    formatAPIData(row) {
      const formattedItem = {
        rowId: row.rowid || "",
        likeCount: parseInt(row["DZS"]) || 0,
        isLiked: false,
        showHeart: false
      };
      Object.keys(config_fields.FIELD_MAPPING).forEach((key) => {
        const apiFieldId = config_fields.FIELD_MAPPING[key];
        formattedItem[key] = row[apiFieldId] || "";
      });
      if (formattedItem.imageUrl || row[config_fields.FIELD_MAPPING.imageUrl]) {
        formattedItem.imageUrl = this.getImageUrl(formattedItem.imageUrl || row[config_fields.FIELD_MAPPING.imageUrl]);
      }
      if (formattedItem.chanpingtu || row[config_fields.FIELD_MAPPING.chanpingtu]) {
        formattedItem.chanpingtu = this.getImageUrl(formattedItem.chanpingtu || row[config_fields.FIELD_MAPPING.chanpingtu]);
      }
      const agentBrandImageData = formattedItem.agentBrandImage || row[config_fields.FIELD_MAPPING.agentBrandImage];
      if (agentBrandImageData) {
        formattedItem.agentBrandImage = this.getImageUrl(agentBrandImageData);
      } else {
        formattedItem.agentBrandImage = "/static/代理图标.png";
      }
      const introMaterialsData = formattedItem.introductionMaterials || row[config_fields.FIELD_MAPPING.introductionMaterials];
      if (introMaterialsData) {
        try {
          formattedItem.introductionMaterials = Array.isArray(introMaterialsData) ? introMaterialsData : JSON.parse(introMaterialsData);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/form/form.vue:769", "解析介绍资料失败:", error);
          formattedItem.introductionMaterials = [];
        }
      } else {
        formattedItem.introductionMaterials = [];
      }
      return formattedItem;
    },
    getImageUrl(imageData) {
      try {
        if (typeof imageData === "string" && imageData.startsWith("http")) {
          let cleanUrl = imageData.includes("?") ? imageData.split("?")[0] : imageData;
          return cleanUrl;
        }
        if (!imageData) {
          return "/static/熙迈LOGO.png";
        }
        const parsedData = Array.isArray(imageData) ? imageData : JSON.parse(imageData);
        if (!Array.isArray(parsedData) || parsedData.length === 0) {
          common_vendor.index.__f__("warn", "at pages/form/form.vue:795", "图片数据格式错误或为空");
          return "/static/熙迈LOGO.png";
        }
        const imageItem = parsedData[0];
        let imageUrl = imageItem.large_thumbnail_full_path || imageItem.original_file_full_path || imageItem.thumbnail_full_path || imageItem.preview_url || "/static/熙迈LOGO.png";
        if (imageUrl && imageUrl.includes("?")) {
          imageUrl = imageUrl.split("?")[0];
        }
        return imageUrl;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/form/form.vue:817", "解析图片URL失败:", error.message, "原始数据:", imageData);
        return "/static/熙迈LOGO.png";
      }
    },
    async handleCategoryClick(item) {
      try {
        const subCategoryResponse = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations",
          method: "POST",
          data: {
            appKey: "984e1ff028f80150",
            sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
            worksheetId: "fenlei",
            pageSize: 50,
            pageIndex: 1,
            rowId: item.rowId,
            controlId: "67b2dd3aef727a4cd047da37",
            getSystemControl: false
          },
          header: {
            "Content-Type": "application/json"
          }
        });
        const recommendResponse = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations",
          method: "POST",
          data: {
            appKey: "984e1ff028f80150",
            sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
            worksheetId: "fenlei",
            pageSize: 50,
            pageIndex: 1,
            rowId: item.rowId,
            controlId: "GLCP",
            getSystemControl: false
          },
          header: {
            "Content-Type": "application/json"
          }
        });
        if (subCategoryResponse.statusCode !== 200) {
          common_vendor.index.__f__("error", "at pages/form/form.vue:860", "请求子分类失败:", response);
          common_vendor.index.showToast({
            title: "请求子分类失败",
            icon: "none"
          });
          return;
        }
        if (!subCategoryResponse.data || !subCategoryResponse.data.data) {
          common_vendor.index.__f__("error", "at pages/form/form.vue:870", "接口返回数据格式异常:", subCategoryResponse.data);
          common_vendor.index.showToast({
            title: "数据格式异常",
            icon: "none"
          });
          return;
        }
        if (!subCategoryResponse.data.data.rows || subCategoryResponse.data.data.rows.length === 0) {
          common_vendor.index.__f__("log", "at pages/form/form.vue:880", "=== Form页面跳转到Form页面 ===");
          common_vendor.index.__f__("log", "at pages/form/form.vue:881", "跳转的产品信息:", {
            productName: item.productName,
            productCode: item.productCode,
            productManager: item.productManager
          });
          this.generateShortLinkForProduct(item);
          return;
        }
        const categoryUrl = `/pages/category/category?title=${encodeURIComponent(item.productName)}&parentImage=${encodeURIComponent(item.imageUrl || this.imageUrl || "/static/熙迈LOGO.png")}&shareMode=1`;
        common_vendor.index.__f__("log", "at pages/form/form.vue:895", "=== Form页面跳转到Category页面（简化模式）===");
        common_vendor.index.__f__("log", "at pages/form/form.vue:896", "跳转URL:", categoryUrl);
        common_vendor.index.__f__("log", "at pages/form/form.vue:897", "URL长度:", categoryUrl.length);
        common_vendor.index.navigateTo({
          url: categoryUrl
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/form/form.vue:903", "获取子分类失败:", error);
        common_vendor.index.showToast({
          title: "获取子分类失败",
          icon: "none"
        });
      }
    },
    // 为产品生成短链接并跳转
    async generateShortLinkForProduct(item) {
      common_vendor.index.__f__("log", "at pages/form/form.vue:913", "=== 开始处理产品跳转 ===");
      common_vendor.index.__f__("log", "at pages/form/form.vue:914", "产品信息:", item);
      let targetUrl;
      if (item.productCode) {
        targetUrl = `/pages/form/form?productCode=${encodeURIComponent(item.productCode)}&shareMode=1`;
      } else if (item.productName) {
        targetUrl = `/pages/form/form?productName=${encodeURIComponent(item.productName)}&shareMode=1`;
      } else {
        targetUrl = `/pages/form/form?shareMode=1`;
      }
      common_vendor.index.__f__("log", "at pages/form/form.vue:926", "构建的跳转URL:", targetUrl);
      common_vendor.index.__f__("log", "at pages/form/form.vue:927", "URL长度:", targetUrl.length);
      try {
        common_vendor.index.__f__("log", "at pages/form/form.vue:930", "开始跳转...");
        common_vendor.index.navigateTo({
          url: targetUrl,
          success: (res) => {
            common_vendor.index.__f__("log", "at pages/form/form.vue:934", "跳转成功:", res);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/form/form.vue:937", "跳转失败:", err);
            common_vendor.index.showToast({
              title: "跳转失败",
              icon: "none"
            });
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/form/form.vue:945", "跳转异常:", error);
        common_vendor.index.showToast({
          title: "跳转异常",
          icon: "none"
        });
      }
    },
    copyUrl(url) {
      common_vendor.index.setClipboardData({
        data: url,
        success: () => {
          common_vendor.index.showToast({
            title: "网址已复制",
            icon: "none"
          });
        }
      });
    },
    openDLPPGW() {
      if (this.DLPPGW) {
        if (!this.DLPPGW.startsWith("http://") && !this.DLPPGW.startsWith("https://")) {
          this.DLPPGW = "https://" + this.DLPPGW;
        }
        common_vendor.index.navigateTo({
          url: `/pages/WebView/WebView?url=${encodeURIComponent(this.DLPPGW)}`
        });
      } else {
        common_vendor.index.showToast({
          title: "代理品牌官网链接为空",
          icon: "none"
        });
      }
    },
    previewImage() {
      common_vendor.index.__f__("log", "at pages/form/form.vue:982", "图片被点击了！");
      if (this.highResImageUrl) {
        common_vendor.index.previewImage({
          urls: [this.highResImageUrl]
          // 使用高清图片URL进行预览
        });
      } else {
        common_vendor.index.__f__("error", "at pages/form/form.vue:988", "图片URL为空");
      }
    },
    //拨打电话
    callnum(num) {
      common_vendor.index.makePhoneCall({
        phoneNumber: num
        //仅为示例
      });
    },
    previewAgentImage() {
      if (this.agentBrandImage) {
        common_vendor.index.previewImage({
          urls: [this.agentBrandImage]
        });
      }
    },
    // 显示资料列表
    showMaterialsList() {
      this.showMaterialsModal = true;
    },
    // 隐藏资料列表
    hideMaterialsList() {
      this.showMaterialsModal = false;
    },
    // 预览资料文件
    previewMaterial(material) {
      const downloadUrl = material.DownloadUrl;
      const originalUrl = material.original_file_full_path;
      const fileName = material.original_file_name;
      common_vendor.index.__f__("log", "at pages/form/form.vue:1019", "预览文件:", fileName);
      common_vendor.index.__f__("log", "at pages/form/form.vue:1020", "DownloadUrl:", downloadUrl);
      common_vendor.index.__f__("log", "at pages/form/form.vue:1021", "original_file_full_path:", originalUrl);
      const fileType = this.getFileType(fileName);
      common_vendor.index.__f__("log", "at pages/form/form.vue:1025", "文件类型:", fileType);
      if (fileType === "video") {
        this.previewVideo(downloadUrl || originalUrl, fileName);
      } else if (fileType === "image") {
        this.previewImage(downloadUrl || originalUrl, fileName);
      } else {
        this.previewWechatWithFallback(downloadUrl, originalUrl, fileName);
      }
    },
    // 带回退机制的预览方法
    previewWechatWithFallback(primaryUrl, fallbackUrl, fileName) {
      common_vendor.index.__f__("log", "at pages/form/form.vue:1041", "尝试预览文档:", fileName);
      common_vendor.index.__f__("log", "at pages/form/form.vue:1042", "主要URL:", primaryUrl);
      common_vendor.index.__f__("log", "at pages/form/form.vue:1043", "备用URL:", fallbackUrl);
      this.previewWechatSingle(primaryUrl, (success) => {
        if (!success && fallbackUrl && fallbackUrl !== primaryUrl) {
          common_vendor.index.__f__("log", "at pages/form/form.vue:1048", "主要URL失败，尝试备用URL");
          this.previewWechatSingle(fallbackUrl, (success2) => {
            if (!success2) {
              common_vendor.index.showModal({
                title: "预览失败",
                content: "文件无法预览，可能是文件格式不支持或网络问题。\n\n是否复制文件链接？",
                confirmText: "复制链接",
                cancelText: "取消",
                success: function(modalRes) {
                  if (modalRes.confirm) {
                    common_vendor.index.setClipboardData({
                      data: primaryUrl,
                      success: function() {
                        common_vendor.index.showToast({
                          title: "链接已复制",
                          icon: "success"
                        });
                      }
                    });
                  }
                }
              });
            }
          });
        }
      });
    },
    // 单次预览尝试
    previewWechatSingle(urlPdf, callback) {
      if (!urlPdf || typeof urlPdf !== "string") {
        common_vendor.index.__f__("error", "at pages/form/form.vue:1079", "无效的文档URL:", urlPdf);
        callback(false);
        return;
      }
      common_vendor.index.showLoading({
        title: "正在加载中.."
      });
      common_vendor.index.__f__("log", "at pages/form/form.vue:1088", "开始下载文件:", urlPdf);
      common_vendor.index.downloadFile({
        url: urlPdf,
        success: function(res) {
          common_vendor.index.__f__("log", "at pages/form/form.vue:1092", "文件下载成功:", res);
          var filePath = res.tempFilePath;
          if (!filePath) {
            common_vendor.index.__f__("error", "at pages/form/form.vue:1096", "下载成功但临时文件路径为空");
            common_vendor.index.hideLoading();
            callback(false);
            return;
          }
          common_vendor.index.__f__("log", "at pages/form/form.vue:1102", "准备打开文档:", filePath);
          common_vendor.index.openDocument({
            filePath,
            showMenu: false,
            success: function(res2) {
              common_vendor.index.__f__("log", "at pages/form/form.vue:1107", "打开文档成功:", res2);
              common_vendor.index.hideLoading();
              callback(true);
            },
            fail: function(err) {
              common_vendor.index.__f__("error", "at pages/form/form.vue:1112", "打开文档失败:", err);
              common_vendor.index.hideLoading();
              callback(false);
            }
          });
        },
        fail: function(err) {
          common_vendor.index.__f__("error", "at pages/form/form.vue:1119", "下载文件失败:", err);
          common_vendor.index.__f__("error", "at pages/form/form.vue:1120", "失败的URL:", urlPdf);
          common_vendor.index.hideLoading();
          callback(false);
        }
      });
    },
    // 微信小程序预览文档
    previewWechat(urlPdf) {
      common_vendor.index.__f__("log", "at pages/form/form.vue:1128", "准备预览文档，URL:", urlPdf);
      if (!urlPdf || typeof urlPdf !== "string") {
        common_vendor.index.__f__("error", "at pages/form/form.vue:1132", "无效的文档URL:", urlPdf);
        common_vendor.index.showToast({
          title: "文档链接无效",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "正在加载中.."
      });
      common_vendor.index.__f__("log", "at pages/form/form.vue:1144", "开始下载文件:", urlPdf);
      common_vendor.index.downloadFile({
        url: urlPdf,
        success: function(res) {
          common_vendor.index.__f__("log", "at pages/form/form.vue:1148", "文件下载成功:", res);
          var filePath = res.tempFilePath;
          if (!filePath) {
            common_vendor.index.__f__("error", "at pages/form/form.vue:1152", "下载成功但临时文件路径为空");
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "文件路径错误",
              icon: "none"
            });
            return;
          }
          common_vendor.index.__f__("log", "at pages/form/form.vue:1161", "准备打开文档:", filePath);
          common_vendor.index.openDocument({
            filePath,
            showMenu: false,
            // 禁用菜单，防止下载
            success: function(res2) {
              common_vendor.index.__f__("log", "at pages/form/form.vue:1166", "打开文档成功:", res2);
              common_vendor.index.hideLoading();
            },
            fail: function(err) {
              common_vendor.index.__f__("error", "at pages/form/form.vue:1170", "打开文档失败:", err);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "文档格式不支持或文件损坏",
                icon: "none"
              });
            }
          });
        },
        fail: function(err) {
          common_vendor.index.__f__("error", "at pages/form/form.vue:1180", "下载文件失败:", err);
          common_vendor.index.__f__("error", "at pages/form/form.vue:1181", "失败的URL:", urlPdf);
          common_vendor.index.hideLoading();
          let errorMsg = "文件加载失败";
          if (err.errMsg && err.errMsg.includes("ENOENT")) {
            errorMsg = "文件不存在或已被删除";
          } else if (err.errMsg && err.errMsg.includes("network")) {
            errorMsg = "网络连接失败，请检查网络";
          } else if (err.errMsg && err.errMsg.includes("timeout")) {
            errorMsg = "下载超时，请重试";
          }
          common_vendor.index.showModal({
            title: "预览失败",
            content: errorMsg + "\n\n是否尝试在浏览器中打开？",
            confirmText: "打开",
            cancelText: "取消",
            success: function(modalRes) {
              if (modalRes.confirm) {
                common_vendor.index.setClipboardData({
                  data: urlPdf,
                  success: function() {
                    common_vendor.index.showToast({
                      title: "链接已复制到剪贴板",
                      icon: "success"
                    });
                  }
                });
              }
            }
          });
        }
      });
    },
    // 获取文件类型
    getFileType(fileName) {
      const ext = fileName.split(".").pop().toLowerCase();
      const videoTypes = ["mp4", "avi", "mov", "wmv", "flv", "webm", "mkv", "ogg"];
      if (videoTypes.includes(ext))
        return "video";
      const imageTypes = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
      if (imageTypes.includes(ext))
        return "image";
      if (ext === "pdf")
        return "pdf";
      const docTypes = ["doc", "docx", "xls", "xlsx", "ppt", "pptx"];
      if (docTypes.includes(ext))
        return "document";
      return "unknown";
    },
    // 获取文件图标
    getMaterialIcon(fileName) {
      const ext = fileName.split(".").pop().toLowerCase();
      switch (ext) {
        case "pdf":
          return "paperplane";
        case "doc":
        case "docx":
          return "compose";
        case "xls":
        case "xlsx":
          return "bars";
        case "ppt":
        case "pptx":
          return "videocam";
        case "mp4":
        case "avi":
        case "mov":
        case "wmv":
        case "flv":
        case "webm":
        case "mkv":
          return "videocam-filled";
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
        case "bmp":
        case "webp":
          return "image";
        default:
          return "folder";
      }
    },
    // 预览视频文件
    previewVideo(videoUrl, fileName) {
      common_vendor.index.__f__("log", "at pages/form/form.vue:1274", "预览视频:", fileName);
      common_vendor.index.__f__("log", "at pages/form/form.vue:1275", "视频URL:", videoUrl);
      this.showVideoModal = true;
      this.currentVideoUrl = videoUrl;
      this.currentVideoName = fileName;
    },
    // 预览图片文件
    previewImage(imageUrl, fileName) {
      common_vendor.index.__f__("log", "at pages/form/form.vue:1285", "预览图片:", fileName);
      common_vendor.index.__f__("log", "at pages/form/form.vue:1286", "图片URL:", imageUrl);
      common_vendor.index.previewImage({
        urls: [imageUrl],
        current: imageUrl,
        success: function() {
          common_vendor.index.__f__("log", "at pages/form/form.vue:1293", "图片预览成功");
        },
        fail: function(err) {
          common_vendor.index.__f__("error", "at pages/form/form.vue:1296", "图片预览失败:", err);
          common_vendor.index.showToast({
            title: "图片预览失败",
            icon: "none"
          });
        }
      });
    },
    // 关闭视频预览
    closeVideoModal() {
      this.showVideoModal = false;
      this.currentVideoUrl = "";
      this.currentVideoName = "";
    },
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0)
        return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    }
  }
};
if (!Array) {
  const _component_uni_icons = common_vendor.resolveComponent("uni-icons");
  _component_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.productName),
    b: common_vendor.t($data.productCode),
    c: common_vendor.t($data.productManager),
    d: common_vendor.t($data.contactPhone),
    e: common_vendor.o(($event) => $options.callnum($data.contactPhone)),
    f: common_vendor.t($data.YWLX),
    g: $data.YWLX === "代理"
  }, $data.YWLX === "代理" ? {
    h: common_vendor.p({
      type: "link",
      size: "18",
      color: "#2979FF"
    }),
    i: common_vendor.p({
      type: "forward",
      size: "14",
      color: "#999"
    }),
    j: common_vendor.o((...args) => $options.openDLPPGW && $options.openDLPPGW(...args)),
    k: common_vendor.t($data.DLPPGW),
    l: common_vendor.o(($event) => $options.copyUrl($data.DLPPGW)),
    m: $data.agentBrandImage,
    n: common_vendor.o((...args) => $options.previewAgentImage && $options.previewAgentImage(...args))
  } : {}, {
    o: common_vendor.t($data.serviceDescription),
    p: $data.highResImageUrl
  }, $data.highResImageUrl ? {
    q: $data.highResImageUrl,
    r: common_vendor.o((...args) => $options.previewImage && $options.previewImage(...args))
  } : {
    s: common_assets._imports_0$1
  }, {
    t: $data.introductionMaterials.length > 0
  }, $data.introductionMaterials.length > 0 ? {
    v: common_vendor.p({
      type: "folder",
      size: "20",
      color: "#2979FF"
    }),
    w: common_vendor.t($data.introductionMaterials.length),
    x: common_vendor.p({
      type: "forward",
      size: "14",
      color: "#999"
    }),
    y: common_vendor.o((...args) => $options.showMaterialsList && $options.showMaterialsList(...args))
  } : {}, {
    z: $data.caseStudies.length > 0
  }, $data.caseStudies.length > 0 ? {
    A: common_vendor.f($data.caseStudies, (study, index, i0) => {
      return {
        a: common_vendor.t(study.clientName),
        b: common_vendor.t(study.details),
        c: index
      };
    })
  } : {}, {
    B: $data.recommendations.length > 0
  }, $data.recommendations.length > 0 ? {
    C: common_vendor.f($data.recommendations, (item, index, i0) => {
      return common_vendor.e({
        a: item.imageUrl || "/static/熙迈LOGO.png",
        b: common_vendor.t(item.englishName),
        c: item.YWLX === "代理"
      }, item.YWLX === "代理" ? {} : {}, {
        d: common_vendor.t(item.productName),
        e: item.isLiked ? "/static/红色小红心.svg" : "/static/灰色小红心.svg",
        f: item.showHeart
      }, item.showHeart ? {
        g: common_vendor.t(item.likeCount)
      } : {}, {
        h: common_vendor.o(($event) => $options.handleLike(item), index),
        i: index,
        j: common_vendor.o(($event) => $options.handleCategoryClick(item), index)
      });
    })
  } : {}, {
    D: $data.showMaterialsModal
  }, $data.showMaterialsModal ? {
    E: common_vendor.p({
      type: "close",
      size: "20",
      color: "#666"
    }),
    F: common_vendor.o((...args) => $options.hideMaterialsList && $options.hideMaterialsList(...args)),
    G: common_vendor.f($data.introductionMaterials, (material, index, i0) => {
      return {
        a: "4823dda4-5-" + i0,
        b: common_vendor.p({
          type: $options.getMaterialIcon(material.original_file_name),
          size: "24",
          color: "#2979FF"
        }),
        c: common_vendor.t(material.original_file_name),
        d: common_vendor.t($options.formatFileSize(material.file_size)),
        e: "4823dda4-6-" + i0,
        f: index,
        g: common_vendor.o(($event) => $options.previewMaterial(material), index)
      };
    }),
    H: common_vendor.p({
      type: "eye",
      size: "16",
      color: "#999"
    }),
    I: common_vendor.o(() => {
    }),
    J: common_vendor.o((...args) => $options.hideMaterialsList && $options.hideMaterialsList(...args))
  } : {}, {
    K: $data.showVideoModal
  }, $data.showVideoModal ? {
    L: common_vendor.t($data.currentVideoName),
    M: common_vendor.p({
      type: "close",
      size: "20",
      color: "#fff"
    }),
    N: common_vendor.o((...args) => $options.closeVideoModal && $options.closeVideoModal(...args)),
    O: $data.currentVideoUrl,
    P: common_vendor.o(() => {
    }),
    Q: common_vendor.o((...args) => $options.closeVideoModal && $options.closeVideoModal(...args))
  } : {}, {
    R: common_vendor.o(($event) => $options.handleContact($data.productManager))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/form/form.js.map
