
	/* 广告弹窗样式 */
.ad-popup-overlay {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
}
.ad-popup-content {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 40rpx;
		margin: 40rpx;
		max-width: 600rpx;
		width: 80%;
		position: relative;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
}
.ad-popup-close {
		position: absolute;
		top: 20rpx;
		right: 30rpx;
		font-size: 40rpx;
		color: #999;
		cursor: pointer;
		z-index: 10000;
}
.ad-popup-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		text-align: center;
		margin-bottom: 30rpx;
}
.ad-popup-image {
		width: 100%;
		max-height: 400rpx;
		border-radius: 10rpx;
		margin-bottom: 30rpx;
}
.ad-popup-button {
		background-color: #5a7fb8;
		color: #fff;
		text-align: center;
		padding: 20rpx 40rpx;
		border-radius: 50rpx;
		font-size: 28rpx;
		font-weight: bold;
		cursor: pointer;
}
.ad-popup-button:active {
		background-color: #4a6fa8;
}
.search-box {
		padding: 20rpx;
		display: flex;
		gap: 20rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #eee;
}
.search-input {
		flex: 1;
		height: 70rpx;
		padding: 0 20rpx;
		border: 1rpx solid #ddd;
		border-radius: 35rpx;
}
.search-btn {
		width: 140rpx;
		height: 70rpx;
		line-height: 70rpx;
		border-radius: 35rpx;
		background-color: #007AFF;
		color: white;
}
.container {
		position: relative;
		background: #42f3f933;
		animation: bg-flow 25s linear infinite;
		min-height: 100vh;
		padding: 0;
		padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
}

	/* 提升内容层级 */
.search-box,
	.category-list,
	.no-result {
		position: relative;
		z-index: 1;
}
.category-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 24rpx;
}
.category-item {
		width: calc(50% - 12rpx);
		margin-bottom: 24rpx;
		background-color: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
		overflow: hidden;
		position: relative;
}
.img {
		width: 100%;
		height: 250rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;
}
.img .icon {
		height: 100%;
		width: 100%;
		display: block;
		object-fit: contain;
}
.banner {
		padding: 10rpx;
}
.banner-left {
		width: 100%;
}
.top-blue {
		height: 60rpx;
		width: 100%;
		border-radius: 15rpx;
		background-color: #6d92cc;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 8rpx 12rpx;
		box-sizing: border-box;
}
.english-name {
		color: #ffffff;
		font-size: 16rpx;
		text-align: center;
		line-height: 1.4;
		word-wrap: break-word;
		word-break: break-all;
		width: 100%;
}
.bottom-white {
		color: #6d92cc;
		min-height: 80rpx;
		width: 100%;
		background-color: #fff;
		display: flex;
		align-items: center;
		padding: 10rpx;
		box-sizing: border-box;
}
.bottom-white-text {
		flex: 1;
		min-width: 0;
		font-size: 22rpx;
		text-align: center;
		white-space: normal;
		line-height: 1.4;
		margin: 0 8rpx;
}
.logo {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		width: 70rpx;
		height: 70rpx;
}
.logo .icon {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
		padding: 0;
}
.like-section {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 10rpx;
		flex-shrink: 0;
}
.like-icon {
		width: 40rpx;
		height: 40rpx;
}
.red-heart-animation {
		position: absolute;
		right: 12rpx;
		bottom: 30rpx;
		font-size: 30rpx;
		animation: pop-heart 0.6s ease-out forwards;
		pointer-events: none;
}
.pop-animation {
		position: absolute;
		bottom: 100%; /* 从按钮上方开始 */
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		flex-direction: column;
		animation: pop-up 0.8s ease-out forwards;
		pointer-events: none;
}
.pop-heart {
		font-size: 30rpx;
}
.pop-count {
		font-size: 24rpx;
		color: #ff6a6a;
		font-weight: bold;
}
@keyframes pop-up {
0% {
			transform: translateX(-50%) scale(0.5);
			opacity: 0;
			bottom: 100%;
}
50% {
			transform: translateX(-50%) scale(1.2);
			opacity: 1;
}
100% {
			transform: translateX(-50%) scale(1);
			opacity: 0;
			bottom: 150%; /* 向上飘动 */
}
}
@keyframes pop-heart {
0% {
			transform: scale(0.5);
			opacity: 0;
}
50% {
			transform: scale(1.6);
			opacity: 1;
}
100% {
			transform: scale(1);
			opacity: 0;
}
}
.no-result {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
		margin: 40rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		text-align: center;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.no-result-icon {
		font-size: 100rpx;
		color: #e0e0e0;
		margin-bottom: 20rpx;
}
.no-result-text {
		font-size: 32rpx;
		color: #666;
		margin-bottom: 10rpx;
}
.no-result-subtext {
		font-size: 24rpx;
		color: #999;
}
.service-btn {
		display: flex;
		align-items: center;
		padding: 10rpx;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		border-radius: 24rpx;
		box-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		-webkit-backdrop-filter: blur(10px);
		        backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
}
.text-container {
		flex: 1;
}
.btn-text {
		color: #fff;
		font-size: 38rpx;
		font-weight: 600;
		text-align: center;
		display: block;
		margin-bottom: 0;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.btn-subtext {
		color: rgba(255, 255, 255, 0.9);
		font-size: 24rpx;
		line-height: 1.6;
		text-align: center;
		display: block;
		opacity: 0.9;
		margin-bottom: 16rpx;
}

	/* 交互动画 */
.service-btn:active {
		transform: scale(0.98) translateY(2rpx);
		box-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);
}


	/* 悬浮呼吸动画 */
@keyframes float {
0% {
			transform: translateY(0);
}
50% {
			transform: translateY(-10rpx);
}
100% {
			transform: translateY(0);
}
}
.service-btn {
		animation: float 3s ease-in-out infinite;
}

	/* 流光边框效果 */
.service-btn::after {
		content: '';
		position: absolute;
		top: -1rpx;
		left: -1rpx;
		right: -1rpx;
		bottom: -1rpx;
		background: linear-gradient(45deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.3) 50%,
				rgba(255, 255, 255, 0) 100%);
		border-radius: 24rpx;
		animation: shine 3s infinite;
		z-index: -1;
}
@keyframes shine {
0% {
			opacity: 0;
			left: -50%;
}
50% {
			opacity: 0.4;
}
100% {
			opacity: 0;
			left: 150%;
}
}

