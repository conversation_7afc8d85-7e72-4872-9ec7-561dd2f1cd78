"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
if (!Math) {
  "./pages/home/<USER>";
  "./pages/index/index.js";
  "./pages/category/category.js";
  "./pages/form/form.js";
  "./pages/WebView/WebView.js";
  "./pages/apply/apply.js";
}
const _sfc_main = {
  onLaunch: function() {
    common_vendor.index.__f__("log", "at App.vue:6", "App Launch");
    this.globalData.shareConfig = {
      title: "熙迈科技服务有限公司 - 专业工业服务",
      path: "pages/home/<USER>",
      imageUrl: "/static/熙迈LOGO.png"
    };
  },
  onShow: function() {
    common_vendor.index.__f__("log", "at App.vue:16", "App Show");
  },
  onHide: function() {
    common_vendor.index.__f__("log", "at App.vue:19", "App Hide");
  },
  globalData: {
    shareConfig: {}
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
