{"version": 3, "names": ["_sfc_main", "data", "notices", "buttons", "icon", "text", "url", "type", "banners", "image", "methods", "handleButtonClick", "item", "handleCustomerService", "common_vendor", "index", "navigateTo", "Page", "handleContact", "e", "__f__", "detail", "path", "query", "callnum", "num", "makePhoneCall", "phoneNumber", "Location", "wx$1", "openLocation", "latitude", "longitude", "scale", "name", "handleBannerClick", "current", "wx", "createPage", "MiniProgramPage"], "sources": ["home.vue", "D:/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaG9tZS9ob21lLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 公告栏 -->\r\n\t\t<view class=\"notice\">\r\n\t\t\t<uni-icons type=\"sound\" size=\"18\" color=\"#666\"></uni-icons>\r\n\t\t\t<swiper class=\"notice-swiper\" vertical autoplay circular interval=\"3000\">\r\n\t\t\t\t<swiper-item v-for=\"(item, index) in notices\" :key=\"index\">\r\n\t\t\t\t\t<text class=\"notice-text\">{{ item }}</text>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\r\n\t\t<!-- 四个按钮 -->\r\n\t\t<view class=\"button-group\">\r\n\t\t\t<view class=\"button-item\"  v-for=\"(item, index) in buttons\" :key=\"index\" open-type=\"item.type\" @click=\"handleButtonClick(item)\">\r\n\t\t\t\t<uni-icons :type=\"item.icon\" size=\"30\" color=\"#2979FF\"></uni-icons>\r\n\t\t\t\t<text class=\"button-text\">{{ item.text }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 轮播图 -->\r\n\t\t<swiper class=\"banner\" circular autoplay interval=\"5000\" indicator-dots=\"true\" indicator-color=\"#007aff\">\r\n\t\t\t<swiper-item v-for=\"(item, index) in banners\" :key=\"index\">\r\n\t\t\t\t<image :src=\"item.image\" mode=\"aspectFill\" class=\"banner-image\"\r\n\t\t\t\t\t@click=\"handleBannerClick($event,item.url)\"></image>\r\n\t\t\t</swiper-item>\r\n\t\t</swiper>\r\n\r\n\t\t<!-- 联系我们 -->\r\n\t\t<view class=\"contact\">\r\n\t\t\t<text class=\"contact-title\">联系我们</text>\r\n\t\t\t<text class=\"contact-info\" @click=\"callnum()\">电话：************</text>\r\n\t\t\t<text class=\"contact-info\" @click=\"Location()\">地址：上海市浦东新区川沙路669号曹路创意空间622-626室</text>\r\n\t\t\t<text class=\"contact-info\">邮箱：<EMAIL></text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tnotices: [\r\n\t\t\t\t\t\"系统维护通知：2025年3月5日0:00-6:00进行系统升级\",\r\n\t\t\t\t\t\"欢迎使用本小程序，有问题请及时联系我们\"\r\n\t\t\t\t],\r\n\t\t\t\tbuttons: [{\r\n\t\t\t\t\t\ticon: \"home\",\r\n\t\t\t\t\t\ttext: \"产品展示\",\r\n\t\t\t\t\t\turl: \"/pages/index/index\",\r\n\t\t\t\t\t\ttype: \"navigateBack\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ticon: \"chat\",\r\n\t\t\t\t\t\ttext: \"联系客服\",\r\n\t\t\t\t\t\ttype: \"contact\"\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\tbanners: [{\r\n\t\t\t\t\t\timage: \"https://cdn.yun.sooce.cn/6/45743/jpg/17081758091926bc682f48a953fa738b953441c01be09.jpg?imageMogr2/thumbnail/1800x&version=1708175811\",\r\n\t\t\t\t\t\turl: \"/pages/WebView/WebView\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\timage: \"/static/banner2.jpg\",\r\n\t\t\t\t\t\turl: \"/pages/banner2/banner2\" // 添加跳转路径\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\timage: \"/static/banner3.jpg\",\r\n\t\t\t\t\t\turl: \"/pages/banner3/banner3\" // 添加跳转路径\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\thandleButtonClick(item) {\r\n\t\t\t\t// 跳转到对应页面\r\n\r\n\t\t\t\t// 新增判断逻辑\r\n\t\t\t\tif (item.text === '联系客服') {\r\n\t\t\t\t\tthis.handleCustomerService();\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 原有跳转逻辑\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: item.url\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\thandleCustomerService()\r\n\t\t\t\t{\r\n\t\t\t\t\tPage({\r\n\t\t\t\t\t    handleContact (e) {\r\n\t\t\t\t\t        console.log(e.detail.path)\r\n\t\t\t\t\t        console.log(e.detail.query)\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t})\r\n\t\t\t\t},\r\n\t\t\t\tcallnum(num) {\r\n\t\t\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\t\t\tphoneNumber: '************' //仅为示例\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tLocation() {\r\n\t\t\t\t\t\tconsole.log(123)\r\n\t\t\t\t\t\t// wx.getLocation({\r\n\t\t\t\t\t\t// \ttype: 'gcj02', //返回可以用于wx.openLocation的经纬度\r\n\t\t\t\t\t\t// \tsuccess(res) {\r\n\t\t\t\t\t\t// \t\tconst latitude = 121.339374\r\n\t\t\t\t\t\t// \t\tconst longitude = 31.19625\r\n\t\t\t\t\t\twx.openLocation({\r\n\t\t\t\t\t\t\tlatitude: 31.275361,\r\n\t\t\t\t\t\t\tlongitude: 121.670906,\r\n\t\t\t\t\t\t\tscale: 18,\r\n\t\t\t\t\t\t\tname: \"上海市浦东新区川沙路669号曹路创意空间622-626室\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t// }\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t},\r\n\t\t\t\t\thandleBannerClick(e, url) {\r\n\t\t\t\t\t\tconsole.log(e, url, 'url')\r\n\t\t\t\t\t\t// 获取当前点击的轮播图索引\r\n\t\t\t\t\t\tconst current = e.detail.current;\r\n\t\t\t\t\t\t// 跳转到对应页面\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: url\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n</script>\r\n\r\n<style>\r\n\tpage {\r\n\t\tfont-family: \"Helvetica Neue\", Helvetica, \"PingFang SC\", \"Hiragino Sans GB\", \"Microsoft YaHei\", Arial, sans-serif;\r\n\t}\r\n\r\n\t.container {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground: linear-gradient(180deg, #f8f9ff 0%, #f0f2ff 100%);\r\n\t}\r\n\r\n\t/* 公告栏 - 添加闪烁动画 */\r\n\t.notice {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #fffbe6;\r\n\t\tborder-radius: 10rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.notice-swiper {\r\n\t\theight: 40rpx;\r\n\t\tflex: 1;\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.notice-text {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\r\n\t/* 按钮组 - 使用网格布局 */\r\n\t.button-group {\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: repeat(4, 1fr);\r\n\t\tgap: 20rpx;\r\n\t\tbackground: transparent;\r\n\t\tpadding: 0;\r\n\t\tmargin-bottom: 24rpx;\r\n\t}\r\n\r\n\t.button-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 24rpx;\r\n\t\tpadding: 32rpx 0;\r\n\t\ttransition: all 0.3s ease;\r\n\t\tbox-shadow: 0 6rpx 20rpx rgba(41, 121, 255, 0.1);\r\n\t}\r\n\r\n\t.button-item:active {\r\n\t\ttransform: scale(0.98);\r\n\t\tbackground: #f5f7ff;\r\n\t}\r\n\r\n\t.uni-icons {\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\r\n\t.button-text {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t/* 轮播图 - 优化指示器 */\r\n\t.banner {\r\n\t\theight: 320rpx;\r\n\t\tborder-radius: 24rpx;\r\n\t\toverflow: hidden;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.banner-image {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\ttransition: transform 0.3s ease;\r\n\t}\r\n\r\n\t.banner swiper-item:hover .banner-image {\r\n\t\ttransform: scale(1.02);\r\n\t}\r\n\r\n\t/* 联系我们 - 卡片式设计 */\r\n\t.contact {\r\n\t\tbackground: #fff;\r\n\t\tpadding: 32rpx;\r\n\t\tborder-radius: 24rpx;\r\n\t\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.contact-title {\r\n\t\tfont-size: 34rpx;\r\n\t\tcolor: #1a1a1a;\r\n\t\tfont-weight: 600;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tposition: relative;\r\n\t\tpadding-left: 20rpx;\r\n\t}\r\n\r\n\t.contact-title::before {\r\n\t\tcontent: \"\";\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\ttop: 50%;\r\n\t\ttransform: translateY(-50%);\r\n\t\twidth: 8rpx;\r\n\t\theight: 32rpx;\r\n\t\tbackground: #2979FF;\r\n\t\tborder-radius: 4rpx;\r\n\t}\r\n\r\n\t.contact-info {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 16rpx;\r\n\t\tpadding: 12rpx 0;\r\n\t\tborder-bottom: 1rpx solid #eee;\r\n\t}\r\n\r\n\t.contact-info:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n</style>", "import MiniProgramPage from 'E:/小程序项目/熙迈门户/XMMH/pages/home/<USER>'\nwx.createPage(MiniProgramPage)"], "mappings": ";;;;AAuCC,IAAKA,SAAA,GAAU;EACdC,IAAA,WAAAA,KAAA,EAAO;IACN,OAAO;MACNC,OAAA,EAAS,CACR,mCACA,sBACA;MACDC,OAAA,EAAS,CAAC;QACRC,IAAA,EAAM;QACNC,IAAA,EAAM;QACNC,GAAA,EAAK;QACLC,IAAA,EAAM;MACN,GACD;QACCH,IAAA,EAAM;QACNC,IAAA,EAAM;QACNE,IAAA,EAAM;MACN,EACD;MACDC,OAAA,EAAS,CAAC;QACRC,KAAA,EAAO;QACPH,GAAA,EAAK;MACL,GACD;QACCG,KAAA,EAAO;QACPH,GAAA,EAAK;QAAA;MACL,GACD;QACCG,KAAA,EAAO;QACPH,GAAA,EAAK;QAAA;MACN;IACD;EAED;;EACDI,OAAA,EAAS;IACRC,iBAAA,WAAAA,kBAAkBC,IAAA,EAAM;MAIvB,IAAIA,IAAA,CAAKP,IAAA,KAAS,QAAQ;QACzB,KAAKQ,qBAAA,EAAqB;MAAA,OACpB;QAENC,aAAA,CAAAC,KAAA,CAAIC,UAAA,CAAW;UACdV,GAAA,EAAKM,IAAA,CAAKN;QACX,CAAC;MACF;IACC;IACDO,qBAAA,WAAAA,sBAAA,EACA;MACCI,IAAA,CAAK;QACDC,aAAA,WAAAA,cAAeC,CAAA,EAAG;UACdL,aAAA,CAAAC,KAAA,CAAAK,KAAA,qCAAYD,CAAA,CAAEE,MAAA,CAAOC,IAAI;UACzBR,aAAA,CAAYC,KAAA,CAAAK,KAAA,qCAAAD,CAAA,CAAEE,MAAA,CAAOE,KAAK;QAC9B;MAAA,CACH;IACD;IACDC,OAAA,WAAAA,QAAQC,GAAA,EAAK;MACXX,aAAA,CAAAC,KAAA,CAAIW,aAAA,CAAc;QACjBC,WAAA,EAAa;QAAA;MACd,CAAC;IACD;IACDC,QAAA,WAAAA,SAAA,EAAW;MACVd,aAAA,CAAAC,KAAA,CAAAK,KAAA,sCAAY,GAAG;MAMfN,aAAA,CAAAe,IAAA,CAAGC,YAAA,CAAa;QACfC,QAAA,EAAU;QACVC,SAAA,EAAW;QACXC,KAAA,EAAO;QACPC,IAAA,EAAM;MAAA,CACN;IAGD;IACDC,iBAAA,WAAAA,kBAAkBhB,CAAA,EAAGb,GAAA,EAAK;MACzBQ,aAAA,CAAYC,KAAA,CAAAK,KAAA,sCAAAD,CAAA,EAAGb,GAAA,EAAK,KAAK;MAETa,CAAA,CAAEE,MAAA,CAAOe,OAAA;MAEzBtB,aAAA,CAAAC,KAAA,CAAIC,UAAA,CAAW;QACdV,GAAA,EAAAA;MACD,CAAC;IACF;EACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7HH+B,EAAA,CAAGC,UAAA,CAAWC,eAAe", "ignoreList": []}