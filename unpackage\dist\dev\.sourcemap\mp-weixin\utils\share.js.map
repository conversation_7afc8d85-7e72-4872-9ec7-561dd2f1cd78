{"version": 3, "file": "share.js", "sources": ["utils/share.js"], "sourcesContent": ["/**\n * 分享工具类 - 简化版\n */\nclass ShareUtils {\n\n\t/**\n\t * 获取默认分享配置\n\t * @param {Object} options 配置参数\n\t * @param {string} options.title 分享标题\n\t * @param {string} options.path 分享路径\n\t * @param {string} options.imageUrl 分享图片\n\t * @returns {Object} 分享配置\n\t */\n\tstatic getDefaultShareConfig(options = {}) {\n\t\tconsole.log('📤 ShareUtils.getDefaultShareConfig 被调用');\n\t\tconsole.log('输入参数:', options);\n\n\t\tconst {\n\t\t\ttitle = '熙迈科技服务有限公司 - 专业工业服务',\n\t\t\tpath = 'pages/home/<USER>',\n\t\t\timageUrl = '/static/熙迈LOGO.png'\n\t\t} = options;\n\n\t\tconst shareConfig = {\n\t\t\ttitle,\n\t\t\tpath,\n\t\t\timageUrl\n\t\t};\n\n\t\tconsole.log('生成的分享配置:', shareConfig);\n\t\tconsole.log('分享路径长度:', path.length);\n\n\t\treturn shareConfig;\n\t}\n}\n\nexport default ShareUtils;\n"], "names": ["uni"], "mappings": ";;AAGA,MAAM,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUhB,OAAO,sBAAsB,UAAU,IAAI;AAC1CA,kBAAAA,2CAAY,yCAAyC;AACrDA,kBAAY,MAAA,MAAA,OAAA,wBAAA,SAAS,OAAO;AAE5B,UAAM;AAAA,MACL,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,WAAW;AAAA,IACX,IAAG;AAEJ,UAAM,cAAc;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,IACH;AAEEA,kBAAY,MAAA,MAAA,OAAA,wBAAA,YAAY,WAAW;AACnCA,kBAAY,MAAA,MAAA,OAAA,wBAAA,WAAW,KAAK,MAAM;AAElC,WAAO;AAAA,EACP;AACF;;"}