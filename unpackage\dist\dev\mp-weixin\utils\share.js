"use strict";
const common_vendor = require("../common/vendor.js");
class ShareUtils {
  /**
   * 获取默认分享配置
   * @param {Object} options 配置参数
   * @param {string} options.title 分享标题
   * @param {string} options.path 分享路径
   * @param {string} options.imageUrl 分享图片
   * @returns {Object} 分享配置
   */
  static getDefaultShareConfig(options = {}) {
    common_vendor.index.__f__("log", "at utils/share.js:15", "📤 ShareUtils.getDefaultShareConfig 被调用");
    common_vendor.index.__f__("log", "at utils/share.js:16", "输入参数:", options);
    const {
      title = "熙迈科技服务有限公司 - 专业工业服务",
      path = "pages/home/<USER>",
      imageUrl = "/static/熙迈LOGO.png"
    } = options;
    const shareConfig = {
      title,
      path,
      imageUrl
    };
    common_vendor.index.__f__("log", "at utils/share.js:30", "生成的分享配置:", shareConfig);
    common_vendor.index.__f__("log", "at utils/share.js:31", "分享路径长度:", path.length);
    return shareConfig;
  }
}
exports.ShareUtils = ShareUtils;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/share.js.map
