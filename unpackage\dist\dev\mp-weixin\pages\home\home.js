"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_share = require("../../utils/share.js");
const common_assets = require("../../common/assets.js");
const _sfc_main = {
  data() {
    return {
      showPopup: false,
      popupList: [],
      // 弹窗列表
      currentPopupIndex: 0,
      // 当前弹窗索引
      popupData: {
        title: "",
        imageUrl: "",
        jumpUrl: "",
        isActive: false
      },
      notices: [
        "欢迎使用本小程序，有问题请及时联系我们"
      ],
      // 修改为两个按钮数组
      mainButtons: [{
        icon: "index",
        text: "熙迈科技工业服务-点击了解更多",
        url: "/pages/index/index"
      }],
      subButtons: [
        {
          icon: "GZH",
          text: "公众号"
        },
        {
          icon: "SPH",
          text: "视频号"
        }
        // {
        // 	icon: "PMS",
        // 	text: "PMS",
        // 	url: "https://dmit.duoningbio.com/app/3f976431-6007-4fa7-ad78-14cc163f5b66?ch=no&ac=no"
        // },
        // {
        // 	icon: "BBS",
        // 	text: "BBS",
        // 	url: "http://cmbbs.duoningbio.com:5443/"
        // },
        // {
        // 	icon: "GSGW",
        // 	text: "公司官网",
        // 	url: "http://www.smile-tech.top/"
        // }
      ],
      banners: [
        {
          image: "https://cdn.yun.sooce.cn/6/45743/jpg/17081760583573cc2409e377f7e2779502a46c7c54cb0.jpg?imageMogr2/thumbnail/1800x&version=1708176059",
          url: "https://cdn.yun.sooce.cn/6/45743/jpg/17081760583573cc2409e377f7e2779502a46c7c54cb0.jpg?imageMogr2/thumbnail/1800x&version=1708176059"
        },
        {
          image: "https://cdn.yun.sooce.cn/6/45743/jpg/1708175809120f6c32e3ee03340d47e26d93bf4dad34b.jpg?imageMogr2/thumbnail/1800x&version=1708175811",
          url: "https://cdn.yun.sooce.cn/6/45743/jpg/1708175809120f6c32e3ee03340d47e26d93bf4dad34b.jpg?imageMogr2/thumbnail/1800x&version=1708175811"
          // 添加跳转路径
        },
        {
          image: "https://cdn.yun.sooce.cn/6/45743/jpg/1708175809193fc9e25a917635857492cc13c0f6b7173.jpg?imageMogr2/thumbnail/1800x&version=1708175811",
          url: "https://cdn.yun.sooce.cn/6/45743/jpg/1708175809193fc9e25a917635857492cc13c0f6b7173.jpg?imageMogr2/thumbnail/1800x&version=1708175811"
          // 添加跳转路径
        },
        {
          image: "https://cdn.yun.sooce.cn/6/45743/jpg/1708176038714046bd3b4360f5077916b30961fe86281.jpg?imageMogr2/thumbnail/1800x&version=1708176040",
          url: "https://cdn.yun.sooce.cn/6/45743/jpg/1708176038714046bd3b4360f5077916b30961fe86281.jpg?imageMogr2/thumbnail/1800x&version=1708176040"
          // 添加跳转路径
        },
        {
          image: "https://cdn.yun.sooce.cn/6/45743/jpg/17081758091926bc682f48a953fa738b953441c01be09.jpg?imageMogr2/thumbnail/1800x&version=1708175811",
          url: "https://cdn.yun.sooce.cn/6/45743/jpg/17081758091926bc682f48a953fa738b953441c01be09.jpg?imageMogr2/thumbnail/1800x&version=1708175811"
          // 添加跳转路径
        }
      ],
      contactInfos: [
        {
          title: "上海松江总部",
          phones: ["************", "021-68775023"],
          address: "上海市松江区新桥镇民强路1525号30幢3层",
          email: "<EMAIL>",
          latitude: 31.038129,
          longitude: 121.290108
        },
        {
          title: "华东服务中心",
          phones: ["************", "021-68775023"],
          address: "上海市浦东新区川沙路669号曹路创意空间622-626室",
          email: "<EMAIL>",
          latitude: 31.275257,
          longitude: 121.670465
        },
        {
          title: "西南服务中心",
          phones: ["************", "18980586458"],
          address: "四川省成都市武侯区桂溪街道蜀都中心二期1号楼3单元701室",
          email: "<EMAIL>",
          latitude: 30.553102,
          longitude: 104.065304
        },
        // {
        // 	title: "华南服务中心",
        // 	phones: ["400-0000-000", "13450476532"],
        // 	address: "深圳市龙华区民治街道民乐社区星河WORLD2期E栋22层EA10",
        // 	email: "<EMAIL>",
        // 	latitude: 22.604259,
        // 	longitude: 114.057841
        // },
        {
          title: "华北服务中心",
          phones: ["4000885153", "18686480987"],
          address: "沈阳市和平区红椿路38-6号1-1-2",
          email: "<EMAIL>",
          latitude: 41.697049,
          longitude: 123.385829
        }
      ]
    };
  },
  // 分享功能
  onShareAppMessage(res) {
    common_vendor.index.__f__("log", "at pages/home/<USER>", "分享给好友", res);
    return utils_share.ShareUtils.getDefaultShareConfig({
      title: "熙迈科技服务有限公司 - SMILE",
      path: "pages/home/<USER>",
      imageUrl: "/static/熙迈LOGO.png"
    });
  },
  onShareTimeline(res) {
    common_vendor.index.__f__("log", "at pages/home/<USER>", "分享到朋友圈", res);
    return utils_share.ShareUtils.getDefaultShareConfig({
      title: "熙迈科技服务有限公司 - SMILE",
      path: "pages/home/<USER>",
      imageUrl: "/static/熙迈LOGO.png"
    });
  },
  onLoad() {
    this.loadPopupData();
  },
  methods: {
    // 加载弹窗数据
    async loadPopupData() {
      try {
        common_vendor.index.__f__("log", "at pages/home/<USER>", "开始加载弹窗数据");
        const response = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows",
          method: "POST",
          data: {
            appKey: "984e1ff028f80150",
            sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
            worksheetId: "xcxdcwh",
            pageSize: 10,
            // 获取更多弹窗数据
            pageIndex: 1,
            listType: 0,
            controls: [],
            filters: [{
              controlId: "isActive",
              dataType: 36,
              // 复选框类型
              spliceType: 1,
              filterType: 2,
              // 等于
              value: "1"
              // 选中状态
            }]
          },
          header: {
            "Content-Type": "application/json"
          }
        });
        if (response.data && response.data.data && response.data.data.rows && response.data.data.rows.length > 0) {
          const activePopups = response.data.data.rows.filter((row) => {
            const now = /* @__PURE__ */ new Date();
            const startDate = new Date(row.startDate);
            const endDate = new Date(row.endDate);
            return now >= startDate && now <= endDate;
          });
          if (activePopups.length > 0) {
            this.popupList = activePopups.map((row) => {
              return {
                title: row.title || "",
                jumpUrl: row.jumpUrl || "",
                imageUrl: this.getImageUrl(row.imageUrl),
                startDate: row.startDate || "",
                endDate: row.endDate || "",
                isActive: row.isActive === "1"
              };
            });
            this.popupData = this.popupList[0];
            this.currentPopupIndex = 0;
            this.showPopup = true;
          }
        } else {
          common_vendor.index.__f__("log", "at pages/home/<USER>", "没有找到有效的弹窗数据");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/home/<USER>", "加载弹窗数据失败:", error);
      }
    },
    // 检查弹窗是否在有效期内
    isPopupValid() {
      if (!this.popupData.isActive) {
        return false;
      }
      const now = /* @__PURE__ */ new Date();
      const startDate = this.popupData.startDate ? new Date(this.popupData.startDate) : null;
      const endDate = this.popupData.endDate ? new Date(this.popupData.endDate) : null;
      if (startDate && now < startDate) {
        return false;
      }
      if (endDate && now > endDate) {
        return false;
      }
      return true;
    },
    // 处理图片URL
    getImageUrl(imageData) {
      try {
        const parsedData = Array.isArray(imageData) ? imageData : JSON.parse(imageData);
        if (!Array.isArray(parsedData)) {
          common_vendor.index.__f__("warn", "at pages/home/<USER>", "图片数据格式错误");
          return "";
        }
        if (parsedData.length > 0) {
          const firstImage = parsedData[0];
          if (firstImage.preview_url) {
            return firstImage.preview_url;
          }
        }
        return "";
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/home/<USER>", "解析图片URL失败:", error.message);
        return "";
      }
    },
    // 关闭弹窗
    closePopup() {
      this.showPopup = false;
    },
    // 轮播图切换事件
    onSwiperChange(e) {
      this.currentPopupIndex = e.detail.current;
      this.popupData = this.popupList[this.currentPopupIndex];
    },
    // 处理弹窗点击
    handlePopupClick(popup) {
      const currentPopup = popup || this.popupData;
      if (!currentPopup.jumpUrl) {
        common_vendor.index.__f__("log", "at pages/home/<USER>", "没有跳转链接");
        return;
      }
      if (currentPopup.jumpUrl.startsWith("#小程序://")) {
        const match = currentPopup.jumpUrl.match(/#小程序:\/\/([^\/]+)\/(.+)/);
        if (match) {
          match[1];
          const path = match[2];
          common_vendor.wx$1.navigateTo({
            url: `${path}`,
            success: (res) => {
              common_vendor.index.__f__("log", "at pages/home/<USER>", "跳转成功:", res);
            },
            fail: (err) => {
              common_vendor.index.__f__("error", "at pages/home/<USER>", "跳转失败:", err);
              common_vendor.index.navigateTo({
                url: "/pages/index/index"
              });
            }
          });
        }
      } else if (currentPopup.jumpUrl.startsWith("/pages/")) {
        common_vendor.index.navigateTo({
          url: currentPopup.jumpUrl
        });
      } else if (currentPopup.jumpUrl.startsWith("http")) {
        common_vendor.index.navigateTo({
          url: `/pages/WebView/WebView?url=${encodeURIComponent(currentPopup.jumpUrl)}`
        });
      }
      this.closePopup();
    },
    // 处理小程序链接
    handleMiniProgramLink(link2) {
      common_vendor.index.__f__("log", "at pages/home/<USER>", "处理小程序链接:", link2);
      const match = link2.match(/#小程序:\/\/([^\/]+)\/(.+)/);
      if (match) {
        const appName = match[1];
        const path = match[2];
        common_vendor.index.__f__("log", "at pages/home/<USER>", "解析结果:", { appName, path });
        common_vendor.index.showModal({
          title: "跳转小程序",
          content: `即将跳转到"${appName}"小程序，请确认是否继续？`,
          confirmText: "确认跳转",
          cancelText: "取消",
          success: (res) => {
            if (res.confirm) {
              this.generateShortLinkAndJump(appName, path, link2);
            }
          }
        });
      } else {
        common_vendor.index.__f__("error", "at pages/home/<USER>", "小程序链接格式不正确:", link2);
        common_vendor.index.showToast({
          title: "链接格式错误",
          icon: "none"
        });
      }
    },
    // 生成短链接并跳转
    async generateShortLinkAndJump(appName, path, originalLink) {
      const appIdMap = {
        "熙迈科技-SMILETECH": "wx6ebba6c9d8c21fb1"
        // 可以添加更多小程序的映射
      };
      const appId = appIdMap[appName];
      if (!appId) {
        common_vendor.index.__f__("error", "at pages/home/<USER>", "未配置小程序appId:", appName);
        common_vendor.index.showModal({
          title: "跳转失败",
          content: "该小程序暂未配置，请联系管理员添加配置。",
          showCancel: false,
          confirmText: "知道了"
        });
        return;
      }
      try {
        common_vendor.index.__f__("log", "at pages/home/<USER>", "开始生成短链接...");
        const shortLinkRes = await new Promise((resolve, reject) => {
          common_vendor.wx$1.generateShortLink({
            pageUrl: `pages/index/index?shareCode=${encodeURIComponent(path)}`,
            pageTitle: `${appName}产品详情`,
            isPermanent: false,
            // 临时链接
            success: resolve,
            fail: reject
          });
        });
        common_vendor.index.__f__("log", "at pages/home/<USER>", "短链接生成成功:", shortLinkRes);
        if (shortLinkRes.link) {
          common_vendor.index.setClipboardData({
            data: shortLinkRes.link,
            success: () => {
              common_vendor.index.showModal({
                title: "链接已生成",
                content: "短链接已复制到剪贴板，请在微信中打开。",
                showCancel: false,
                confirmText: "知道了"
              });
            }
          });
        } else {
          throw new Error("短链接生成失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/home/<USER>", "生成短链接失败:", error);
        common_vendor.index.__f__("log", "at pages/home/<USER>", "降级为直接跳转方案");
        this.attemptMiniProgramJump(appName, path);
      }
    },
    // 尝试跳转小程序
    attemptMiniProgramJump(appName, path) {
      const appIdMap = {
        "熙迈科技-SMILETECH": "wx6ebba6c9d8c21fb1"
        // 熙迈科技小程序的真实appId
        // 可以添加更多小程序的映射
      };
      const appId = appIdMap[appName];
      if (!appId) {
        common_vendor.index.__f__("error", "at pages/home/<USER>", "未配置小程序appId:", appName);
        common_vendor.index.showModal({
          title: "跳转失败",
          content: "该小程序暂未配置，请联系管理员添加配置。",
          showCancel: false,
          confirmText: "知道了"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages/home/<USER>", "使用appId:", appId);
      common_vendor.index.__f__("log", "at pages/home/<USER>", "原始path:", path);
      let targetPath = "";
      if (path && path.length < 20 && !/^pages\//.test(path)) {
        common_vendor.index.__f__("log", "at pages/home/<USER>", "检测到分享码格式，跳转到首页");
        targetPath = path;
      } else if (path && path.startsWith("pages/")) {
        targetPath = path;
      } else {
        targetPath = "pages/index/index";
      }
      common_vendor.index.__f__("log", "at pages/home/<USER>", "最终跳转路径:", targetPath);
      common_vendor.index.__f__("log", "at pages/home/<USER>", "准备跳转小程序，参数:", {
        appId,
        path: targetPath,
        envVersion: "release"
      });
      common_vendor.wx$1.navigateToMiniProgram({
        appId,
        path: targetPath,
        extraData: {
          // 可以传递额外数据
          shareCode: path
          // 将原始分享码作为额外数据传递
        },
        envVersion: "release",
        // 正式版
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/home/<USER>", "跳转小程序成功:", res);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/home/<USER>", "跳转小程序失败:", err);
          common_vendor.index.__f__("error", "at pages/home/<USER>", "错误详情:", JSON.stringify(err));
          let errorMsg = "跳转失败";
          let content = "无法跳转到目标小程序";
          if (err.errMsg.includes("appId") || err.errMsg.includes("invalid")) {
            errorMsg = "配置错误";
            content = "小程序配置有误，请联系管理员检查appId配置。";
          } else if (err.errMsg.includes("permission")) {
            errorMsg = "权限不足";
            content = "当前小程序没有跳转到目标小程序的权限。";
          } else if (err.errMsg.includes("not exist")) {
            errorMsg = "小程序不存在";
            content = "目标小程序不存在或已下线。";
          }
          common_vendor.index.showModal({
            title: errorMsg,
            content: content + "\n\n是否复制小程序链接到剪贴板？",
            confirmText: "复制链接",
            cancelText: "取消",
            success: (modalRes) => {
              if (modalRes.confirm) {
                common_vendor.index.setClipboardData({
                  data: link,
                  success: () => {
                    common_vendor.index.showToast({
                      title: "链接已复制",
                      icon: "success"
                    });
                  }
                });
              }
            }
          });
        }
      });
    },
    // 图片加载成功处理
    handleImageLoad(e) {
    },
    // 图片加载失败处理
    handleImageError(e) {
      common_vendor.index.__f__("log", "at pages/home/<USER>", "图片加载失败:", e);
      common_vendor.index.__f__("log", "at pages/home/<USER>", "失败的图片URL:", this.popupData.imageUrl);
      common_vendor.index.showToast({
        title: "图片加载失败",
        icon: "none",
        duration: 2e3
      });
    },
    handleContact(e) {
      common_vendor.wx$1.openCustomerServiceChat({
        extInfo: {
          url: "https://work.weixin.qq.com/kfid/kfcaf7fbb93aa905a54"
        },
        corpId: "wwa76e36d25343b6b9",
        success(res) {
        }
      });
    },
    handleButtonClick(item) {
      if (item.text === "公众号") {
        common_vendor.wx$1.openOfficialAccountProfile({
          username: "gh_42bacb18625e",
          success: (res) => {
          },
          fail: (res) => {
          }
        });
      } else if (item.icon === "PMS" || item.icon === "BBS" || item.icon === "GSGW") {
        if (!item.url) {
          common_vendor.index.__f__("error", "at pages/home/<USER>", "URL is undefined for button:", item.text);
          return;
        }
        common_vendor.index.navigateTo({
          url: `/pages/WebView/WebView?url=${encodeURIComponent(item.url)}`
        });
      } else if (item.text === "视频号") {
        common_vendor.wx$1.openChannelsUserProfile({
          finderUserName: "sph1phDRUOAw9ds"
        });
      } else if (item.icon === "index") {
        if (!item.url) {
          common_vendor.index.__f__("error", "at pages/home/<USER>", "url is undefined for button:", item.text);
          return;
        }
        common_vendor.index.navigateTo({
          url: item.url
        });
      }
    },
    callnum(phone) {
      if (phone) {
        common_vendor.index.makePhoneCall({
          phoneNumber: phone
        });
      }
    },
    Location(latitude, longitude, name) {
      common_vendor.wx$1.openLocation({
        latitude,
        longitude,
        scale: 18,
        name
      });
    },
    handleBannerClick(e, url) {
      common_vendor.index.__f__("log", "at pages/home/<USER>", e, url, "url");
      e.detail.current;
      common_vendor.index.navigateTo({
        url
      });
    }
  }
};
if (!Array) {
  const _component_uni_icons = common_vendor.resolveComponent("uni-icons");
  _component_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_assets._imports_0,
    b: common_vendor.f($data.mainButtons, (item, index, i0) => {
      return {
        a: "07e72d3c-0-" + i0,
        b: common_vendor.p({
          type: item.icon,
          size: "30",
          color: "#2979FF"
        }),
        c: common_vendor.t(item.text),
        d: index,
        e: common_vendor.o(($event) => $options.handleButtonClick(item), index)
      };
    }),
    c: common_assets._imports_1,
    d: common_vendor.f($data.subButtons, (item, index, i0) => {
      return {
        a: "07e72d3c-1-" + i0,
        b: common_vendor.p({
          type: item.icon,
          size: "30",
          color: "#2979FF"
        }),
        c: common_vendor.t(item.text),
        d: index,
        e: common_vendor.o(($event) => $options.handleButtonClick(item), index)
      };
    }),
    e: common_vendor.f($data.banners, (item, index, i0) => {
      return {
        a: item.image,
        b: common_vendor.o(($event) => $options.handleBannerClick($event, item.url), index),
        c: index
      };
    }),
    f: common_vendor.p({
      type: "sound",
      size: "18",
      color: "#666"
    }),
    g: common_vendor.f($data.notices, (item, index, i0) => {
      return {
        a: common_vendor.t(item),
        b: index
      };
    }),
    h: common_vendor.f($data.contactInfos, (info, index, i0) => {
      return {
        a: common_vendor.t(info.title),
        b: common_vendor.f(info.phones, (phone, i, i1) => {
          return {
            a: common_vendor.t(i + 1),
            b: common_vendor.t(phone),
            c: i,
            d: common_vendor.o(($event) => $options.callnum(phone), i)
          };
        }),
        c: common_vendor.t(info.address),
        d: common_vendor.o(($event) => $options.Location(info.latitude, info.longitude, info.address), index),
        e: common_vendor.t(info.email),
        f: index
      };
    }),
    i: common_vendor.o((...args) => $options.handleContact && $options.handleContact(...args)),
    j: $data.showPopup
  }, $data.showPopup ? common_vendor.e({
    k: common_vendor.o((...args) => $options.closePopup && $options.closePopup(...args)),
    l: common_vendor.f($data.popupList, (popup, index, i0) => {
      return common_vendor.e({
        a: popup.imageUrl,
        b: common_vendor.o(($event) => $options.handlePopupClick(popup), index),
        c: common_vendor.o((...args) => $options.handleImageLoad && $options.handleImageLoad(...args), index),
        d: popup.title
      }, popup.title ? {
        e: common_vendor.t(popup.title)
      } : {}, {
        f: index
      });
    }),
    m: $data.popupList.length > 1,
    n: common_vendor.o((...args) => $options.onSwiperChange && $options.onSwiperChange(...args)),
    o: $data.popupList.length > 1
  }, $data.popupList.length > 1 ? {
    p: common_vendor.t($data.currentPopupIndex + 1),
    q: common_vendor.t($data.popupList.length)
  } : {}, {
    r: common_vendor.o(() => {
    }),
    s: common_vendor.o((...args) => $options.closePopup && $options.closePopup(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-07e72d3c"]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/home/<USER>
