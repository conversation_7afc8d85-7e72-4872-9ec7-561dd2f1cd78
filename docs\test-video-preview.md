# 视频预览功能测试指南

## 测试准备

### 1. 测试数据准备
为了测试视频预览功能，需要准备以下测试数据：

```javascript
// 测试用的材料数据示例
const testMaterials = [
    {
        original_file_name: "产品介绍视频.mp4",
        DownloadUrl: "https://example.com/video.mp4",
        original_file_full_path: "https://example.com/video.mp4",
        file_size: 10485760 // 10MB
    },
    {
        original_file_name: "产品图片.jpg",
        DownloadUrl: "https://example.com/image.jpg",
        original_file_full_path: "https://example.com/image.jpg",
        file_size: 2097152 // 2MB
    },
    {
        original_file_name: "产品手册.pdf",
        DownloadUrl: "https://example.com/manual.pdf",
        original_file_full_path: "https://example.com/manual.pdf",
        file_size: 5242880 // 5MB
    }
];
```

### 2. 测试环境
- 微信开发者工具
- 真机测试（推荐）
- 确保网络连接正常

## 测试用例

### 测试用例 1：文件类型检测
**目的**：验证 `getFileType()` 方法能正确识别不同文件类型

**步骤**：
1. 在控制台调用 `this.getFileType('test.mp4')`
2. 验证返回值为 'video'
3. 测试其他文件类型：
   - `getFileType('test.jpg')` 应返回 'image'
   - `getFileType('test.pdf')` 应返回 'pdf'
   - `getFileType('test.doc')` 应返回 'document'

**预期结果**：
- mp4 文件返回 'video'
- jpg 文件返回 'image'
- pdf 文件返回 'pdf'
- doc 文件返回 'document'

### 测试用例 2：视频文件图标显示
**目的**：验证视频文件显示正确的图标

**步骤**：
1. 在相关资料列表中添加 mp4 文件
2. 检查文件图标是否为 'videocam-filled'

**预期结果**：
- 视频文件显示视频图标
- 图标颜色为 #2979FF

### 测试用例 3：视频预览功能
**目的**：验证点击视频文件能正确打开视频预览弹窗

**步骤**：
1. 点击相关资料中的 mp4 文件
2. 检查是否显示视频预览弹窗
3. 验证视频是否能正常播放
4. 测试全屏功能
5. 测试关闭功能

**预期结果**：
- 弹窗正确显示
- 视频能正常播放
- 控制按钮正常工作
- 能正确关闭弹窗

### 测试用例 4：图片预览功能
**目的**：验证点击图片文件能正确打开图片预览

**步骤**：
1. 点击相关资料中的 jpg 文件
2. 检查是否调用微信小程序的图片预览功能
3. 验证图片是否能正常显示

**预期结果**：
- 使用微信原生图片预览
- 图片正常显示
- 支持缩放等操作

### 测试用例 5：文档预览兼容性
**目的**：验证原有的文档预览功能不受影响

**步骤**：
1. 点击相关资料中的 pdf 文件
2. 验证是否仍使用原有的文档预览方式
3. 测试 doc、xls 等文件类型

**预期结果**：
- PDF 文件使用原有预览方式
- 其他文档类型正常预览
- 不影响现有功能

### 测试用例 6：错误处理
**目的**：验证错误情况的处理

**步骤**：
1. 测试无效的视频 URL
2. 测试网络错误情况
3. 测试不支持的文件格式

**预期结果**：
- 显示适当的错误提示
- 不会导致应用崩溃
- 提供备选方案（如复制链接）

## 手动测试步骤

### 1. 在 category 页面测试
1. 打开任意分类页面
2. 点击"查看相关资料"按钮
3. 在弹出的资料列表中查找视频文件
4. 点击视频文件进行预览测试

### 2. 在 form 页面测试
1. 打开任意产品详情页面
2. 滚动到"介绍资料"部分
3. 点击"查看相关资料"按钮
4. 测试视频文件预览功能

### 3. 响应式测试
1. 在不同屏幕尺寸下测试
2. 横屏和竖屏切换测试
3. 不同设备型号测试

## 调试技巧

### 1. 控制台日志
在预览过程中，注意查看控制台输出：
```
预览文件: 产品介绍视频.mp4
文件类型: video
预览视频: 产品介绍视频.mp4
视频URL: https://example.com/video.mp4
```

### 2. 网络请求监控
- 检查视频文件的网络请求
- 确认 URL 是否正确
- 验证文件是否能正常下载

### 3. 常见问题排查
1. **视频无法播放**：
   - 检查视频格式是否支持
   - 确认 URL 是 HTTPS 协议
   - 验证文件是否存在

2. **弹窗不显示**：
   - 检查 `showVideoModal` 状态
   - 确认事件绑定是否正确
   - 验证 CSS 样式是否正确

3. **图标不显示**：
   - 检查 uni-icons 组件是否正确引入
   - 确认图标名称是否正确
   - 验证 CSS 样式是否生效

## 性能测试

### 1. 加载时间测试
- 测试不同大小视频文件的加载时间
- 记录首次播放的延迟时间

### 2. 内存使用测试
- 监控视频播放时的内存使用
- 测试多次打开关闭弹窗的内存释放

### 3. 网络流量测试
- 测试视频预览的网络流量消耗
- 验证是否有不必要的重复请求

## 测试报告模板

```
测试日期：____
测试环境：____
测试设备：____

测试结果：
□ 文件类型检测正常
□ 视频预览功能正常
□ 图片预览功能正常
□ 文档预览兼容性正常
□ 错误处理正常
□ 响应式布局正常

发现问题：
1. ____
2. ____

建议改进：
1. ____
2. ____
```
