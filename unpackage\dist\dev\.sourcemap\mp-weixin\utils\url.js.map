{"version": 3, "file": "url.js", "sources": ["utils/url.js"], "sourcesContent": ["export function objectToParams(obj) {\r\n  return Object.keys(obj)\r\n    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(obj[key])}`)\r\n    .join('&');\r\n} "], "names": [], "mappings": ";AAAO,SAAS,eAAe,KAAK;AAClC,SAAO,OAAO,KAAK,GAAG,EACnB,IAAI,SAAO,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,IAAI,GAAG,CAAC,CAAC,EAAE,EACvE,KAAK,GAAG;AACb;;"}