{"version": 3, "file": "share-demo.js", "sources": ["pages/share-demo/share-demo.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc2hhcmUtZGVtby9zaGFyZS1kZW1vLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<view class=\"header\">\n\t\t\t<text class=\"title\">分享功能演示</text>\n\t\t\t<text class=\"subtitle\">体验小程序的各种分享方式</text>\n\t\t</view>\n\t\t\n\t\t<view class=\"demo-section\">\n\t\t\t<view class=\"section-title\">基础分享功能</view>\n\t\t\t\n\t\t\t<view class=\"demo-item\">\n\t\t\t\t<view class=\"demo-info\">\n\t\t\t\t\t<text class=\"demo-name\">分享给好友</text>\n\t\t\t\t\t<text class=\"demo-desc\">点击右上角菜单或使用按钮分享</text>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"demo-btn\" @click=\"shareToFriend\">分享好友</button>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"demo-item\">\n\t\t\t\t<view class=\"demo-info\">\n\t\t\t\t\t<text class=\"demo-name\">分享到朋友圈</text>\n\t\t\t\t\t<text class=\"demo-desc\">点击右上角菜单分享到朋友圈</text>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"demo-btn\" @click=\"shareToTimeline\">分享朋友圈</button>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"demo-section\">\n\t\t\t<view class=\"section-title\">链接生成功能</view>\n\t\t\t\n\t\t\t<view class=\"demo-item\">\n\t\t\t\t<view class=\"demo-info\">\n\t\t\t\t\t<text class=\"demo-name\">生成URL Scheme</text>\n\t\t\t\t\t<text class=\"demo-desc\">生成可在微信外打开的链接</text>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"demo-btn\" @click=\"generateUrlScheme\">生成链接</button>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"demo-item\">\n\t\t\t\t<view class=\"demo-info\">\n\t\t\t\t\t<text class=\"demo-name\">生成短链接</text>\n\t\t\t\t\t<text class=\"demo-desc\">生成短链接用于分享</text>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"demo-btn\" @click=\"generateShortLink\">生成短链</button>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<view class=\"demo-section\">\n\t\t\t<view class=\"section-title\">工具功能</view>\n\t\t\t\n\t\t\t<view class=\"demo-item\">\n\t\t\t\t<view class=\"demo-info\">\n\t\t\t\t\t<text class=\"demo-name\">复制页面路径</text>\n\t\t\t\t\t<text class=\"demo-desc\">复制当前页面路径到剪贴板</text>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"demo-btn\" @click=\"copyPagePath\">复制路径</button>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"demo-item\">\n\t\t\t\t<view class=\"demo-info\">\n\t\t\t\t\t<text class=\"demo-name\">显示分享面板</text>\n\t\t\t\t\t<text class=\"demo-desc\">显示完整的分享选项面板</text>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"demo-btn\" @click=\"showSharePanel\">分享面板</button>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 使用分享组件 -->\n\t\t<ShareButton \n\t\t\t:shareConfig=\"{\n\t\t\t\ttitle: '分享功能演示页面',\n\t\t\t\tpath: 'pages/share-demo/share-demo',\n\t\t\t\timageUrl: '/static/熙迈LOGO.png'\n\t\t\t}\"\n\t\t/>\n\t</view>\n</template>\n\n<script>\n\timport ShareUtils from '@/utils/share.js';\n\timport ShareButton from '@/components/ShareButton/ShareButton.vue';\n\t\n\texport default {\n\t\tcomponents: {\n\t\t\tShareButton\n\t\t},\n\t\t\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 分享给好友\n\t\tonShareAppMessage(res) {\n\t\t\tconsole.log('分享给好友', res);\n\t\t\treturn ShareUtils.getDefaultShareConfig({\n\t\t\t\ttitle: '分享功能演示 - 熙迈科技',\n\t\t\t\tpath: 'pages/share-demo/share-demo',\n\t\t\t\timageUrl: '/static/熙迈LOGO.png'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 分享到朋友圈\n\t\tonShareTimeline(res) {\n\t\t\tconsole.log('分享到朋友圈', res);\n\t\t\treturn ShareUtils.getDefaultShareConfig({\n\t\t\t\ttitle: '分享功能演示 - 熙迈科技',\n\t\t\t\tpath: 'pages/share-demo/share-demo',\n\t\t\t\timageUrl: '/static/熙迈LOGO.png'\n\t\t\t});\n\t\t},\n\t\t\n\t\tmethods: {\n\t\t\tshareToFriend() {\n\t\t\t\tShareUtils.shareToFriend({\n\t\t\t\t\ttitle: '分享功能演示 - 熙迈科技',\n\t\t\t\t\tpath: 'pages/share-demo/share-demo'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\tshareToTimeline() {\n\t\t\t\tShareUtils.shareToTimeline({\n\t\t\t\t\ttitle: '分享功能演示 - 熙迈科技',\n\t\t\t\t\tpath: 'pages/share-demo/share-demo'\n\t\t\t\t});\n\t\t\t},\n\t\t\t\n\t\t\tasync generateUrlScheme() {\n\t\t\t\twx.showLoading({\n\t\t\t\t\ttitle: '生成中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst urlScheme = await ShareUtils.generateUrlScheme({\n\t\t\t\t\t\tpath: 'pages/share-demo/share-demo',\n\t\t\t\t\t\tquery: { from: 'demo' }\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\twx.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\twx.showModal({\n\t\t\t\t\t\ttitle: 'URL Scheme',\n\t\t\t\t\t\tcontent: `生成成功！\\n\\n${urlScheme}`,\n\t\t\t\t\t\tconfirmText: '复制',\n\t\t\t\t\t\tcancelText: '关闭',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tShareUtils.copyToClipboard(urlScheme);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\twx.hideLoading();\n\t\t\t\t\twx.showToast({\n\t\t\t\t\t\ttitle: '生成失败',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tasync generateShortLink() {\n\t\t\t\twx.showLoading({\n\t\t\t\t\ttitle: '生成中...'\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst shortLink = await ShareUtils.generateShortLink({\n\t\t\t\t\t\tpath: 'pages/share-demo/share-demo',\n\t\t\t\t\t\tquery: { from: 'demo' }\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\twx.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\twx.showModal({\n\t\t\t\t\t\ttitle: '短链接',\n\t\t\t\t\t\tcontent: `生成成功！\\n\\n${shortLink}`,\n\t\t\t\t\t\tconfirmText: '复制',\n\t\t\t\t\t\tcancelText: '关闭',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tShareUtils.copyToClipboard(shortLink);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\twx.hideLoading();\n\t\t\t\t\twx.showToast({\n\t\t\t\t\t\ttitle: '生成失败',\n\t\t\t\t\t\ticon: 'error'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tcopyPagePath() {\n\t\t\t\tconst currentPages = getCurrentPages();\n\t\t\t\tconst currentPage = currentPages[currentPages.length - 1];\n\t\t\t\tconst path = currentPage.route;\n\t\t\t\tShareUtils.copyToClipboard(`小程序页面：${path}`, '页面路径已复制');\n\t\t\t},\n\t\t\t\n\t\t\tshowSharePanel() {\n\t\t\t\tShareUtils.showSharePanel({\n\t\t\t\t\ttitle: '分享功能演示 - 熙迈科技',\n\t\t\t\t\tpath: 'pages/share-demo/share-demo'\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t};\n</script>\n\n<style scoped>\n\t.container {\n\t\tpadding: 40rpx;\n\t\tbackground: #f5f5f5;\n\t\tmin-height: 100vh;\n\t}\n\t\n\t.header {\n\t\ttext-align: center;\n\t\tmargin-bottom: 60rpx;\n\t}\n\t\n\t.title {\n\t\tdisplay: block;\n\t\tfont-size: 48rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tmargin-bottom: 16rpx;\n\t}\n\t\n\t.subtitle {\n\t\tdisplay: block;\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.demo-section {\n\t\tbackground: #fff;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 32rpx;\n\t\tmargin-bottom: 32rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n\t}\n\t\n\t.section-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t\tmargin-bottom: 32rpx;\n\t\tpadding-bottom: 16rpx;\n\t\tborder-bottom: 2rpx solid #eee;\n\t}\n\t\n\t.demo-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 24rpx 0;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t}\n\t\n\t.demo-item:last-child {\n\t\tborder-bottom: none;\n\t}\n\t\n\t.demo-info {\n\t\tflex: 1;\n\t}\n\t\n\t.demo-name {\n\t\tdisplay: block;\n\t\tfont-size: 30rpx;\n\t\tcolor: #333;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.demo-desc {\n\t\tdisplay: block;\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.demo-btn {\n\t\tbackground: linear-gradient(135deg, #2979FF, #00B4FF);\n\t\tcolor: #fff;\n\t\tborder: none;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 16rpx 32rpx;\n\t\tfont-size: 26rpx;\n\t\tmin-width: 120rpx;\n\t}\n\t\n\t.demo-btn:active {\n\t\topacity: 0.8;\n\t}\n</style>\n", "import MiniProgramPage from 'D:/小程序项目/熙迈门户/XMMH/pages/share-demo/share-demo.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "ShareUtils", "wx"], "mappings": ";;;AAgFC,MAAO,cAAa,MAAW;AAE/B,MAAK,YAAU;AAAA,EACd,YAAY;AAAA,IACX;AAAA,EACA;AAAA,EAED,OAAO;AACN,WAAO;EAGP;AAAA;AAAA,EAGD,kBAAkB,KAAK;AACtBA,kBAAA,MAAA,MAAA,OAAA,yCAAY,SAAS,GAAG;AACxB,WAAOC,YAAAA,WAAW,sBAAsB;AAAA,MACvC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IACX,CAAC;AAAA,EACD;AAAA;AAAA,EAGD,gBAAgB,KAAK;AACpBD,kBAAA,MAAA,MAAA,OAAA,0CAAY,UAAU,GAAG;AACzB,WAAOC,YAAAA,WAAW,sBAAsB;AAAA,MACvC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IACX,CAAC;AAAA,EACD;AAAA,EAED,SAAS;AAAA,IACR,gBAAgB;AACfA,kBAAAA,WAAW,cAAc;AAAA,QACxB,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA,IAED,kBAAkB;AACjBA,kBAAAA,WAAW,gBAAgB;AAAA,QAC1B,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA,IAED,MAAM,oBAAoB;AACzBC,oBAAAA,KAAG,YAAY;AAAA,QACd,OAAO;AAAA,MACR,CAAC;AAED,UAAI;AACH,cAAM,YAAY,MAAMD,YAAU,WAAC,kBAAkB;AAAA,UACpD,MAAM;AAAA,UACN,OAAO,EAAE,MAAM,OAAO;AAAA,QACvB,CAAC;AAEDC,sBAAE,KAAC,YAAW;AAEdA,sBAAAA,KAAG,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA;AAAA,EAAY,SAAS;AAAA,UAC9B,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAChBD,qCAAW,gBAAgB,SAAS;AAAA,YACrC;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACA,SAAO,OAAO;AACfC,sBAAE,KAAC,YAAW;AACdA,sBAAAA,KAAG,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,MAAM,oBAAoB;AACzBA,oBAAAA,KAAG,YAAY;AAAA,QACd,OAAO;AAAA,MACR,CAAC;AAED,UAAI;AACH,cAAM,YAAY,MAAMD,YAAU,WAAC,kBAAkB;AAAA,UACpD,MAAM;AAAA,UACN,OAAO,EAAE,MAAM,OAAO;AAAA,QACvB,CAAC;AAEDC,sBAAE,KAAC,YAAW;AAEdA,sBAAAA,KAAG,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,SAAS;AAAA;AAAA,EAAY,SAAS;AAAA,UAC9B,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAChBD,qCAAW,gBAAgB,SAAS;AAAA,YACrC;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACA,SAAO,OAAO;AACfC,sBAAE,KAAC,YAAW;AACdA,sBAAAA,KAAG,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,eAAe;AACd,YAAM,eAAe;AACrB,YAAM,cAAc,aAAa,aAAa,SAAS,CAAC;AACxD,YAAM,OAAO,YAAY;AACzBD,kBAAU,WAAC,gBAAgB,SAAS,IAAI,IAAI,SAAS;AAAA,IACrD;AAAA,IAED,iBAAiB;AAChBA,kBAAAA,WAAW,eAAe;AAAA,QACzB,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACF;AAAA,EACD;;;;;;;;;;;;;;;;;;;;;;;;AC9MF,GAAG,WAAW,eAAe;"}