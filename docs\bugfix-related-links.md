# 相关链接功能 Bug 修复

## 问题描述

在从 index 页面跳转到 category 页面时出现错误：
```
TypeError: parentRelatedLinks.join is not a function
```

## 问题原因

### 1. 数据类型不匹配
- **API 返回的数据**：`relatedLinks` 字段是字符串类型
- **代码中的假设**：认为 `relatedLinks` 是数组类型
- **错误操作**：对字符串调用了 `.join()` 方法

### 2. 具体错误位置
```javascript
// 错误的代码
const parentRelatedLinks = item.relatedLinks || [];  // relatedLinks 是字符串，不是数组
parentRelatedLinks.join(',')  // 字符串没有 join 方法
```

### 3. 数据格式分析
根据实际 API 数据：
```javascript
{
  lianjie: "https://mp.weixin.qq.com/s/miJkoYv_Ytp56Gr_0LiT9Q"  // 字符串格式
}
```

## 修复方案

### 1. 修复 index 页面 (pages/index/index.vue)
```javascript
// 修复前
const parentRelatedLinks = item.relatedLinks || [];
parentRelatedLinks.join(',')

// 修复后
const parentRelatedLinksStr = item.relatedLinks || '';
parentRelatedLinksStr  // 直接使用字符串
```

### 2. 修复 category 页面 (pages/category/category.vue)
```javascript
// 修复前
const parentRelatedLinks = item.relatedLinks || [];
parentRelatedLinks.join(',')

// 修复后
const parentRelatedLinksStr = item.relatedLinks || '';
parentRelatedLinksStr  // 直接使用字符串
```

## 修复后的完整流程

### 1. 数据流转
```
API 数据 (字符串) → index 页面 → category 页面 → 解析为数组
```

### 2. 具体步骤
1. **API 返回**：`lianjie: "链接1,链接2,链接3"`
2. **index 传递**：直接传递字符串
3. **category 接收**：接收字符串参数
4. **category 解析**：使用 `split(',')` 转换为数组
5. **页面显示**：正常显示链接列表

### 3. URL 参数传递
```javascript
// 修复后的 URL 构建
const categoryUrl = `/pages/category/category?
  title=${encodeURIComponent(item.productName)}&
  parentRelatedLinks=${encodeURIComponent(parentRelatedLinksStr)}&
  ...其他参数
`;
```

## 测试验证

### 1. 测试用例
- **单个链接**：`"https://mp.weixin.qq.com/s/abc123"`
- **多个链接**：`"链接1,链接2,链接3"`
- **空数据**：`""` 或 `null`

### 2. 预期结果
- 不再出现 `join is not a function` 错误
- 页面跳转正常
- 相关链接正确显示

### 3. 验证步骤
1. 从 index 页面点击有相关链接的分类
2. 检查控制台是否有错误
3. 确认 category 页面正常显示
4. 验证相关链接区域是否正确显示

## 相关文件修改

### 1. pages/index/index.vue
- **行号**：699-704
- **修改**：将 `parentRelatedLinks` 改为 `parentRelatedLinksStr`
- **影响**：修复跳转到 category 页面的错误

### 2. pages/category/category.vue  
- **行号**：1147-1153
- **修改**：将 `parentRelatedLinks` 改为 `parentRelatedLinksStr`
- **影响**：修复从 category 页面跳转到子 category 页面的错误

## 数据处理逻辑

### 1. 字符串到数组的转换
```javascript
// 在 category 页面的数据解析中
const relatedLinksStr = decodeURIComponent(options.parentRelatedLinks || '');
if (relatedLinksStr) {
    this.currentRelatedLinks = relatedLinksStr.split(',')
        .map(link => link.trim())
        .filter(link => link.length > 0);
}
```

### 2. 数组到字符串的转换
```javascript
// 在页面跳转时
const parentRelatedLinksStr = item.relatedLinks || '';  // 已经是字符串，直接使用
```

## 防止类似问题

### 1. 类型检查
```javascript
// 建议添加类型检查
const parentRelatedLinksStr = typeof item.relatedLinks === 'string' 
    ? item.relatedLinks 
    : (Array.isArray(item.relatedLinks) ? item.relatedLinks.join(',') : '');
```

### 2. 数据验证
```javascript
// 在关键位置添加数据验证
console.log('相关链接数据类型:', typeof item.relatedLinks);
console.log('相关链接数据内容:', item.relatedLinks);
```

### 3. 错误处理
```javascript
// 添加 try-catch 保护
try {
    const categoryUrl = `...&parentRelatedLinks=${encodeURIComponent(parentRelatedLinksStr)}`;
    uni.navigateTo({ url: categoryUrl });
} catch (error) {
    console.error('跳转失败:', error);
    // 降级处理
}
```

## 总结

这个 bug 的根本原因是对数据类型的错误假设。API 返回的 `relatedLinks` 字段是字符串格式，但代码中错误地将其当作数组处理。

修复方案很简单：
1. 认识到 `relatedLinks` 是字符串
2. 在传递时直接使用字符串
3. 在接收时将字符串解析为数组

这个修复确保了数据流转的一致性，避免了类型转换错误。
