<script>
	import ShareUtils from '@/utils/share.js';

	export default {
		onLaunch: function() {
			console.log('App Launch');

			// 设置全局分享配置
			this.globalData.shareConfig = {
				title: '熙迈科技服务有限公司 - 专业工业服务',
				path: 'pages/home/<USER>',
				imageUrl: '/static/熙迈LOGO.png'
			};
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},

		globalData: {
			shareConfig: {}
		}
	}
</script>

<style>
	.fixed-customer-service {
		position: fixed;
		bottom: 40rpx;
		left: 50%;
		transform: translateX(-50%);
		z-index: 999;
		width: 700rpx;
	}
	
	.service-btn {
		display: flex;
		align-items: center;
		padding: 10rpx;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		border-radius: 24rpx;
		box-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
	}
	
	.text-container {
		flex: 1;
	}
	
	.btn-text {
		color: #fff;
		font-size: 38rpx;
		font-weight: 600;
		text-align: center;
		display: block;
		margin-bottom: 0;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}
	
	.btn-subtext {
		color: rgba(255, 255, 255, 0.9);
		font-size: 24rpx;
		line-height: 1.6;
		text-align: center;
		display: block;
		opacity: 0.9;
		margin-bottom: 16rpx;
	}
	
	/* 交互动画 */
	.service-btn:active {
		transform: scale(0.98) translateY(2rpx);
		box-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);
	}
	
	
	/* 悬浮呼吸动画 */
	@keyframes float {
		0% {
			transform: translateY(0);
		}
	
		50% {
			transform: translateY(-10rpx);
		}
	
		100% {
			transform: translateY(0);
		}
	}
	
	.service-btn {
		animation: float 3s ease-in-out infinite;
	}
	
	/* 流光边框效果 */
	.service-btn::after {
		content: '';
		position: absolute;
		top: -1rpx;
		left: -1rpx;
		right: -1rpx;
		bottom: -1rpx;
		background: linear-gradient(45deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.3) 50%,
				rgba(255, 255, 255, 0) 100%);
		border-radius: 24rpx;
		animation: shine 3s infinite;
		z-index: -1;
	}
	
	@keyframes shine {
		0% {
			opacity: 0;
			left: -50%;
		}
	
		50% {
			opacity: 0.4;
		}
	
		100% {
			opacity: 0;
			left: 150%;
		}
	}
	.service-btn {
		display: flex;
		align-items: center;
		padding: 10rpx;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		border-radius: 24rpx;
		box-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
	}
	
	.text-container {
		flex: 1;
	}
	
	.btn-text {
		color: #fff;
		font-size: 38rpx;
		font-weight: 600;
		text-align: center;
		display: block;
		margin-bottom: 0;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}
	
	.btn-subtext {
		color: rgba(255, 255, 255, 0.9);
		font-size: 24rpx;
		line-height: 1.6;
		text-align: center;
		display: block;
		opacity: 0.9;
		margin-bottom: 16rpx;
	}
	
	/* 交互动画 */
	.service-btn:active {
		transform: scale(0.98) translateY(2rpx);
		box-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);
	}
	
	
	/* 悬浮呼吸动画 */
	@keyframes float {
		0% {
			transform: translateY(0);
		}
	
		50% {
			transform: translateY(-10rpx);
		}
	
		100% {
			transform: translateY(0);
		}
	}
	
	.service-btn {
		animation: float 3s ease-in-out infinite;
	}
	
	/* 流光边框效果 */
	.service-btn::after {
		content: '';
		position: absolute;
		top: -1rpx;
		left: -1rpx;
		right: -1rpx;
		bottom: -1rpx;
		background: linear-gradient(45deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.3) 50%,
				rgba(255, 255, 255, 0) 100%);
		border-radius: 24rpx;
		animation: shine 3s infinite;
		z-index: -1;
	}
	
	@keyframes shine {
		0% {
			opacity: 0;
			left: -50%;
		}
	
		50% {
			opacity: 0.4;
		}
	
		100% {
			opacity: 0;
			left: 150%;
		}
	}

</style>
