<template>
	<view class="container">
		<view class="logo-container">
			<image class="logo" src="/static/DN&XM.png" mode="aspectFit"></image>
		</view>
		<view class="main-button-group">
			<view class="button-item" v-for="(item, index) in mainButtons" :key="index"
				@click="handleButtonClick(item)">
				<uni-icons :type="item.icon" size="30" color="#2979FF"></uni-icons>
				<view class="button-text-row">
					<text class="button-text-main">{{ item.text }}</text>
					<image src="/static/小手.svg" class="hand-icon" mode="widthFix" />
				</view>
			</view>
		</view>

		<view class="sub-button-group">
			<view class="button-item" v-for="(item, index) in subButtons" :key="index" @click="handleButtonClick(item)">
				<uni-icons :type="item.icon" size="30" color="#2979FF"></uni-icons>
				<text class="button-text">{{ item.text }}</text>
			</view>
		</view>

		<!-- 轮播图 -->
		<swiper class="banner" circular autoplay interval="5000" indicator-dots="true" indicator-color="#007aff">
			<swiper-item v-for="(item, index) in banners" :key="index">
				<image :src="item.image" mode="aspectFill" class="banner-image"
					@click="handleBannerClick($event,item.url)"></image>
			</swiper-item>
		</swiper>
		<!-- 公告栏 -->
		<view class="notice">
			<uni-icons type="sound" size="18" color="#666"></uni-icons>
			<swiper class="notice-swiper" vertical autoplay circular interval="3000">
				<swiper-item v-for="(item, index) in notices" :key="index">
					<text class="notice-text">{{ item }}</text>
				</swiper-item>
			</swiper>
		</view>
		<!-- 联系我们 -->
		<view class="contact" v-for="(info, index) in contactInfos" :key="index">
			<text class="contact-title">{{ info.title }}</text>
			<text class="contact-info" v-for="(phone, i) in info.phones" :key="i" @click="callnum(phone)">
				电话{{ i + 1 }}：{{ phone }}
			</text>
			<text class="contact-info"
				@click="Location(info.latitude, info.longitude, info.address)">地址：{{ info.address }}</text>
			<text class="contact-info">邮箱：{{ info.email }}</text>
		</view>
	</view>

	<view class="fixed-customer-service">
		<button class="service-btn"  @click="handleContact" >
			<view class="text-container">
				<text class="btn-text">微信客服</text>
				<text class="btn-subtext">如有需求，请点我联系</text>
			</view>
		</button>
	</view>

	<!-- 弹窗轮播图 -->
	<view v-if="showPopup" class="popup-overlay" @click="closePopup">
		<view class="popup-content" @click.stop>
			<view class="popup-close" @click="closePopup">
				<text class="close-icon">×</text>
			</view>
			<swiper
				class="popup-swiper"
				:indicator-dots="popupList.length > 1"
				:autoplay="false"
				:circular="true"
				indicator-color="rgba(41, 121, 255, 0.3)"
				indicator-active-color="#2979FF"
				@change="onSwiperChange"
			>
				<swiper-item
					v-for="(popup, index) in popupList"
					:key="index"
					class="popup-swiper-item"
				>
					<image
						:src="popup.imageUrl"
						class="popup-image"
						mode="aspectFit"
						@click="handlePopupClick(popup)"
						@load="handleImageLoad"
					/>
					<view v-if="popup.title" class="popup-title">{{ popup.title }}</view>
				</swiper-item>
			</swiper>
			<!-- 轮播图指示器文字 -->
			<view v-if="popupList.length > 1" class="popup-indicator">
				{{ currentPopupIndex + 1 }} / {{ popupList.length }}
			</view>
		</view>
	</view>

</template>

<script>
	import ShareUtils from '@/utils/share.js';

	export default {
		data() {
			return {
				showPopup: false,
				popupList: [], // 弹窗列表
				currentPopupIndex: 0, // 当前弹窗索引
				popupData: {
					title: '',
					imageUrl: '',
					jumpUrl: '',
					isActive: false
				},
				notices: [
					"欢迎使用本小程序，有问题请及时联系我们"
				],
				// 修改为两个按钮数组
				mainButtons: [{
					icon: "index",
					text: "熙迈科技工业服务-点击了解更多",
					url: "/pages/index/index"
				}],
				subButtons: [{
						icon: "GZH",
						text: "公众号"
					},
					{
						icon: "SPH",
						text: "视频号"
					},

					// {
					// 	icon: "PMS",
					// 	text: "PMS",
					// 	url: "https://dmit.duoningbio.com/app/3f976431-6007-4fa7-ad78-14cc163f5b66?ch=no&ac=no"
					// },
					// {
					// 	icon: "BBS",
					// 	text: "BBS",
					// 	url: "http://cmbbs.duoningbio.com:5443/"
					// },
					// {
					// 	icon: "GSGW",
					// 	text: "公司官网",
					// 	url: "http://www.smile-tech.top/"
					// }
				],
				banners: [{
						image: "https://cdn.yun.sooce.cn/6/45743/jpg/17081760583573cc2409e377f7e2779502a46c7c54cb0.jpg?imageMogr2/thumbnail/1800x&version=1708176059",
						url: "https://cdn.yun.sooce.cn/6/45743/jpg/17081760583573cc2409e377f7e2779502a46c7c54cb0.jpg?imageMogr2/thumbnail/1800x&version=1708176059"
					},
					{
						image: "https://cdn.yun.sooce.cn/6/45743/jpg/1708175809120f6c32e3ee03340d47e26d93bf4dad34b.jpg?imageMogr2/thumbnail/1800x&version=1708175811",
						url: "https://cdn.yun.sooce.cn/6/45743/jpg/1708175809120f6c32e3ee03340d47e26d93bf4dad34b.jpg?imageMogr2/thumbnail/1800x&version=1708175811" // 添加跳转路径
					},
					{
						image: "https://cdn.yun.sooce.cn/6/45743/jpg/1708175809193fc9e25a917635857492cc13c0f6b7173.jpg?imageMogr2/thumbnail/1800x&version=1708175811",
						url: "https://cdn.yun.sooce.cn/6/45743/jpg/1708175809193fc9e25a917635857492cc13c0f6b7173.jpg?imageMogr2/thumbnail/1800x&version=1708175811" // 添加跳转路径
					},
					{
						image: "https://cdn.yun.sooce.cn/6/45743/jpg/1708176038714046bd3b4360f5077916b30961fe86281.jpg?imageMogr2/thumbnail/1800x&version=1708176040",
						url: "https://cdn.yun.sooce.cn/6/45743/jpg/1708176038714046bd3b4360f5077916b30961fe86281.jpg?imageMogr2/thumbnail/1800x&version=1708176040" // 添加跳转路径
					},
					{
						image: "https://cdn.yun.sooce.cn/6/45743/jpg/17081758091926bc682f48a953fa738b953441c01be09.jpg?imageMogr2/thumbnail/1800x&version=1708175811",
						url: "https://cdn.yun.sooce.cn/6/45743/jpg/17081758091926bc682f48a953fa738b953441c01be09.jpg?imageMogr2/thumbnail/1800x&version=1708175811" // 添加跳转路径
					}
				],
				contactInfos: [{
						title: "上海松江总部",
						phones: ["************", "021-68775023"],
						address: "上海市松江区新桥镇民强路1525号30幢3层",
						email: "<EMAIL>",
						latitude: 31.038129,
						longitude: 121.290108
					},
					{
						title: "华东服务中心",
						phones: ["************", "021-68775023"],
						address: "上海市浦东新区川沙路669号曹路创意空间622-626室",
						email: "<EMAIL>",
						latitude: 31.275257,
						longitude: 121.670465
					},
					{
						title: "西南服务中心",
						phones: ["************", "18980586458"],
						address: "四川省成都市武侯区桂溪街道蜀都中心二期1号楼3单元701室",
						email: "<EMAIL>",
						latitude: 30.553102,
						longitude: 104.065304
					},
					// {
					// 	title: "华南服务中心",
					// 	phones: ["400-0000-000", "13450476532"],
					// 	address: "深圳市龙华区民治街道民乐社区星河WORLD2期E栋22层EA10",
					// 	email: "<EMAIL>",
					// 	latitude: 22.604259,
					// 	longitude: 114.057841
					// },
					{
						title: "华北服务中心",
						phones: ["4000885153", "18686480987"],
						address: "沈阳市和平区红椿路38-6号1-1-2",
						email: "<EMAIL>",
						latitude: 41.697049,
						longitude: 123.385829
					}
				]
			};
		},
		// 分享功能
		onShareAppMessage(res) {
			console.log('分享给好友', res);
			return ShareUtils.getDefaultShareConfig({
				title: '熙迈科技服务有限公司 - SMILE',
				path: 'pages/home/<USER>',
				imageUrl: '/static/熙迈LOGO.png'
			});
		},

		onShareTimeline(res) {
			console.log('分享到朋友圈', res);
			return ShareUtils.getDefaultShareConfig({
				title: '熙迈科技服务有限公司 - SMILE',
				path: 'pages/home/<USER>',
				imageUrl: '/static/熙迈LOGO.png'
			});
		},

		onLoad() {
			// 页面加载时获取弹窗数据
			this.loadPopupData();
		},

		methods: {
			// 加载弹窗数据
			async loadPopupData() {
				try {
					console.log('开始加载弹窗数据');
					const response = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',
						method: 'POST',
						data: {
							appKey: '984e1ff028f80150',
							sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
							worksheetId: 'xcxdcwh',
							pageSize: 10, // 获取更多弹窗数据
							pageIndex: 1,
							listType: 0,
							controls: [],
							filters: [{
								controlId: 'isActive',
								dataType: 36, // 复选框类型
								spliceType: 1,
								filterType: 2, // 等于
								value: '1' // 选中状态
							}]
						},
						header: {
							'Content-Type': 'application/json'
						}
					});



					if (response.data && response.data.data && response.data.data.rows && response.data.data.rows.length > 0) {
						// 获取所有激活的弹窗数据，并过滤出在有效期内的
						const activePopups = response.data.data.rows.filter(row => {
							const now = new Date();
							const startDate = new Date(row.startDate);
							const endDate = new Date(row.endDate);
							return now >= startDate && now <= endDate;
						});

						if (activePopups.length > 0) {
							// 处理所有有效的弹窗数据
							this.popupList = activePopups.map(row => {
								return {
									title: row.title || '',
									jumpUrl: row.jumpUrl || '',
									imageUrl: this.getImageUrl(row.imageUrl),
									startDate: row.startDate || '',
									endDate: row.endDate || '',
									isActive: row.isActive === '1'
								};
							});

							// 设置第一个弹窗为当前弹窗（兼容旧逻辑）
							this.popupData = this.popupList[0];
							this.currentPopupIndex = 0;
							this.showPopup = true;
						}
					} else {
						console.log('没有找到有效的弹窗数据');
					}
				} catch (error) {
					console.error('加载弹窗数据失败:', error);
				}
			},

			// 检查弹窗是否在有效期内
			isPopupValid() {
				if (!this.popupData.isActive) {
					return false;
				}

				const now = new Date();
				const startDate = this.popupData.startDate ? new Date(this.popupData.startDate) : null;
				const endDate = this.popupData.endDate ? new Date(this.popupData.endDate) : null;

				// 如果有开始时间，检查是否已开始
				if (startDate && now < startDate) {
					return false;
				}

				// 如果有结束时间，检查是否已结束
				if (endDate && now > endDate) {
					return false;
				}

				return true;
			},

			// 处理图片URL
			getImageUrl(imageData) {
				try {
					// 支持直接传入数组或JSON字符串
					const parsedData = Array.isArray(imageData) ? imageData : JSON.parse(imageData);

					if (!Array.isArray(parsedData)) {
						console.warn("图片数据格式错误");
						return ''; // 弹窗没有图片时返回空字符串
					}

					// 获取第一个图片对象
					if (parsedData.length > 0) {
						const firstImage = parsedData[0];

						// 直接使用 preview_url
						if (firstImage.preview_url) {
							return firstImage.preview_url;
						}
					}

					return '';

				} catch (error) {
					console.error("解析图片URL失败:", error.message);
					return ''; // 弹窗解析失败时返回空字符串
				}
			},

			// 关闭弹窗
			closePopup() {
				this.showPopup = false;
			},

			// 轮播图切换事件
			onSwiperChange(e) {
				this.currentPopupIndex = e.detail.current;
				// 更新当前弹窗数据（兼容旧逻辑）
				this.popupData = this.popupList[this.currentPopupIndex];
			},

			// 处理弹窗点击
			handlePopupClick(popup) {
				// 如果传入了popup参数，使用传入的数据，否则使用当前弹窗数据
				const currentPopup = popup || this.popupData;

				if (!currentPopup.jumpUrl) {
					console.log('没有跳转链接');
					return;
				}



				// 检查是否是小程序链接，如果是就直接处理
				if (currentPopup.jumpUrl.startsWith('#小程序://')) {
					// 解析小程序链接并直接跳转
					const match = currentPopup.jumpUrl.match(/#小程序:\/\/([^\/]+)\/(.+)/);
					if (match) {
						const appName = match[1];
						const path = match[2];

						// 使用wx.navigateTo跳转
						wx.navigateTo({
							url: `${path}`,
							success: (res) => {
								console.log('跳转成功:', res);
							},
							fail: (err) => {
								console.error('跳转失败:', err);
								// 降级方案：跳转到首页
								uni.navigateTo({
									url: '/pages/index/index'
								});
							}
						});
					}
				} else if (currentPopup.jumpUrl.startsWith('/pages/')) {
					// 内部页面跳转
					uni.navigateTo({
						url: currentPopup.jumpUrl
					});
				} else if (currentPopup.jumpUrl.startsWith('http')) {
					// 外部链接
					uni.navigateTo({
						url: `/pages/WebView/WebView?url=${encodeURIComponent(currentPopup.jumpUrl)}`
					});
				}

				// 点击后关闭弹窗
				this.closePopup();
			},

			// 处理小程序链接
			handleMiniProgramLink(link) {
				console.log('处理小程序链接:', link);

				// 解析小程序链接格式：#小程序://小程序名称/path
				const match = link.match(/#小程序:\/\/([^\/]+)\/(.+)/);

				if (match) {
					const appName = match[1];
					const path = match[2];

					console.log('解析结果:', { appName, path });

					// 显示确认对话框
					uni.showModal({
						title: '跳转小程序',
						content: `即将跳转到"${appName}"小程序，请确认是否继续？`,
						confirmText: '确认跳转',
						cancelText: '取消',
						success: (res) => {
							if (res.confirm) {
								// 用户确认后，尝试生成短链接并跳转
								this.generateShortLinkAndJump(appName, path, link);
							}
						}
					});
				} else {
					console.error('小程序链接格式不正确:', link);
					uni.showToast({
						title: '链接格式错误',
						icon: 'none'
					});
				}
			},

			// 生成短链接并跳转
			async generateShortLinkAndJump(appName, path, originalLink) {
				// 根据小程序名称获取对应的appId
				const appIdMap = {
					'熙迈科技-SMILETECH': 'wx6ebba6c9d8c21fb1',
					// 可以添加更多小程序的映射
				};

				const appId = appIdMap[appName];

				if (!appId) {
					console.error('未配置小程序appId:', appName);
					uni.showModal({
						title: '跳转失败',
						content: '该小程序暂未配置，请联系管理员添加配置。',
						showCancel: false,
						confirmText: '知道了'
					});
					return;
				}

				try {
					console.log('开始生成短链接...');

					// 使用微信API生成短链接
					const shortLinkRes = await new Promise((resolve, reject) => {
						wx.generateShortLink({
							pageUrl: `pages/index/index?shareCode=${encodeURIComponent(path)}`,
							pageTitle: `${appName}产品详情`,
							isPermanent: false, // 临时链接
							success: resolve,
							fail: reject
						});
					});

					console.log('短链接生成成功:', shortLinkRes);

					if (shortLinkRes.link) {
						// 复制短链接到剪贴板
						uni.setClipboardData({
							data: shortLinkRes.link,
							success: () => {
								uni.showModal({
									title: '链接已生成',
									content: '短链接已复制到剪贴板，请在微信中打开。',
									showCancel: false,
									confirmText: '知道了'
								});
							}
						});
					} else {
						throw new Error('短链接生成失败');
					}

				} catch (error) {
					console.error('生成短链接失败:', error);

					// 降级方案：直接尝试小程序跳转
					console.log('降级为直接跳转方案');
					this.attemptMiniProgramJump(appName, path);
				}
			},

			// 尝试跳转小程序
			attemptMiniProgramJump(appName, path) {
				// 根据小程序名称获取对应的appId
				const appIdMap = {
					'熙迈科技-SMILETECH': 'wx6ebba6c9d8c21fb1', // 熙迈科技小程序的真实appId
					// 可以添加更多小程序的映射
				};

				const appId = appIdMap[appName];

				if (!appId) {
					console.error('未配置小程序appId:', appName);
					uni.showModal({
						title: '跳转失败',
						content: '该小程序暂未配置，请联系管理员添加配置。',
						showCancel: false,
						confirmText: '知道了'
					});
					return;
				}

				console.log('使用appId:', appId);
				console.log('原始path:', path);

				// 处理特殊的分享码格式
				let targetPath = '';

				// 如果path看起来像分享码（短字符串），则跳转到首页
				if (path && path.length < 20 && !/^pages\//.test(path)) {
					console.log('检测到分享码格式，跳转到首页');
					targetPath = path; // 跳转到首页
				} else if (path && path.startsWith('pages/')) {
					targetPath = path;
				} else {
					// 默认跳转到首页
					targetPath = 'pages/index/index';
				}

				console.log('最终跳转路径:', targetPath);

				// 使用微信API打开小程序
				console.log('准备跳转小程序，参数:', {
					appId: appId,
					path: targetPath,
					envVersion: 'release'
				});

				wx.navigateToMiniProgram({
					appId: appId,
					path: targetPath,
					extraData: {
						// 可以传递额外数据
						shareCode: path // 将原始分享码作为额外数据传递
					},
					envVersion: 'release', // 正式版
					success: (res) => {
						console.log('跳转小程序成功:', res);
					},
					fail: (err) => {
						console.error('跳转小程序失败:', err);
						console.error('错误详情:', JSON.stringify(err));

						// 提供更友好的错误提示
						let errorMsg = '跳转失败';
						let content = '无法跳转到目标小程序';

						if (err.errMsg.includes('appId') || err.errMsg.includes('invalid')) {
							errorMsg = '配置错误';
							content = '小程序配置有误，请联系管理员检查appId配置。';
						} else if (err.errMsg.includes('permission')) {
							errorMsg = '权限不足';
							content = '当前小程序没有跳转到目标小程序的权限。';
						} else if (err.errMsg.includes('not exist')) {
							errorMsg = '小程序不存在';
							content = '目标小程序不存在或已下线。';
						}

						// 提供备用方案：复制链接
						uni.showModal({
							title: errorMsg,
							content: content + '\n\n是否复制小程序链接到剪贴板？',
							confirmText: '复制链接',
							cancelText: '取消',
							success: (modalRes) => {
								if (modalRes.confirm) {
									// 复制原始链接到剪贴板
									uni.setClipboardData({
										data: link,
										success: () => {
											uni.showToast({
												title: '链接已复制',
												icon: 'success'
											});
										}
									});
								}
							}
						});
					}
				});
			},

			// 图片加载成功处理
			handleImageLoad(e) {
				// 图片加载成功，无需特殊处理
			},

			// 图片加载失败处理
			handleImageError(e) {
				console.log('图片加载失败:', e);
				console.log('失败的图片URL:', this.popupData.imageUrl);
				uni.showToast({
					title: '图片加载失败',
					icon: 'none',
					duration: 2000
				});
			},

			handleContact(e) {
				wx.openCustomerServiceChat({
					extInfo: {
						url: 'https://work.weixin.qq.com/kfid/kfcaf7fbb93aa905a54'
					},
					corpId: 'wwa76e36d25343b6b9',
					success(res) {
					}
				})
			},
			handleButtonClick(item) {
				if (item.text === "公众号") {
					wx.openOfficialAccountProfile({
						username: 'gh_42bacb18625e',
						success: res => {
						},
						fail: res => {
						}
					})
				} else if (item.icon === "PMS" || item.icon === "BBS" || item.icon === "GSGW") {
					if (!item.url) {
						console.error('URL is undefined for button:', item.text);
						return;
					}
					// 修改为跳转到 WebView 页面
					uni.navigateTo({
						url: `/pages/WebView/WebView?url=${encodeURIComponent(item.url)}`
					});
				} else if (item.text === "视频号") {
					wx.openChannelsUserProfile({
						finderUserName: "sph1phDRUOAw9ds",
					});
				} else if (item.icon === "index") {

					if (!item.url) {
						console.error('url is undefined for button:', item.text);
						return;
					}
					uni.navigateTo({
						url: item.url
					});
				}
			},
			callnum(phone) {
				if (phone) {
					uni.makePhoneCall({
						phoneNumber: phone
					});
				}
			},
			Location(latitude, longitude, name) {
				wx.openLocation({
					latitude: latitude,
					longitude: longitude,
					scale: 18,
					name: name
				});
			},
			handleBannerClick(e, url) {
				console.log(e, url, 'url')
				// 获取当前点击的轮播图索引
				const current = e.detail.current;
				// 跳转到对应页面
				uni.navigateTo({
					url: url
				});
			}
		}
	};
</script>

<style scoped>
	page {
		font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
	}

	.logo-container {
		padding: 20rpx 0 0 20rpx;
		margin-bottom: 20rpx;
		text-align: center;
	}

	.logo {
		width: 500rpx;
		height: 80rpx;
		filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
	}

	/* 调整原有容器间距 */
	.container {
		padding: 20rpx;
		padding-bottom: 240rpx;
		padding-top: 120rpx;
		background: linear-gradient(180deg, #f8f9ff 0%, #f0f2ff 100%);
		position: relative;
	}

	.container {
		padding: 20rpx;
		padding-bottom: 240rpx;
		background: linear-gradient(180deg, #f8f9ff 0%, #f0f2ff 100%);
	}

	.main-button-group .button-item {
		position: relative;
		overflow: hidden;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		box-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);
		border-radius: 24rpx;
		padding: 32rpx 0;
		transition: all 0.3s ease;
		animation: buttonFloat 3s ease-in-out infinite;
	}

	.main-button-group .button-text,
	.main-button-group .uni-icons {
		color: #fff !important;
		z-index: 2;
		position: relative;
	}

	/* 添加呼吸动画 */

	@keyframes buttonFloat {

		0%,
		100% {
			transform: translateY(0);
		}

		50% {
			transform: translateY(-10rpx);
		}
	}

	/* 流光边框效果 */
	.main-button-group .button-item::after {
		content: '';
		position: absolute;
		top: -1rpx;
		left: -1rpx;
		right: -1rpx;
		bottom: -1rpx;
		background: linear-gradient(45deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.4) 50%,
				rgba(255, 255, 255, 0) 100%);
		animation: buttonShine 3s infinite;
		z-index: 1;
	}

	@keyframes buttonShine {
		0% {
			opacity: 0;
			left: -50%;
		}

		50% {
			opacity: 0.4;
		}

		100% {
			opacity: 0;
			left: 150%;
		}
	}

	.main-button-group .button-item:hover {
		transform: scale(1.05);
		box-shadow: 0 16rpx 40rpx rgba(41, 121, 255, 0.4);
	}

	/* 点击效果优化 */
	.main-button-group .button-item:active {
		transform: scale(0.98) translateY(2rpx);
		box-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);
	}

	/* 公告栏 - 添加闪烁动画 */
	.notice {
		display: flex;
		align-items: center;
		padding: 20rpx;
		background-color: #fffbe6;
		border-radius: 10rpx;
		margin-bottom: 20rpx;
	}

	.notice-swiper {
		height: 40rpx;
		flex: 1;
		margin-left: 20rpx;
	}

	.notice-text {
		font-size: 26rpx;
		color: #666;
	}


	/* 按钮组 - 使用网格布局 */
	.main-button-group {
		display: grid;
		grid-template-columns: 1fr;
		gap: 20rpx;
		margin-bottom: 20rpx;
	}

	.sub-button-group {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 20rpx;
		margin-bottom: 40rpx;
	}

	.button-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background: #fff;
		border-radius: 24rpx;
		padding: 32rpx 0;
		transition: all 0.3s ease;
		box-shadow: 0 6rpx 20rpx rgba(41, 121, 255, 0.1);
	}

	.button-item:active {
		transform: scale(0.98);
		background: #f5f7ff;
	}

	.uni-icons {
		margin-bottom: 16rpx;
	}

	.button-text-main {
		font-size: 40rpx;
		color: #ffffff;
		font-weight: 500;
	}

	.button-text {
		font-size: 26rpx;
		color: #333;
		font-weight: 500;
	}

	/* 轮播图 - 优化指示器 */
	.banner {
		height: 320rpx;
		border-radius: 24rpx;
		overflow: hidden;
		margin-bottom: 24rpx;
		margin-top: 40rpx;
		position: relative;
	}

	.banner-image {
		width: 100%;
		height: 100%;
		transition: transform 0.3s ease;
	}

	.banner swiper-item:hover .banner-image {
		transform: scale(1.02);
	}

	/* 联系我们 - 卡片式设计 */
	.contact {
		background: #fff;
		padding: 32rpx;
		border-radius: 24rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.05);
		margin-bottom: 20rpx;
	}

	.contact-title {
		font-size: 34rpx;
		color: #1a1a1a;
		font-weight: 600;
		margin-bottom: 24rpx;
		position: relative;
		padding-left: 20rpx;
	}

	.contact-title::before {
		content: "";
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 8rpx;
		height: 32rpx;
		background: #2979FF;
		border-radius: 4rpx;
	}

	.contact-info {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: #666;
		margin-bottom: 16rpx;
		padding: 12rpx 0;
		border-bottom: 1rpx solid #eee;
	}

	.contact-info:last-child {
		border-bottom: none;
	}

	.fixed-customer-service {
		position: fixed;
		bottom: 40rpx;
		left: 50%;
		transform: translateX(-50%);
		z-index: 999;
		width: 700rpx;
	}



	.service-btn {
		display: flex;
		align-items: center;
		padding: 10rpx;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		border-radius: 24rpx;
		box-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
	}

	.text-container {
		flex: 1;
	}

	.btn-text {
		color: #fff;
		font-size: 38rpx;
		font-weight: 600;
		text-align: center;
		display: block;
		margin-bottom: 0;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.btn-subtext {
		color: rgba(255, 255, 255, 0.9);
		font-size: 24rpx;
		line-height: 1.6;
		text-align: center;
		display: block;
		opacity: 0.9;
		margin-bottom: 16rpx;
	}

	/* 交互动画 */
	.service-btn:active {
		transform: scale(0.98) translateY(2rpx);
		box-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);
	}


	/* 悬浮呼吸动画 */
	@keyframes float {
		0% {
			transform: translateY(0);
		}

		50% {
			transform: translateY(-10rpx);
		}

		100% {
			transform: translateY(0);
		}
	}

	.service-btn {
		animation: float 3s ease-in-out infinite;
	}

	/* 流光边框效果 */
	.service-btn::after {
		content: '';
		position: absolute;
		top: -1rpx;
		left: -1rpx;
		right: -1rpx;
		bottom: -1rpx;
		background: linear-gradient(45deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.3) 50%,
				rgba(255, 255, 255, 0) 100%);
		border-radius: 24rpx;
		animation: shine 3s infinite;
		z-index: -1;
	}

	@keyframes shine {
		0% {
			opacity: 0;
			left: -50%;
		}

		50% {
			opacity: 0.4;
		}

		100% {
			opacity: 0;
			left: 150%;
		}
	}

	.service-btn {
		display: flex;
		align-items: center;
		padding: 10rpx;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		border-radius: 24rpx;
		box-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
	}

	.text-container {
		flex: 1;
	}

	.btn-text {
		color: #fff;
		font-size: 38rpx;
		font-weight: 600;
		text-align: center;
		display: block;
		margin-bottom: 0;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.btn-subtext {
		color: rgba(255, 255, 255, 0.9);
		font-size: 24rpx;
		line-height: 1.6;
		text-align: center;
		display: block;
		opacity: 0.9;
		margin-bottom: 16rpx;
	}

	/* 交互动画 */
	.service-btn:active {
		transform: scale(0.98) translateY(2rpx);
		box-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);
	}


	/* 悬浮呼吸动画 */
	@keyframes float {
		0% {
			transform: translateY(0);
		}

		50% {
			transform: translateY(-10rpx);
		}

		100% {
			transform: translateY(0);
		}
	}

	.service-btn {
		animation: float 3s ease-in-out infinite;
	}

	/* 流光边框效果 */
	.service-btn::after {
		content: '';
		position: absolute;
		top: -1rpx;
		left: -1rpx;
		right: -1rpx;
		bottom: -1rpx;
		background: linear-gradient(45deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.3) 50%,
				rgba(255, 255, 255, 0) 100%);
		border-radius: 24rpx;
		animation: shine 3s infinite;
		z-index: -1;
	}

	@keyframes shine {
		0% {
			opacity: 0;
			left: -50%;
		}

		50% {
			opacity: 0.4;
		}

		100% {
			opacity: 0;
			left: 150%;
		}
	}

	/* 弹窗样式 - 符合页面风格 */
	.popup-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(180deg, rgba(41, 121, 255, 0.1) 0%, rgba(0, 180, 255, 0.1) 100%);
		backdrop-filter: blur(10rpx);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		animation: fadeIn 0.3s ease-out;
	}

	.popup-content {
		position: relative;
		background: linear-gradient(180deg, #ffffff 0%, #f8f9ff 100%);
		border-radius: 24rpx;
		max-width: 640rpx;
		width: 90%;
		height: 55vh;
		margin: 40rpx;
		overflow: hidden;
		box-shadow: 0 20rpx 60rpx rgba(41, 121, 255, 0.2);
		border: 1rpx solid rgba(41, 121, 255, 0.1);
		animation: popupSlideIn 0.3s ease-out;
	}

	.popup-close {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		width: 60rpx;
		height: 60rpx;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 10;
		box-shadow: 0 8rpx 24rpx rgba(41, 121, 255, 0.3);
		transition: all 0.3s ease;
	}

	.popup-close:active {
		transform: scale(0.95);
		box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.4);
	}

	.close-icon {
		color: #fff;
		font-size: 40rpx;
		font-weight: bold;
		line-height: 1;
	}

	/* 轮播图样式 */
	.popup-swiper {
		width: 100%;
		height: 100%;
	}

	.popup-swiper-item {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
	}

	.popup-image {
		width: 100%;
		flex: 1;
		display: block;
		object-fit: contain;
		background: linear-gradient(180deg, #f8f9ff 0%, #f0f2ff 100%);
		border-radius: 20rpx 20rpx 0 0;
	}

	.popup-title {
		padding: 20rpx 30rpx;
		font-size: 26rpx;
		font-weight: 600;
		text-align: center;
		line-height: 1.4;
		background: linear-gradient(135deg, rgba(41, 121, 255, 0.9), rgba(0, 180, 255, 0.9));
		color: #fff;
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 2;
		backdrop-filter: blur(10rpx);
		border-top: 1rpx solid rgba(255, 255, 255, 0.2);
	}

	/* 轮播图指示器 */
	.popup-indicator {
		position: absolute;
		top: 20rpx;
		left: 20rpx;
		background: linear-gradient(135deg, rgba(41, 121, 255, 0.9), rgba(0, 180, 255, 0.9));
		color: #fff;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		font-size: 24rpx;
		z-index: 3;
		backdrop-filter: blur(10rpx);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		box-shadow: 0 4rpx 12rpx rgba(41, 121, 255, 0.2);
	}

	.popup-no-image {
		width: 100%;
		height: 400rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #f5f5f5;
		border: 2rpx dashed #ddd;
	}

	.no-image-text {
		color: #999;
		font-size: 28rpx;
	}

	.debug-info {
		margin-top: 20rpx;
		padding: 20rpx;
		background-color: #f0f0f0;
		border-radius: 8rpx;
		font-size: 24rpx;
		color: #666;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
		}
		to {
			opacity: 1;
		}
	}

	@keyframes popupSlideIn {
		from {
			transform: scale(0.8) translateY(-50rpx);
			opacity: 0;
		}
		to {
			transform: scale(1) translateY(0);
			opacity: 1;
		}
	}

	.button-text-row {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.hand-icon {
		width: 36rpx;
		height: 36rpx;
		margin-left: 12rpx;
		display: inline-block;
		vertical-align: middle;
	}
</style>