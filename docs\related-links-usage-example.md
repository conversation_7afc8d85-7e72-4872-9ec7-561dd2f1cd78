# 相关视频号/公众号链接使用示例

## 数据录入示例

### 1. 单个公众号文章链接
```
https://mp.weixin.qq.com/s/nIkBc49Tto_yCwC1Np_y4g
```
**显示效果**：公众号文章

### 2. 单个视频号链接
```
https://channels.weixin.qq.com/video/abc123
```
**显示效果**：视频号

### 3. 多个链接组合
```
https://mp.weixin.qq.com/s/nIkBc49Tto_yCwC1Np_y4g,https://channels.weixin.qq.com/video/abc123,https://example.com/article
```
**显示效果**：
- 公众号文章
- 视频号  
- example.com

### 4. 包含空格的链接（会自动处理）
```
https://mp.weixin.qq.com/s/abc123, https://channels.weixin.qq.com/def456 , https://example.com
```
**显示效果**：
- 公众号文章
- 视频号
- example.com

## 页面显示效果

### Form 页面（产品详情页）
```
┌─────────────────────────────────────┐
│ 📋 基本信息                          │
│ ...                                 │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ 📁 查看相关资料 (3个文件)            │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ 🔗 相关视频号/公众号 (2个链接)       │
│                                     │
│ ✈️ 公众号文章                 ▶     │
│ ✈️ 视频号                     ▶     │
└─────────────────────────────────────┘
```

### Category 页面（分类页）
```
┌─────────────────────────────────────┐
│ ✈️ 查看相关资料 (2个文件)            │
└─────────────────────────────────────┘

┌─────────────────────────────────────┐
│ 🔗 相关视频号/公众号 (3个链接)       │
│                                     │
│ ✈️ 公众号文章               ▶       │
│ ✈️ 视频号                   ▶       │
│ ✈️ example.com              ▶       │
└─────────────────────────────────────┘
```

## 用户操作流程

### 1. 点击链接
用户点击任意相关链接项目

### 2. 确认对话框
```
┌─────────────────────────────────────┐
│              打开链接                │
│                                     │
│ 即将复制链接到剪贴板，请在浏览器中打开 │
│                                     │
│    [取消]           [复制链接]       │
└─────────────────────────────────────┘
```

### 3. 复制成功提示
```
┌─────────────────────────────────────┐
│              ✅                     │
│            链接已复制                │
└─────────────────────────────────────┘
```

### 4. 用户后续操作
1. 打开微信或浏览器
2. 粘贴链接
3. 访问相关内容

## 代码集成示例

### 1. 在现有页面中添加相关链接显示

```vue
<template>
    <!-- 其他内容 -->
    
    <!-- 相关链接区域 -->
    <view v-if="relatedLinks.length > 0" class="links-section">
        <view class="links-header">
            <uni-icons type="link" size="20" color="#2979FF"></uni-icons>
            <text class="links-title">相关链接 ({{relatedLinks.length}}个)</text>
        </view>
        <view class="links-list">
            <view v-for="(link, index) in relatedLinks" :key="index" 
                  class="link-item" @tap="openRelatedLink(link)">
                <uni-icons type="paperplane-filled" size="16" color="#2979FF"></uni-icons>
                <text class="link-text">{{ getLinkDisplayText(link) }}</text>
                <uni-icons type="right" size="12" color="#999"></uni-icons>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            relatedLinks: []
        }
    },
    methods: {
        // 解析相关链接数据
        parseRelatedLinks(linksStr) {
            if (!linksStr) return [];
            return linksStr.split(',')
                .map(link => link.trim())
                .filter(link => link.length > 0);
        },
        
        // 获取链接显示文本
        getLinkDisplayText(link) {
            if (link.includes('mp.weixin.qq.com')) {
                return '公众号文章';
            }
            if (link.includes('channels.weixin.qq.com') || link.includes('video.weixin.qq.com')) {
                return '视频号';
            }
            try {
                const url = new URL(link);
                return url.hostname;
            } catch (error) {
                return '相关链接';
            }
        },
        
        // 打开相关链接
        openRelatedLink(link) {
            uni.showModal({
                title: '打开链接',
                content: '即将复制链接到剪贴板，请在浏览器中打开',
                confirmText: '复制链接',
                cancelText: '取消',
                success: (res) => {
                    if (res.confirm) {
                        uni.setClipboardData({
                            data: link,
                            success: () => {
                                uni.showToast({
                                    title: '链接已复制',
                                    icon: 'success'
                                });
                            }
                        });
                    }
                }
            });
        }
    }
}
</script>
```

### 2. 样式定义

```css
.links-section {
    margin: 20rpx;
    background-color: #fff;
    border-radius: 12rpx;
    padding: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.links-header {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
}

.links-title {
    margin-left: 12rpx;
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
}

.links-list {
    background-color: #f8f9fa;
    border-radius: 8rpx;
    overflow: hidden;
}

.link-item {
    display: flex;
    align-items: center;
    padding: 24rpx 20rpx;
    border-bottom: 1px solid #eee;
    background-color: #fff;
    margin-bottom: 2rpx;
}

.link-item:last-child {
    margin-bottom: 0;
}

.link-item:active {
    background-color: #f5f5f5;
}

.link-text {
    flex: 1;
    margin-left: 12rpx;
    color: #333;
    font-size: 28rpx;
}
```

## 常见问题解答

### Q1: 为什么不能直接在小程序内打开链接？
A1: 微信小程序出于安全考虑，不允许直接跳转到外部链接。只能通过复制链接的方式让用户在浏览器中打开。

### Q2: 支持哪些类型的链接？
A2: 支持所有HTTP/HTTPS链接，但会针对微信公众号和视频号链接显示特殊的标识。

### Q3: 链接数量有限制吗？
A3: 理论上没有限制，但建议控制在10个以内，以保证良好的用户体验。

### Q4: 如何修改链接显示的文本？
A4: 可以修改 `getLinkDisplayText` 方法中的逻辑来自定义显示文本。

### Q5: 可以添加图标吗？
A5: 可以根据链接类型在模板中添加不同的图标，比如公众号用文章图标，视频号用视频图标。

## 扩展功能建议

### 1. 链接预览
可以考虑添加链接预览功能，显示链接的标题和描述。

### 2. 访问统计
可以添加链接点击统计功能，了解用户对不同链接的兴趣。

### 3. 分类管理
可以对链接进行分类，比如"产品介绍"、"使用教程"、"案例分享"等。

### 4. 二维码生成
可以为链接生成二维码，方便用户扫码访问。
