"use strict";
const common_vendor = require("../../common/vendor.js");
const config_fields = require("../../config/fields.js");
const utils_url = require("../../utils/url.js");
const utils_share = require("../../utils/share.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      categories: [],
      likesLog: {},
      // 广告弹窗相关
      showAdPopup: false,
      adData: {}
    };
  },
  onLoad() {
    this.getCategories();
    this.getAdPopupData();
  },
  onShow() {
    this.updateLikesStatus();
  },
  // 分享功能
  onShareAppMessage(res) {
    common_vendor.index.__f__("log", "at pages/index/index.vue:106", "分享给好友", res);
    return utils_share.ShareUtils.getDefaultShareConfig({
      title: "熙迈科技工业服务 - 专业产品展示",
      path: "pages/index/index",
      imageUrl: "/static/熙迈LOGO.png"
    });
  },
  onShareTimeline(res) {
    common_vendor.index.__f__("log", "at pages/index/index.vue:115", "分享到朋友圈", res);
    return utils_share.ShareUtils.getDefaultShareConfig({
      title: "熙迈科技工业服务 - 专业产品展示",
      path: "pages/index/index",
      imageUrl: "/static/熙迈LOGO.png"
    });
  },
  methods: {
    // 获取广告弹窗数据
    async getAdPopupData() {
      try {
        const requestData = {
          appKey: "984e1ff028f80150",
          sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
          worksheetId: "xcxdcwh",
          viewId: "",
          pageSize: 1,
          pageIndex: 1,
          listType: 0,
          controls: [],
          filters: [
            {
              controlId: "6870869ba849420e13f654c3",
              // isActive字段
              dataType: 36,
              spliceType: 1,
              filterType: 2,
              value: "1"
              // 只获取启用的弹窗
            }
          ]
        };
        const response2 = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows",
          method: "POST",
          data: requestData,
          header: {
            "Content-Type": "application/json"
          }
        });
        if (response2.data && response2.data.data && response2.data.data.rows && response2.data.data.rows.length > 0) {
          const adRow = response2.data.data.rows[0];
          this.adData = {
            id: adRow["6870869ba849420e13f654bf"],
            // id
            title: adRow["687085d1a849420e13f654b5"],
            // title
            jumpUrl: adRow["6870869ba849420e13f654c0"],
            // jumpUrl
            imageUrl: this.getImageUrl(adRow["687085d1a849420e13f654b7"]),
            // imageUrl
            startDate: adRow["6870869ba849420e13f654c1"],
            // startDate
            endDate: adRow["6870869ba849420e13f654c2"],
            // endDate
            isActive: adRow["6870869ba849420e13f654c3"]
            // isActive
          };
          if (this.shouldShowPopup()) {
            this.showAdPopup = true;
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:174", "获取广告弹窗数据失败:", error);
      }
    },
    // 处理图片URL（附件类型字段）
    getImageUrl(attachmentData) {
      if (!attachmentData)
        return "";
      try {
        const parsed = typeof attachmentData === "string" ? JSON.parse(attachmentData) : attachmentData;
        if (parsed && parsed.length > 0 && parsed[0].previewUrl) {
          return parsed[0].previewUrl;
        }
      } catch (e) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:187", "解析图片数据失败:", e);
      }
      return "";
    },
    // 判断是否应该显示弹窗
    shouldShowPopup() {
      if (!this.adData.isActive)
        return false;
      const today = /* @__PURE__ */ new Date();
      const todayStr = today.toISOString().split("T")[0];
      if (this.adData.startDate && todayStr < this.adData.startDate)
        return false;
      if (this.adData.endDate && todayStr > this.adData.endDate)
        return false;
      const lastShown = common_vendor.index.getStorageSync("adPopupLastShown");
      if (lastShown === todayStr)
        return false;
      return true;
    },
    // 关闭广告弹窗
    closeAdPopup() {
      this.showAdPopup = false;
      const today = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
      common_vendor.index.setStorageSync("adPopupLastShown", today);
    },
    // 处理广告点击
    handleAdClick() {
      if (this.adData.jumpUrl) {
        const today = (/* @__PURE__ */ new Date()).toISOString().split("T")[0];
        common_vendor.index.setStorageSync("adPopupLastShown", today);
        this.showAdPopup = false;
        common_vendor.index.navigateTo({
          url: this.adData.jumpUrl,
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/index/index.vue:232", "页面跳转失败:", err);
            common_vendor.index.showToast({
              title: "页面跳转失败",
              icon: "none"
            });
          }
        });
      }
    },
    updateLikesStatus() {
      const likesLog = common_vendor.index.getStorageSync("likes_log") || {};
      this.likesLog = likesLog;
      if (this.categories.length > 0) {
        this.categories.forEach((item) => {
          if (likesLog[item.rowId]) {
            this.$set(item, "isLiked", true);
          } else {
            this.$set(item, "isLiked", false);
          }
          if (item.children && item.children.length > 0) {
            item.children.forEach((child) => {
              if (likesLog[child.rowId]) {
                this.$set(child, "isLiked", true);
              } else {
                this.$set(child, "isLiked", false);
              }
            });
          }
        });
      }
    },
    handleLike(item) {
      item.isLiked = !item.isLiked;
      let webhookAction = "";
      if (item.isLiked) {
        item.likeCount++;
        webhookAction = "increment";
        this.$set(this.likesLog, item.rowId, true);
        this.$set(item, "showHeart", true);
        setTimeout(() => {
          this.$set(item, "showHeart", false);
        }, 600);
      } else {
        item.likeCount--;
        webhookAction = "decrement";
        this.$delete(this.likesLog, item.rowId);
      }
      common_vendor.index.setStorageSync("likes_log", this.likesLog);
      common_vendor.index.request({
        url: "https://dmit.duoningbio.com/api/workflow/hooks/Njg2Y2ZhNDk3MDMwNzAyNDViNDMxOTcx",
        method: "POST",
        header: {
          "Content-Type": "application/json"
        },
        data: {
          action: webhookAction,
          id: item.rowId
        },
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/index/index.vue:304", `Webhook '${webhookAction}' for rowid ${item.rowId} sent successfully. Response:`, res.data);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/index/index.vue:307", `Webhook '${webhookAction}' for rowid ${item.rowId} failed:`, err);
        }
      });
    },
    formatAPIData(row) {
      const formattedItem = {
        rowId: row.rowid || "",
        children: [],
        likeCount: parseInt(row["DZS"]) || 0,
        isLiked: false,
        showHeart: false
      };
      Object.keys(config_fields.FIELD_MAPPING).forEach((key) => {
        const apiFieldId = config_fields.FIELD_MAPPING[key];
        formattedItem[key] = row[apiFieldId] || "";
      });
      formattedItem.caseStudiesCount = parseInt(row[config_fields.FIELD_MAPPING.caseStudiesCount]) || 0;
      if (formattedItem.imageUrl || row.imageUrl) {
        formattedItem.imageUrl = this.getImageUrl(formattedItem.imageUrl || row.imageUrl);
      }
      return formattedItem;
    },
    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        common_vendor.index.showToast({
          title: "请输入搜索关键词",
          icon: "none"
        });
        return;
      }
      try {
        const requestData = {
          appKey: "984e1ff028f80150",
          sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
          worksheetId: "fenlei",
          viewId: "",
          pageSize: 50,
          pageIndex: 1,
          listType: 0,
          controls: [],
          filters: [
            {
              controlId: "67b2da03ef727a4cd047da1b",
              dataType: 2,
              spliceType: 2,
              filterType: 1,
              value: this.searchKeyword
            },
            {
              controlId: "67b2dd25ef727a4cd047da2a",
              dataType: 2,
              spliceType: 2,
              filterType: 1,
              value: this.searchKeyword
            }
          ]
        };
        const response2 = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows",
          method: "POST",
          data: requestData,
          header: {
            "Content-Type": "application/json"
          }
        });
        if (response2.statusCode !== 200) {
          common_vendor.index.__f__("error", "at pages/index/index.vue:383", "请求失败:", response2);
          common_vendor.index.showToast({
            title: "请求失败",
            icon: "none"
          });
          return;
        }
        if (response2.data && response2.data.data) {
          if (response2.data.data.rows && response2.data.data.rows.length > 0) {
            const formattedData = response2.data.data.rows.map((row) => this.formatAPIData(row));
            common_vendor.index.__f__("log", "at pages/index/index.vue:394", "Formatted Data:", formattedData);
            common_vendor.index.navigateTo({
              url: `/pages/category/category?title=搜索结果&children=${encodeURIComponent(JSON.stringify(formattedData))}`
            });
          } else if (response2.data.data.total === 0 || response2.data.error_code === 1) {
            common_vendor.index.showToast({
              title: "没有找到相关产品",
              icon: "none",
              duration: 2e3
            });
          }
        } else {
          common_vendor.index.__f__("error", "at pages/index/index.vue:406", "接口返回数据格式异常:", response2.data);
          common_vendor.index.showToast({
            title: "数据格式异常",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:413", "获取分类失败:", error);
        common_vendor.index.showToast({
          title: "获取分类失败",
          icon: "none"
        });
      }
    },
    async getCategories() {
      try {
        const response2 = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows",
          method: "POST",
          data: {
            appKey: "984e1ff028f80150",
            sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
            worksheetId: "fenlei",
            viewId: "",
            pageSize: 50,
            pageIndex: 1,
            listType: 0,
            controls: [],
            filters: [{
              controlId: "67b2dd3aef727a4cd047da36",
              dataType: 29,
              spliceType: 1,
              filterType: 7
            }],
            sortId: "67b2dd25ef727a4cd047da2a"
          },
          header: {
            "Content-Type": "application/json"
          }
        });
        common_vendor.index.__f__("log", "at pages/index/index.vue:447", "API Response:", response2);
        if (response2.statusCode !== 200) {
          common_vendor.index.__f__("error", "at pages/index/index.vue:450", "请求失败:", response2);
          common_vendor.index.showToast({
            title: "请求失败",
            icon: "none"
          });
          return;
        }
        if (response2.data && response2.data.data && response2.data.data.rows) {
          common_vendor.index.__f__("log", "at pages/index/index.vue:459", "Raw Data:", response2.data.data.rows);
          this.categories = response2.data.data.rows.map((row) => this.formatAPIData(row));
          this.categories.sort((a, b) => b.likeCount - a.likeCount);
        } else {
          common_vendor.index.__f__("error", "at pages/index/index.vue:466", "接口返回数据格式异常:", response2.data);
          common_vendor.index.showToast({
            title: "数据格式异常",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:473", "获取分类失败:", error);
        common_vendor.index.showToast({
          title: "获取分类失败",
          icon: "none"
        });
      }
    },
    getImageUrl(imageData) {
      try {
        const parsedData = Array.isArray(imageData) ? imageData : JSON.parse(imageData);
        if (!Array.isArray(parsedData)) {
          common_vendor.index.__f__("warn", "at pages/index/index.vue:486", "图片数据格式错误");
          return "/static/熙迈LOGO.png";
        }
        const targetItem = parsedData.find(
          (item) => {
            var _a, _b;
            return ((_a = item.fileUrl) == null ? void 0 : _a.startsWith("http")) || ((_b = item.thumbnail_full_path) == null ? void 0 : _b.startsWith("http"));
          }
        );
        return (targetItem == null ? void 0 : targetItem.fileUrl) || (targetItem == null ? void 0 : targetItem.thumbnail_full_path) || "/static/熙迈LOGO.png";
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:502", "解析失败:", error.message);
        return "/static/熙迈LOGO.png";
      }
    },
    async handleCategoryClick(item) {
      let recommendations = [];
      let parentMaterialsWithDownloadUrl = [];
      try {
        const hasIntroductionMaterials = item.introductionMaterials && item.introductionMaterials.length > 0;
        if (hasIntroductionMaterials) {
          common_vendor.index.__f__("log", "at pages/index/index.vue:516", "检测到相关资料，获取完整附件信息...");
          try {
            const materialResponse = await common_vendor.index.request({
              url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows",
              method: "POST",
              data: {
                appKey: "984e1ff028f80150",
                sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
                worksheetId: "fenlei",
                pageSize: 1,
                pageIndex: 1,
                listType: 0,
                controls: [],
                filters: [{
                  controlId: "rowid",
                  dataType: 2,
                  spliceType: 1,
                  filterType: 2,
                  value: item.rowId
                }]
              },
              header: {
                "Content-Type": "application/json"
              }
            });
            if (materialResponse.data && materialResponse.data.data && materialResponse.data.data.rows && materialResponse.data.data.rows.length > 0) {
              const productData = materialResponse.data.data.rows[0];
              if (productData.DLPP_JSZL) {
                const materialsData = JSON.parse(productData.DLPP_JSZL);
                common_vendor.index.__f__("log", "at pages/index/index.vue:546", "原始附件数据:", materialsData);
                parentMaterialsWithDownloadUrl = materialsData.map((material) => {
                  common_vendor.index.__f__("log", "at pages/index/index.vue:548", "处理单个附件:", material);
                  return {
                    // 使用正确的字段名
                    name: material.original_file_name || "未知文件",
                    url: material.DownloadUrl || material.original_file_full_path || "",
                    size: material.file_size || 0,
                    // 保留完整的字段信息（与 form 页面一致）
                    DownloadUrl: material.DownloadUrl || "",
                    original_file_full_path: material.original_file_full_path || "",
                    original_file_name: material.original_file_name || "未知文件",
                    file_size: material.file_size || 0
                  };
                });
                common_vendor.index.__f__("log", "at pages/index/index.vue:561", "映射后的附件信息:", parentMaterialsWithDownloadUrl);
              }
            }
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/index/index.vue:565", "获取完整附件信息失败:", error);
            parentMaterialsWithDownloadUrl = item.introductionMaterials || [];
          }
        }
        const subCategoryResponse = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations",
          method: "POST",
          data: {
            appKey: "984e1ff028f80150",
            sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
            worksheetId: "fenlei",
            pageSize: 50,
            pageIndex: 1,
            rowId: item.rowId,
            controlId: "67b2dd3aef727a4cd047da37",
            getSystemControl: false
          },
          header: {
            "Content-Type": "application/json"
          }
        });
        const recommendResponse = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations",
          method: "POST",
          data: {
            appKey: "984e1ff028f80150",
            sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
            worksheetId: "fenlei",
            pageSize: 50,
            pageIndex: 1,
            rowId: item.rowId,
            controlId: "GLCP",
            getSystemControl: false
          },
          header: {
            "Content-Type": "application/json"
          }
        });
        if (subCategoryResponse.statusCode !== 200) {
          common_vendor.index.__f__("error", "at pages/index/index.vue:607", "请求子分类失败:", response);
          common_vendor.index.showToast({
            title: "请求子分类失败",
            icon: "none"
          });
          return;
        }
        if (!subCategoryResponse.data || !subCategoryResponse.data.data) {
          common_vendor.index.__f__("error", "at pages/index/index.vue:617", "接口返回数据格式异常:", subCategoryResponse.data);
          common_vendor.index.showToast({
            title: "数据格式异常",
            icon: "none"
          });
          return;
        }
        if (!subCategoryResponse.data.data.rows || subCategoryResponse.data.data.rows.length === 0) {
          recommendations = recommendResponse.data.data.rows.map((row) => this.formatAPIData(row));
          let caseStudies = [];
          if (item.caseStudiesCount > 0) {
            try {
              const caseStudiesResponse = await common_vendor.index.request({
                url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations",
                method: "POST",
                data: {
                  appKey: "984e1ff028f80150",
                  sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
                  worksheetId: "fenlei",
                  pageSize: 50,
                  pageIndex: 1,
                  rowId: item.rowId,
                  controlId: "ALQK",
                  getSystemControl: false
                },
                header: {
                  "Content-Type": "application/json"
                }
              });
              if (caseStudiesResponse.data && caseStudiesResponse.data.data && caseStudiesResponse.data.data.rows) {
                caseStudies = caseStudiesResponse.data.data.rows.map((row) => ({
                  clientName: row[config_fields.FIELD_MAPPING.caseClientName] || "",
                  details: row[config_fields.FIELD_MAPPING.caseDetails] || ""
                }));
              }
            } catch (e) {
              common_vendor.index.__f__("error", "at pages/index/index.vue:655", "获取案例情况失败:", e);
            }
          }
          const formUrl = `/pages/form/form?${utils_url.objectToParams(item)}&caseStudies=${encodeURIComponent(JSON.stringify(caseStudies))}`;
          common_vendor.index.__f__("log", "at pages/index/index.vue:662", "=== Index页面跳转到Form页面 ===");
          common_vendor.index.__f__("log", "at pages/index/index.vue:663", "完整的item对象:", item);
          common_vendor.index.__f__("log", "at pages/index/index.vue:664", "跳转的产品信息:", {
            productName: item.productName,
            productCode: item.productCode,
            serviceDescription: item.serviceDescription,
            productManager: item.productManager,
            caseStudiesCount: caseStudies.length
          });
          common_vendor.index.__f__("log", "at pages/index/index.vue:671", "构建的URL:", formUrl);
          common_vendor.index.__f__("log", "at pages/index/index.vue:672", "跳转URL长度:", formUrl.length);
          if (formUrl.length > 1500) {
            common_vendor.index.__f__("log", "at pages/index/index.vue:676", "⚠️ URL过长（" + formUrl.length + "字符），使用简化模式");
            const simpleUrl = item.productCode ? `/pages/form/form?productCode=${encodeURIComponent(item.productCode)}&shareMode=1` : `/pages/form/form?productName=${encodeURIComponent(item.productName)}&shareMode=1`;
            common_vendor.index.navigateTo({
              url: simpleUrl
            });
          } else {
            common_vendor.index.__f__("log", "at pages/index/index.vue:684", "✅ URL长度正常（" + formUrl.length + "字符），使用完整数据传递");
            common_vendor.index.navigateTo({
              url: formUrl
            });
          }
          return;
        }
        const formattedData = subCategoryResponse.data.data.rows.map((row) => this.formatAPIData(row));
        recommendations = recommendResponse.data.data.rows.map((row) => this.formatAPIData(row));
        const parentMaterials = parentMaterialsWithDownloadUrl.length > 0 ? parentMaterialsWithDownloadUrl : item.introductionMaterials || [];
        common_vendor.index.__f__("log", "at pages/index/index.vue:697", "Index页面传递父级介绍资料:", parentMaterials);
        const categoryUrl = `/pages/category/category?title=${encodeURIComponent(item.productName)}&pm=${encodeURIComponent(item.productManager)}&parentImage=${encodeURIComponent(item.imageUrl || "/static/熙迈LOGO.png")}&parentContactPhone=${encodeURIComponent(item.contactPhone || "")}&parentMaterials=${encodeURIComponent(JSON.stringify(parentMaterials))}&shareMode=1`;
        common_vendor.index.__f__("log", "at pages/index/index.vue:702", "=== Index页面跳转到Category页面 ===");
        common_vendor.index.__f__("log", "at pages/index/index.vue:703", "跳转的产品信息:", {
          productName: item.productName,
          productManager: item.productManager,
          imageUrl: item.imageUrl,
          formattedDataCount: formattedData.length,
          recommendationsCount: recommendations.length
        });
        common_vendor.index.__f__("log", "at pages/index/index.vue:710", "跳转URL:", categoryUrl);
        common_vendor.index.__f__("log", "at pages/index/index.vue:711", "URL长度:", categoryUrl.length);
        common_vendor.index.__f__("log", "at pages/index/index.vue:712", "=== 开始跳转（使用分享模式避免URL过长）===");
        common_vendor.index.navigateTo({
          url: categoryUrl
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/index/index.vue:718", "获取子分类失败:", error);
        common_vendor.index.showToast({
          title: "获取子分类失败",
          icon: "none"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.showAdPopup
  }, $data.showAdPopup ? common_vendor.e({
    b: common_vendor.o((...args) => $options.closeAdPopup && $options.closeAdPopup(...args)),
    c: $data.adData.title
  }, $data.adData.title ? {
    d: common_vendor.t($data.adData.title)
  } : {}, {
    e: $data.adData.imageUrl
  }, $data.adData.imageUrl ? {
    f: $data.adData.imageUrl,
    g: common_vendor.o((...args) => $options.handleAdClick && $options.handleAdClick(...args))
  } : {}, {
    h: common_vendor.t($data.adData.buttonText || "立即查看"),
    i: common_vendor.o((...args) => $options.handleAdClick && $options.handleAdClick(...args)),
    j: common_vendor.o(() => {
    }),
    k: common_vendor.o((...args) => $options.closeAdPopup && $options.closeAdPopup(...args))
  }) : {}, {
    l: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    m: $data.searchKeyword,
    n: common_vendor.o(($event) => $data.searchKeyword = $event.detail.value),
    o: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    p: common_vendor.f($data.categories, (item, index, i0) => {
      return common_vendor.e({
        a: item.imageUrl || "/static/熙迈LOGO.png",
        b: common_vendor.t(item.englishName),
        c: item.YWLX === "代理"
      }, item.YWLX === "代理" ? {} : {}, {
        d: common_vendor.t(item.productName),
        e: item.isLiked ? "/static/红色小红心.svg" : "/static/灰色小红心.svg",
        f: item.showHeart
      }, item.showHeart ? {
        g: common_vendor.t(item.likeCount)
      } : {}, {
        h: common_vendor.o(($event) => $options.handleLike(item), index),
        i: index,
        j: common_vendor.o(($event) => $options.handleCategoryClick(item), index)
      });
    }),
    q: common_vendor.o((...args) => _ctx.handleContact && _ctx.handleContact(...args)),
    r: $data.categories.length === 0
  }, $data.categories.length === 0 ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map
