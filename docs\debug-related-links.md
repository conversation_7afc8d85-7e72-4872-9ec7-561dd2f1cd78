# 相关链接功能调试指南

## 问题分析

根据你提供的数据，产品数据中包含：
```javascript
lianjie: "https://mp.weixin.qq.com/s/miJkoYv_Ytp56Gr_0LiT9Q"
```

这个字段应该被正确解析并显示在页面上。

## 调试步骤

### 1. 检查字段映射
已经修正了字段映射：
```javascript
// config/fields.js
relatedLinks: 'lianjie'  // 相关视频号/公众号链接
```

### 2. 检查控制台日志
在微信开发者工具的控制台中查看以下日志：

**正常模式（从URL参数解析）：**
```
🔗 正常模式解析相关链接: [URL参数值]
✅ 正常模式相关链接解析完成: [解析后的数组]
```

**分享模式（从API重新获取）：**
```
🔗 处理相关链接字段: {fieldMapping: "lianjie", rawData: "https://mp.weixin.qq.com/s/...", dataType: "string"}
✅ 相关链接处理完成: ["https://mp.weixin.qq.com/s/..."]
🔗 重新加载数据时设置相关链接: ["https://mp.weixin.qq.com/s/..."]
```

### 3. 检查页面访问方式

#### 方式1：直接访问（正常模式）
如果你是通过分类页面跳转到 form 页面，相关链接数据应该在URL参数中传递。

#### 方式2：分享模式访问
如果你是通过分享链接或直接输入产品代码访问，页面会重新从API获取数据。

### 4. 测试用例

#### 测试1：检查数据是否正确获取
在 form 页面的 `formatAPIData` 方法中，应该能看到：
```javascript
console.log('🔗 处理相关链接字段:', {
    fieldMapping: "lianjie",
    rawData: "https://mp.weixin.qq.com/s/miJkoYv_Ytp56Gr_0LiT9Q",
    dataType: "string"
});
```

#### 测试2：检查页面显示
在页面的 data 中，`relatedLinks` 应该包含：
```javascript
["https://mp.weixin.qq.com/s/miJkoYv_Ytp56Gr_0LiT9Q"]
```

#### 测试3：检查模板渲染
页面应该显示：
```
🔗 相关视频号/公众号 (1个链接)
   ✈️ 公众号文章                 ▶
```

## 可能的问题和解决方案

### 问题1：页面不显示相关链接区域
**原因**：`relatedLinks` 数组为空
**检查**：
1. 控制台是否有相关日志
2. `this.relatedLinks` 的值是否正确

### 问题2：数据获取失败
**原因**：字段映射不正确或API数据格式变化
**检查**：
1. 确认 `FIELD_MAPPING.relatedLinks` 的值是 `"lianjie"`
2. 确认API返回的数据中确实有 `lianjie` 字段

### 问题3：页面访问方式问题
**原因**：不同的访问方式使用不同的数据解析逻辑
**检查**：
1. 确认是否是分享模式（URL中有 `shareMode=1`）
2. 确认是否有 `relatedLinks` URL参数

## 手动测试步骤

### 1. 测试分享模式
访问URL：
```
/pages/form/form?productCode=SMILE-P7&shareMode=1
```

应该看到：
- 控制台显示相关链接处理日志
- 页面显示相关链接区域

### 2. 测试正常模式
从分类页面点击进入产品详情页，应该看到：
- URL参数中包含 `relatedLinks`
- 页面显示相关链接区域

### 3. 测试链接点击
点击"公众号文章"链接，应该：
- 显示确认对话框
- 复制链接到剪贴板
- 显示"链接已复制"提示

## 调试代码

如果仍然有问题，可以在 form 页面的 `onLoad` 方法中添加临时调试代码：

```javascript
onLoad(options) {
    // 临时调试：直接设置测试数据
    console.log('🔧 临时调试：设置测试相关链接');
    this.relatedLinks = ["https://mp.weixin.qq.com/s/miJkoYv_Ytp56Gr_0LiT9Q"];
    
    // 原有的 onLoad 逻辑...
}
```

这样可以确认页面模板和样式是否正常工作。

## 预期结果

正常情况下，你应该能看到：

1. **控制台日志**：
```
🔗 处理相关链接字段: {fieldMapping: "lianjie", rawData: "https://mp.weixin.qq.com/s/miJkoYv_Ytp56Gr_0LiT9Q", dataType: "string"}
✅ 相关链接处理完成: ["https://mp.weixin.qq.com/s/miJkoYv_Ytp56Gr_0LiT9Q"]
```

2. **页面显示**：
在介绍资料下方出现相关链接区域，显示"相关视频号/公众号 (1个链接)"，包含一个"公众号文章"的链接项。

3. **点击测试**：
点击链接后显示确认对话框，确认后复制链接到剪贴板。

如果以上任何一步有问题，请告诉我具体的错误信息或控制台日志，我可以进一步帮你排查。
