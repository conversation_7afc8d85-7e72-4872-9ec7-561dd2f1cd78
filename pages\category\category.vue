<template>
	<view class="container">
		<!-- 父级分类介绍资料 -->
		<view v-if="currentMaterials.length > 0" class="materials-card">
			<view class="materials-link-button" @tap="showParentMaterialsList">
				<uni-icons type="paperplane" size="18" color="#007aff"></uni-icons>
				<text class="materials-link-text">查看相关资料 ({{currentMaterials.length}}个文件)</text>
			</view>
		</view>

		<!-- 父级分类相关链接 -->
		<view v-if="currentRelatedLinks.length > 0" class="links-card">
			<view class="links-header">
				<uni-icons type="link" size="18" color="#007aff"></uni-icons>
				<text class="links-title">相关视频号/公众号 ({{currentRelatedLinks.length}}个链接)</text>
			</view>
			<view class="links-list">
				<view v-for="(link, index) in currentRelatedLinks" :key="index"
					  class="link-item" @tap="openRelatedLink(link)">
					<uni-icons type="paperplane-filled" size="14" color="#007aff"></uni-icons>
					<text class="link-text">{{ getLinkDisplayText(link) }}</text>
					<uni-icons type="right" size="10" color="#999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 产品经理和联系方式 -->
		<view v-if="parentProductManager || parentContactPhone" class="contact-card">
			<view v-if="parentProductManager" class="contact-item">
				<view class="contact-icon">
					<uni-icons type="person" size="14" color="#666"></uni-icons>
				</view>
				<view class="contact-content-inline">
					<text class="contact-label">产品经理: </text>
					<text class="contact-text">{{parentProductManager}}</text>
				</view>
			</view>
			<view v-if="parentContactPhone" class="contact-item">
				<view class="contact-icon">
					<uni-icons type="phone" size="14" color="#666"></uni-icons>
				</view>
				<view class="contact-content-inline">
					<text class="contact-label">联系方式: </text>
					<text class="phone-link" @click="callnum(parentContactPhone)">{{parentContactPhone}}</text>
				</view>
			</view>
		</view>

		<!-- 分类列表 -->
		<view class="category-list">
			<view v-for="(item, index) in categories" :key="index" class="category-item"
				@click="handleCategoryClick(item)">
				<view class="img">
					<image class="icon" :src="item.imageUrl || '/static/熙迈LOGO.png'" mode="heightFix"></image>
				</view>
				<!-- <text class="name">{{item.name}}</text> -->
				<view class="banner">
					<view class="banner-left">
						<view class="top-blue">
							<text class="english-name-content">
								<text v-if="item.englishName">{{ item.englishName }}</text>
								<text class="more-text">更多>></text>
							</text>
						</view>
						<view class="bottom-white">
							<view class="logo">
								<image v-if="item.YWLX === '代理'" class="icon"
									:src="'/static/代理图标.png'" mode="aspectFit"></image>
								<image v-else class="icon"
									:src="'/static/熙迈LOGO.png'" mode="aspectFit"></image>
							</view>
							<view class="bottom-white-text">{{item.productName}}</view>
							<view class="like-section" @click.stop="handleLike(item)">
								<image class="like-icon" :src="item.isLiked ? '/static/红色小红心.svg' : '/static/灰色小红心.svg'"></image>
								<view v-if="item.showHeart" class="pop-animation">
									<text class="pop-heart">❤️</text>
									<text class="pop-count">{{ item.likeCount }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="recommendations.length > 0" class="recommend-section">
		  <view class="section-title">您可能对以下产品感兴趣</view>
		  <view class="category-list">
				<view v-for="(item, index) in recommendations" :key="index" class="category-item"
					@click="handleCategoryClick(item)">
					<view class="img">
						<image class="icon" :src="item.imageUrl || '/static/熙迈LOGO.png'" mode="heightFix"></image>
					</view>
					<view class="banner">
						<view class="banner-left">
							<view class="top-blue">
								<text class="english-name">
									{{ item.englishName }}
									<span style="display:inline-block;width:16rpx;"></span>
									<text class="more-text" style="margin-left: 12rpx;">更多&gt;&gt;</text>
								</text>
							</view>
							<view class="bottom-white">
								<view class="logo">
									<image v-if="item.YWLX === '代理'" class="icon"
										:src="'/static/代理图标.png'" mode="aspectFit"></image>
									<image v-else class="icon"
										:src="'/static/熙迈LOGO.png'" mode="aspectFit"></image>
								</view>
								<view class="bottom-white-text">{{item.productName}}</view>
								<view class="like-section" @click.stop="handleLike(item)">
									<image class="like-icon" :src="item.isLiked ? '/static/红色小红心.svg' : '/static/灰色小红心.svg'"></image>
									<view v-if="item.showHeart" class="pop-animation">
										<text class="pop-heart">❤️</text>
										<text class="pop-count">{{ item.likeCount }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
		  </view>
		</view>

		<view class="fixed-customer-service">
			<button class="service-btn"  @click="handleContact(this.pm)">
				<view class="text-container">
					<text class="btn-text">微信客服</text>
					<text class="btn-subtext">如有需求，请点我联系</text>
				</view>
			</button>
		</view>

		<!-- 无结果提示 -->
		<view v-if="categories.length === 0" class="no-result">
			<text class="no-result-icon">&#9785;</text>
			<text class="no-result-text">没有找到相关产品</text>
			<text class="no-result-subtext">尝试更改搜索关键词或稍后再试</text>
		</view>

		<!-- 介绍资料弹窗 -->
		<view v-if="showMaterialsModal" class="materials-modal" @tap="hideMaterialsList">
			<view class="materials-modal-content" @tap.stop>
				<view class="materials-modal-header">
					<text class="materials-modal-title">相关资料</text>
					<view class="materials-modal-close" @tap="hideMaterialsList">
						<uni-icons type="close" size="20" color="#666"></uni-icons>
					</view>
				</view>
				<view class="materials-modal-body">
					<view v-for="(material, index) in currentMaterials" :key="index"
						  class="material-item" @tap="previewMaterial(material)">
						<view class="material-icon">
							<uni-icons :type="getMaterialIcon(material.original_file_name)" size="24" color="#2979FF"></uni-icons>
						</view>
						<view class="material-info">
							<text class="material-name">{{ material.original_file_name }}</text>
							<text class="material-size">{{ formatFileSize(material.file_size) }}</text>
						</view>
						<view class="material-action">
							<uni-icons type="eye" size="16" color="#999"></uni-icons>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 视频预览弹窗 -->
		<view v-if="showVideoModal" class="video-modal" @tap="closeVideoModal">
			<view class="video-modal-content" @tap.stop>
				<view class="video-modal-header">
					<text class="video-modal-title">{{ currentVideoName }}</text>
					<view class="video-modal-close" @tap="closeVideoModal">
						<uni-icons type="close" size="20" color="#fff"></uni-icons>
					</view>
				</view>
				<view class="video-modal-body">
					<video
						:src="currentVideoUrl"
						controls
						autoplay
						:show-fullscreen-btn="true"
						:show-play-btn="true"
						:show-center-play-btn="true"
						class="video-player"
					></video>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
		import {
			FIELD_MAPPING,
			PRODUCT_MANAGER_SERVICE_LINKS
		} from '@/config/fields.js';
	import {
		objectToParams
	} from '@/utils/url.js';
	import ShareUtils from '@/utils/share.js';

	export default {
		data() {
			return {
				searchKeyword: '',
				categories: [],
				title: '',
				pm: '',
				parentImage: '',
				children: [],
				recommendations: [],
				likesLog: {},
				showMaterialsModal: false, // 控制资料弹窗显示
				currentMaterials: [], // 当前查看的资料列表
				currentRelatedLinks: [], // 当前查看的相关链接列表
				parentProductManager: '', // 父级产品经理
				parentContactPhone: '', // 父级联系电话
				showVideoModal: false, // 控制视频预览弹窗显示
				currentVideoUrl: '', // 当前预览的视频URL
				currentVideoName: '' // 当前预览的视频文件名
			}
		},

		onLoad(options) {
			console.log('=== Category页面 onLoad 开始 ===');
			console.log('接收到的URL参数:', options);

			this.title = decodeURIComponent(options.title || '');
			this.pm = decodeURIComponent(options.pm || '');
			this.parentImage = decodeURIComponent(options.parentImage || '/static/熙迈LOGO.png');

			// 设置导航栏标题为上级分类名称
			if (this.title) {
				uni.setNavigationBarTitle({
					title: this.title
				});
			}

			// 解析父级产品经理和联系电话
			this.parentProductManager = decodeURIComponent(options.pm || '');
			let contactPhone = decodeURIComponent(options.parentContactPhone || '');
			// 去掉 +86 前缀
			if (contactPhone.startsWith('+86')) {
				contactPhone = contactPhone.substring(3);
			}
			this.parentContactPhone = contactPhone;

			console.log('解析后的基本信息:', {
				title: this.title,
				pm: this.pm,
				parentImage: this.parentImage,
				shareMode: options.shareMode,
				parentId: options.parentId
			});

			// 检查是否是分享模式或者有parentId参数
			if (options.shareMode === '1' || options.parentId) {
				// 分享模式或parentId模式：需要重新获取数据
				console.log('🔄 检测到分享模式或parentId模式，开始重新获取数据');

				// 如果有 parentMaterials 参数，先解析它
				if (options.parentMaterials) {
					try {
						this.currentMaterials = JSON.parse(decodeURIComponent(options.parentMaterials || '[]'));
						console.log('✅ 从URL参数解析父级相关资料，数量:', this.currentMaterials.length);
					} catch (error) {
						console.error('❌ 解析URL参数中的父级相关资料失败:', error);
						this.currentMaterials = [];
					}
				}

				// 如果有 parentRelatedLinks 参数，先解析它
				if (options.parentRelatedLinks) {
					try {
						const relatedLinksStr = decodeURIComponent(options.parentRelatedLinks || '');
						if (relatedLinksStr) {
							// 用逗号分隔链接，并过滤空字符串
							this.currentRelatedLinks = relatedLinksStr.split(',')
								.map(link => link.trim())
								.filter(link => link.length > 0);
							console.log('✅ 从URL参数解析父级相关链接，数量:', this.currentRelatedLinks.length);
						} else {
							this.currentRelatedLinks = [];
						}
					} catch (error) {
						console.error('❌ 解析URL参数中的父级相关链接失败:', error);
						this.currentRelatedLinks = [];
					}
				}

				if (options.parentId) {
					// 如果有parentId，直接通过parentId加载子分类
					this.loadDataByParentId(options.parentId);
				} else {
					// 否则通过title搜索
					this.loadDataFromAPI();
				}
			} else {
				console.log('📝 正常模式，解析URL参数中的数据');
				// 正常模式：从URL参数解析数据
				// 解析 children 数据
				try {
					this.children = JSON.parse(decodeURIComponent(options.children || '[]'));
					console.log('✅ 成功解析 children 数据，数量:', this.children.length);

					// 将 children 数据赋值给 categories
					this.categories = this.children;
					this.categories.sort((a, b) => b.likeCount - a.likeCount);
				} catch (error) {
					console.error('❌ 解析 children 数据失败:', error);
					uni.showToast({
						title: '数据解析失败',
						icon: 'none'
					});
				}
				try {
					this.recommendations = JSON.parse(decodeURIComponent(options.recommendations || '[]'))
					this.recommendations.sort((a, b) => b.likeCount - a.likeCount);
					console.log('✅ 成功解析推荐数据，数量:', this.recommendations.length);
				} catch (error) {
					console.error('❌ 解析推荐数据失败:', error)
				}

				// 解析父级分类的介绍资料
				try {
					this.currentMaterials = JSON.parse(decodeURIComponent(options.parentMaterials || '[]'));
					console.log('✅ 成功解析父级介绍资料，数量:', this.currentMaterials.length);
				} catch (error) {
					console.error('❌ 解析父级介绍资料失败:', error);
					this.currentMaterials = [];
				}

				// 解析父级分类的相关链接
				try {
					const relatedLinksStr = decodeURIComponent(options.parentRelatedLinks || '');
					if (relatedLinksStr) {
						// 用逗号分隔链接，并过滤空字符串
						this.currentRelatedLinks = relatedLinksStr.split(',')
							.map(link => link.trim())
							.filter(link => link.length > 0);
						console.log('✅ 成功解析父级相关链接，数量:', this.currentRelatedLinks.length);
					} else {
						this.currentRelatedLinks = [];
					}
				} catch (error) {
					console.error('❌ 解析父级相关链接失败:', error);
					this.currentRelatedLinks = [];
				}
			}
			console.log('=== Category页面 onLoad 结束 ===');
		},
		onShow() {
			this.updateLikesStatus();
		},

		// 分享功能
		onShareAppMessage(res) {
			console.log('=== Category页面分享给好友 ===');
			console.log('分享触发参数:', res);
			console.log('当前页面数据:', {
				title: this.title,
				parentImage: this.parentImage,
				categoriesCount: this.categories.length,
				recommendationsCount: this.recommendations.length
			});

			const shareTitle = this.title ? `${this.title} - 熙迈科技` : '熙迈科技分类产品 - 专业工业服务';

			// 由于分享链接长度限制，只传递核心参数，页面加载时重新获取数据
			const sharePath = `pages/category/category?title=${encodeURIComponent(this.title || '')}&pm=${encodeURIComponent(this.pm || '')}&parentImage=${encodeURIComponent(this.parentImage || '/static/熙迈LOGO.png')}&shareMode=1`;

			console.log('构建的分享路径:', sharePath);
			console.log('分享路径长度:', sharePath.length);

			const shareConfig = ShareUtils.getDefaultShareConfig({
				title: shareTitle,
				path: sharePath,
				imageUrl: this.parentImage || '/static/熙迈LOGO.png'
			});

			console.log('最终分享配置:', shareConfig);
			console.log('=== Category页面分享配置完成 ===');

			return shareConfig;
		},

		onShareTimeline(res) {
			console.log('=== Category页面分享到朋友圈 ===');
			console.log('分享触发参数:', res);

			const shareTitle = this.title ? `${this.title} - 熙迈科技` : '熙迈科技分类产品 - 专业工业服务';

			// 由于分享链接长度限制，只传递核心参数，页面加载时重新获取数据
			const sharePath = `pages/category/category?title=${encodeURIComponent(this.title || '')}&pm=${encodeURIComponent(this.pm || '')}&parentImage=${encodeURIComponent(this.parentImage || '/static/熙迈LOGO.png')}&shareMode=1`;

			console.log('构建的分享路径:', sharePath);

			const shareConfig = ShareUtils.getDefaultShareConfig({
				title: shareTitle,
				path: sharePath,
				imageUrl: this.parentImage || '/static/熙迈LOGO.png'
			});

			console.log('最终分享配置:', shareConfig);
			console.log('=== Category页面朋友圈分享配置完成 ===');

			return shareConfig;
		},

		methods: {
			// 通过父类ID直接加载数据
			async loadDataByParentId(parentId) {
				console.log('🔄 通过父类ID加载数据:', parentId);

				try {
					// 先通过 parentId 获取父级分类的完整数据
					console.log('🔄 开始获取父级分类完整数据');
					const parentDataResponse = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',
						method: 'POST',
						data: {
							appKey: '984e1ff028f80150',
							sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
							worksheetId: 'fenlei',
							pageSize: 50,
							pageIndex: 1,
							listType: 0,
							controls: [],
							filters: [
								{
									controlId: 'rowid',
									dataType: 2,
									spliceType: 1,
									filterType: 2, // 等于（精确匹配）
									value: parentId
								}
							]
						},
						header: {
							'Content-Type': 'application/json'
						}
					});

					console.log('父级数据API响应:', parentDataResponse);

					if (parentDataResponse.data && parentDataResponse.data.data && parentDataResponse.data.data.rows && parentDataResponse.data.data.rows.length > 0) {
						// 获取到完整的父级数据
						const parentItem = parentDataResponse.data.data.rows[0];
						console.log('✅ 获取到父级分类完整数据:', parentItem);

						// 使用完整的父级数据加载子分类
						await this.loadSubCategories(parentItem);
					} else {
						console.log('❌ 未找到对应的父级分类数据');
						// 如果找不到父级数据，创建一个基本对象继续执行
						const parentItem = {
							rowid: parentId
						};
						await this.loadSubCategories(parentItem);
					}

				} catch (error) {
					console.error('❌ 通过父类ID加载数据失败:', error);
					uni.showToast({
						title: '加载数据失败',
						icon: 'none'
					});
				}
			},

			// 从API重新加载数据（用于分享模式）
			async loadDataFromAPI() {
				console.log('🔄 开始从API重新加载数据');
				console.log('搜索标题:', this.title);

				try {
					const requestData = {
						appKey: '984e1ff028f80150',
						sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
						worksheetId: 'fenlei',
						pageSize: 50,
						pageIndex: 1,
						listType: 0,
						controls: [],
						filters: [
							{
								controlId: '67b2da03ef727a4cd047da1b', // 产品名称字段
								dataType: 2,
								spliceType: 1,
								filterType: 2, // 等于（精确匹配）
								value: this.title
							}
						]
					};

					console.log('API请求数据:', requestData);

					// 根据标题搜索对应的分类数据
					const response = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',
						method: 'POST',
						data: requestData,
						header: {
							'Content-Type': 'application/json'
						}
					});

					console.log('API响应:', response);

					if (response.data && response.data.data && response.data.data.rows && response.data.data.rows.length > 0) {
						console.log('✅ 找到匹配的分类数据，数量:', response.data.data.rows.length);
						// 找到匹配的分类，获取其子分类
						const parentItem = response.data.data.rows[0];
						console.log('父级分类数据:', parentItem);
						await this.loadSubCategories(parentItem);
					} else {
						console.log('❌ 未找到匹配的分类数据');
						// 如果找不到数据，显示空状态
						this.categories = [];
						this.recommendations = [];
						uni.showToast({
							title: '未找到相关数据',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('❌ 重新加载数据失败:', error);
					uni.showToast({
						title: '加载数据失败',
						icon: 'none'
					});
				}
			},

			// 加载子分类数据
			async loadSubCategories(parentItem) {
				console.log('🔄 开始加载子分类数据');
				console.log('父级项目rowid:', parentItem.rowid);

				// 从父级项目中提取产品名称并设置导航栏标题
				const parentProductName = parentItem['67b2da03ef727a4cd047da1b'] || '';
				if (parentProductName) {
					this.title = parentProductName;
					uni.setNavigationBarTitle({
						title: parentProductName
					});
					console.log('✅ 设置导航栏标题为:', parentProductName);
				}

				// 从父级项目中提取产品经理和联系电话信息
				console.log('🔄 处理父级联系信息');
				this.parentProductManager = parentItem['67b2de31ef727a4cd047da6f'] || '';
				let contactPhone = parentItem['67b400cdef727a4cd047e08c'] || '';
				// 去掉 +86 前缀
				if (contactPhone.startsWith('+86')) {
					contactPhone = contactPhone.substring(3);
				}
				this.parentContactPhone = contactPhone;

				// 处理父级相关资料（只有在没有从URL参数获取到的情况下才处理）
				if (this.currentMaterials.length === 0) {
					console.log('🔄 从API数据处理父级相关资料');
					if (parentItem.DLPP_JSZL) {
						try {
							const materialsData = JSON.parse(parentItem.DLPP_JSZL);
							this.currentMaterials = materialsData.map(material => ({
								// 使用正确的字段名
								name: material.original_file_name || '未知文件',
								url: material.DownloadUrl || material.original_file_full_path || '',
								size: material.file_size || 0,
								// 保留完整字段（与 form 页面一致）
								DownloadUrl: material.DownloadUrl || '',
								original_file_full_path: material.original_file_full_path || '',
								original_file_name: material.original_file_name || '未知文件',
								file_size: material.file_size || 0
							}));
							console.log('✅ 从API数据成功处理父级相关资料，数量:', this.currentMaterials.length);
						} catch (error) {
							console.error('❌ 解析API数据中的父级相关资料失败:', error);
							this.currentMaterials = [];
						}
					}
				} else {
					console.log('✅ 已从URL参数获取到父级相关资料，跳过API数据处理');
				}

				// 处理父级相关链接（只有在没有从URL参数获取到的情况下才处理）
				if (this.currentRelatedLinks.length === 0) {
					console.log('🔄 从API数据处理父级相关链接');
					const relatedLinksData = parentItem[FIELD_MAPPING.relatedLinks];
					if (relatedLinksData && typeof relatedLinksData === 'string') {
						// 用逗号分隔链接，并过滤空字符串
						this.currentRelatedLinks = relatedLinksData.split(',')
							.map(link => link.trim())
							.filter(link => link.length > 0);
						console.log('✅ 从API数据成功处理父级相关链接，数量:', this.currentRelatedLinks.length);
					}
				} else {
					console.log('✅ 已从URL参数获取到父级相关链接，跳过API数据处理');
				}

				console.log('父级联系信息:', {
					productManager: this.parentProductManager,
					contactPhone: this.parentContactPhone,
					materialsCount: this.currentMaterials.length
				});

				try {
					const subCategoryRequestData = {
						appKey: '984e1ff028f80150',
						sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
						worksheetId: 'fenlei',
						pageSize: 50,
						pageIndex: 1,
						rowId: parentItem.rowid,
						controlId: '67b2dd3aef727a4cd047da37',
						getSystemControl: false
					};

					console.log('子分类API请求数据:', subCategoryRequestData);

					const subCategoryResponse = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',
						method: 'POST',
						data: subCategoryRequestData,
						header: {
							'Content-Type': 'application/json'
						}
					});

					console.log('子分类API响应:', subCategoryResponse);

					if (subCategoryResponse.data && subCategoryResponse.data.data && subCategoryResponse.data.data.rows) {
						console.log('✅ 获取到子分类数据，数量:', subCategoryResponse.data.data.rows.length);
						this.categories = subCategoryResponse.data.data.rows.map(row => this.formatAPIData(row));
						this.categories.sort((a, b) => b.likeCount - a.likeCount);
						console.log('格式化后的子分类数据:', this.categories);
					} else {
						console.log('❌ 未获取到子分类数据');
					}

					// 加载推荐数据
					await this.loadRecommendations(parentItem);

					console.log('✅ 子分类和推荐数据加载完成');
				} catch (error) {
					console.error('❌ 加载子分类失败:', error);
				}
			},

			// 加载推荐数据（根据父级项目）
			async loadRecommendations(parentItem) {
				try {
					console.log('🔄 开始获取推荐数据，父级rowid:', parentItem?.rowid);

					if (!parentItem || !parentItem.rowid) {
						console.log('❌ 没有父级项目信息，跳过推荐数据加载');
						this.recommendations = [];
						return;
					}

					const recommendResponse = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',
						method: 'POST',
						data: {
							appKey: '984e1ff028f80150',
							sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
							worksheetId: 'fenlei',
							rowId: parentItem.rowid,
							controlId: '67b2dd25ef727a4cd047da2b', // 推荐产品字段ID
							pageSize: 10,
							pageIndex: 1
						},
						header: {
							'Content-Type': 'application/json'
						}
					});

					if (recommendResponse.data && recommendResponse.data.data && recommendResponse.data.data.rows) {
						this.recommendations = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));
						this.recommendations.sort((a, b) => b.likeCount - a.likeCount);
						console.log('✅ 获取到推荐数据，数量:', this.recommendations.length);
					} else {
						console.log('❌ 未获取到推荐数据');
						this.recommendations = [];
					}
				} catch (error) {
					console.error('❌ 加载推荐数据失败:', error);
					this.recommendations = [];
				}
			},

			updateLikesStatus() {
				const likesLog = uni.getStorageSync('likes_log') || {};
				this.likesLog = likesLog;
				const processList = (list) => {
					if (list && list.length > 0) {
						list.forEach(item => {
							if (likesLog[item.rowId]) {
								this.$set(item, 'isLiked', true);
							} else {
								this.$set(item, 'isLiked', false);
							}
						});
					}
				};
				processList(this.categories);
				processList(this.recommendations);
			},
			handleLike(item) {
				// 1. Toggle the UI state immediately (Optimistic Update)
				item.isLiked = !item.isLiked;
				
				let webhookAction = '';
			
				if (item.isLiked) {
					// --- UI & Local State Update for LIKE ---
					item.likeCount++;
					webhookAction = 'increment';
					this.$set(this.likesLog, item.rowId, true);
			
					// Trigger animation
					this.$set(item, 'showHeart', true);
					setTimeout(() => {
						this.$set(item, 'showHeart', false);
					}, 600);
				} else {
					// --- UI & Local State Update for UNLIKE ---
					item.likeCount--;
					webhookAction = 'decrement';
					this.$delete(this.likesLog, item.rowId);
				}
			
				// Update local storage for persistence across sessions
				uni.setStorageSync('likes_log', this.likesLog);
			
				// 2. Send the corresponding command to the webhook
				uni.request({
					url: 'https://dmit.duoningbio.com/api/workflow/hooks/Njg2Y2ZhNDk3MDMwNzAyNDViNDMxOTcx',
					method: 'POST',
					header: {
						'Content-Type': 'application/json'
					},
					data: {
						action: webhookAction,
						id: item.rowId
					},
					success: (res) => {
						console.log(`Webhook '${webhookAction}' for rowid ${item.rowId} sent successfully. Response:`, res.data);
					},
					fail: (err) => {
						console.error(`Webhook '${webhookAction}' for rowid ${item.rowId} failed:`, err);
					}
				});
			},
			handleContact(pm) {
				// console.log(pm);
				// 从PRODUCT_MANAGER_SERVICE_LINKS中查找对应的serviceLink
				let serviceLink = 'https://work.weixin.qq.com/kfid/kfc05b214375686d833'; // 默认链接
				const managerLink = PRODUCT_MANAGER_SERVICE_LINKS.find(item => item.manager === pm);
				if (managerLink) {
					serviceLink = managerLink.serviceLink;
				}
				
				// console.log(serviceLink);
				wx.openCustomerServiceChat({
					extInfo: {
						url: serviceLink
					},
					corpId: 'wwa76e36d25343b6b9',
					success(res) {
					}
				})
			},
			formatAPIData(row) {
				const formattedItem = {
					rowId: row.rowid || '',
					children: [],
					likeCount: parseInt(row['DZS']) || 0,
					isLiked: false,
					showHeart: false
				};
				Object.keys(FIELD_MAPPING).forEach(key => {
					const apiFieldId = FIELD_MAPPING[key];
					formattedItem[key] = row[apiFieldId] || '';
				});
				
				// Ensure caseStudiesCount is parsed as a number
				formattedItem.caseStudiesCount = parseInt(row[FIELD_MAPPING.caseStudiesCount]) || 0;

				// 添加图片URL处理
				if (formattedItem.imageUrl || row.imageUrl) {
					formattedItem.imageUrl = this.getImageUrl(formattedItem.imageUrl || row.imageUrl);
				}

				// 解析介绍资料
				if (row.DLPP_JSZL) {
					try {
						const materialsData = JSON.parse(row.DLPP_JSZL);
						if (Array.isArray(materialsData) && materialsData.length > 0) {
							formattedItem.introductionMaterials = materialsData.map(material => ({
								name: material.originalFilename || material.name || '未知文件',
								url: material.fileUrl || material.DownloadUrl || material.url || '',
								size: material.filesize || material.size || 0,
								// 保留原始字段以便兼容
								DownloadUrl: material.fileUrl || material.DownloadUrl || material.url || '',
								original_file_full_path: material.fileUrl || material.DownloadUrl || material.url || '',
								original_file_name: material.originalFilename || material.name || '未知文件',
								file_size: material.filesize || material.size || 0
							}));
						} else {
							formattedItem.introductionMaterials = [];
						}
					} catch (e) {
						console.error('解析介绍资料失败:', e);
						formattedItem.introductionMaterials = [];
					}
				} else {
					formattedItem.introductionMaterials = [];
				}

				// 打印解析后的数据
				// console.log('解析后的数据:', formattedItem);

				return formattedItem;
			},
			async handleSearch() {
				if (!this.searchKeyword.trim()) {
					uni.showToast({
						title: '请输入搜索关键词',
						icon: 'none'
					});
					return;
				}

				try {
					const requestData = {
						appKey: '984e1ff028f80150',
						sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
						worksheetId: 'fenlei',
						viewId: '',
						pageSize: 50,
						pageIndex: 1,
						listType: 0,
						controls: [],
						filters: [{
								controlId: '67b2da03ef727a4cd047da1b',
								dataType: 2,
								spliceType: 2,
								filterType: 1,
								value: this.searchKeyword
							},
							{
								controlId: '67b2dd25ef727a4cd047da2a',
								dataType: 2,
								spliceType: 2,
								filterType: 1,
								value: this.searchKeyword
							}
						]
					};

					const response = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',
						method: 'POST',
						data: requestData,
						header: {
							'Content-Type': 'application/json'
						}
					});

					if (response.statusCode !== 200) {
						console.error('请求失败:', response);
						uni.showToast({
							title: '请求失败',
							icon: 'none'
						});
						return;
					}

					if (response.data && response.data.data) {
						if (response.data.data.rows && response.data.data.rows.length > 0) {
							this.categories = response.data.data.rows.map(row => ({
								name: row['67b2da03ef727a4cd047da1b'] || '未命名分类',
								id: row['67b2dd25ef727a4cd047da2a'] || '',
								icon: this.getImageUrl(row['67b2dd25ef727a4cd047da2b']) ||
									'/static/熙迈LOGO.png',
								rowId: row.rowid || '',
								children: [],
								likeCount: parseInt(row['DZS']) || 0,
								isLiked: false
							}));
							console.log(agentBrandImage)
						} else if (response.data.data.total === 0 || response.data.error_code === 1) {
							uni.showToast({
								title: '没有找到相关产品',
								icon: 'none',
								duration: 2000
							});
							this.categories = [];
						}
					} else {
						console.error('接口返回数据格式异常:', response.data);
						uni.showToast({
							title: '数据格式异常',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取分类失败:', error);
					uni.showToast({
						title: '获取分类失败',
						icon: 'none'
					});
				}
			},
			getImageUrl(imageData) {
				try {
					// 支持直接传入数组或JSON字符串
					const parsedData = Array.isArray(imageData) ? imageData : JSON.parse(imageData);

					if (!Array.isArray(parsedData)) {
						console.warn("图片数据格式错误");
						return '/static/熙迈LOGO.png'; // 返回默认图
					}

					// 查找第一个有效URL（优先fileUrl，其次thumbnail_full_path）
					const targetItem = parsedData.find(item =>
						item.fileUrl?.startsWith('http') ||
						item.thumbnail_full_path?.startsWith('http')
					);

					// 返回优先级：fileUrl > thumbnail_full_path > 默认图
					return targetItem?.fileUrl ||
						targetItem?.thumbnail_full_path ||
						'/static/熙迈LOGO.png';

				} catch (error) {
					console.error("解析失败:", error.message);
					return '/static/熙迈LOGO.png'; // 始终返回字符串
				}
			},
			cleanImageUrl(url) {
				try {
					// 解码URL中的特殊字符
					let cleanedUrl = decodeURIComponent(url);
					// 移除URL中的多余空格
					cleanedUrl = cleanedUrl.trim();
					// 确保URL以http或https开头
					if (!cleanedUrl.startsWith('http')) {
						cleanedUrl = `https://${cleanedUrl}`;
					}
					return cleanedUrl;
				} catch (error) {
					console.error('清理图片URL失败:', error);
					return null;
				}
			},
			async handleCategoryClick(item) {
				let recommendations = [];
				let parentMaterialsWithDownloadUrl = [];

				try {
					// 检查是否有相关资料需要获取完整信息
					const hasIntroductionMaterials = item.introductionMaterials && item.introductionMaterials.length > 0;

					// 如果有相关资料，先调用 getFilterRows 获取包含 DownloadUrl 的完整信息
					if (hasIntroductionMaterials) {
						console.log('检测到相关资料，获取完整附件信息...');
						try {
							const materialResponse = await uni.request({
								url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',
								method: 'POST',
								data: {
									appKey: '984e1ff028f80150',
									sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
									worksheetId: 'fenlei',
									pageSize: 1,
									pageIndex: 1,
									listType: 0,
									controls: [],
									filters: [{
										controlId: 'rowid',
										dataType: 2,
										spliceType: 1,
										filterType: 2,
										value: item.rowId
									}]
								},
								header: {
									'Content-Type': 'application/json'
								}
							});

							if (materialResponse.data && materialResponse.data.data && materialResponse.data.data.rows && materialResponse.data.data.rows.length > 0) {
								const productData = materialResponse.data.data.rows[0];
								if (productData.DLPP_JSZL) {
									const materialsData = JSON.parse(productData.DLPP_JSZL);
									console.log('原始附件数据:', materialsData);
									parentMaterialsWithDownloadUrl = materialsData.map(material => {
										console.log('处理单个附件:', material);
										return {
											// 使用正确的字段名
											name: material.original_file_name || '未知文件',
											url: material.DownloadUrl || material.original_file_full_path || '',
											size: material.file_size || 0,
											// 保留完整的字段信息（与 form 页面一致）
											DownloadUrl: material.DownloadUrl || '',
											original_file_full_path: material.original_file_full_path || '',
											original_file_name: material.original_file_name || '未知文件',
											file_size: material.file_size || 0
										};
									});
									console.log('映射后的附件信息:', parentMaterialsWithDownloadUrl);
								}
							}
						} catch (error) {
							console.error('获取完整附件信息失败:', error);
							// 如果获取失败，使用原有的资料信息
							parentMaterialsWithDownloadUrl = item.introductionMaterials || [];
						}
					}

					const subCategoryResponse = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',
						method: 'POST',
						data: {
							appKey: '984e1ff028f80150',
							sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
							worksheetId: 'fenlei',
							pageSize: 50,
							pageIndex: 1,
							rowId: item.rowId,
							controlId: '67b2dd3aef727a4cd047da37',
							getSystemControl: false
						},
						header: {
							'Content-Type': 'application/json'
						}
					});
					const recommendResponse = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',
						method: 'POST',
						data: {
							appKey: '984e1ff028f80150',
							sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
							worksheetId: 'fenlei',
							pageSize: 50,
							pageIndex: 1,
							rowId: item.rowId,
							controlId: 'GLCP',
							getSystemControl: false
						},
						header: {
							'Content-Type': 'application/json'
						}
					});

					if (subCategoryResponse.statusCode !== 200) {
						console.error('请求子分类失败:', response);
						uni.showToast({
							title: '请求子分类失败',
							icon: 'none'
						});
						return;
					}
					

					if (!subCategoryResponse.data || !subCategoryResponse.data.data) {
						console.error('接口返回数据格式异常:', subCategoryResponse.data);
						uni.showToast({
							title: '数据格式异常',
							icon: 'none'
						});
						return;
					}

					if (!subCategoryResponse.data.data.rows || subCategoryResponse.data.data.rows.length === 0) {
						recommendations = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));

						let caseStudies = [];
						if (item.caseStudiesCount > 0) {
							try {
								const caseStudiesResponse = await uni.request({
									url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',
									method: 'POST',
									data: {
										appKey: '984e1ff028f80150',
										sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
										worksheetId: 'fenlei',
										pageSize: 50,
										pageIndex: 1,
										rowId: item.rowId,
										controlId: 'ALQK',
										getSystemControl: false
									},
									header: {
										'Content-Type': 'application/json'
									}
								});
								if (caseStudiesResponse.data && caseStudiesResponse.data.data && caseStudiesResponse.data.data.rows) {
									caseStudies = caseStudiesResponse.data.data.rows.map(row => ({
										clientName: row[FIELD_MAPPING.caseClientName] || '',
										details: row[FIELD_MAPPING.caseDetails] || ''
									}));
								}
							} catch (e) {
								console.error('获取案例情况失败:', e);
							}
						}
						
						// 正常页面跳转：传递基本数据，但不包含推荐产品（避免URL过长）
						const formUrl = `/pages/form/form?${objectToParams(item)}&caseStudies=${encodeURIComponent(JSON.stringify(caseStudies))}`;

						console.log('=== Category页面跳转到Form页面 ===');
						console.log('跳转的产品信息:', {
							productName: item.productName,
							productCode: item.productCode,
							caseStudiesCount: caseStudies.length
						});
						console.log('跳转URL长度:', formUrl.length);

						// 检查URL长度，如果仍然过长则使用简化模式
						if (formUrl.length > 1000) {
							console.log('⚠️ URL仍然过长，使用简化模式');
							const simpleUrl = item.productCode ?
								`/pages/form/form?productCode=${encodeURIComponent(item.productCode)}&rowid=${encodeURIComponent(item.rowId)}&shareMode=1` :
								`/pages/form/form?productName=${encodeURIComponent(item.productName)}&rowid=${encodeURIComponent(item.rowId)}&shareMode=1`;
							uni.navigateTo({
								url: simpleUrl
							});
						} else {
							uni.navigateTo({
								url: formUrl
							});
						}
						return;
					}

					const formattedData = subCategoryResponse.data.data.rows.map(row => this.formatAPIData(row));
					recommendations = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));

					// 传递父级分类的介绍资料（使用包含 DownloadUrl 的完整信息）
					const parentMaterials = parentMaterialsWithDownloadUrl.length > 0 ? parentMaterialsWithDownloadUrl : (item.introductionMaterials || []);
					console.log('传递父级分类介绍资料:', parentMaterials);

					// 传递父级分类的相关链接
					const parentRelatedLinks = item.relatedLinks || [];
					console.log('传递父级分类相关链接:', parentRelatedLinks);

					uni.navigateTo({
						url: `/pages/category/category?title=${encodeURIComponent(item.productName)}&pm=${encodeURIComponent(item.productManager)}&parentImage=${encodeURIComponent(item.imageUrl || '/static/熙迈LOGO.png')}&parentContactPhone=${encodeURIComponent(item.contactPhone || '')}&children=${encodeURIComponent(JSON.stringify(formattedData))}&recommendations=${encodeURIComponent(JSON.stringify(recommendations))}&parentMaterials=${encodeURIComponent(JSON.stringify(parentMaterials))}&parentRelatedLinks=${encodeURIComponent(parentRelatedLinks.join(','))}`
					});
				} catch (error) {
					console.error('获取子分类失败:', error);
					uni.showToast({
						title: '获取子分类失败',
						icon: 'none'
					});
				}
			},
			// 显示父级分类资料列表
			showParentMaterialsList() {
				// currentMaterials 已经在 onLoad 中设置为父级资料
				this.showMaterialsModal = true;
			},
			// 隐藏资料列表
			hideMaterialsList() {
				this.showMaterialsModal = false;
				// 不要清空 currentMaterials，保持按钮显示
			},
			// 预览资料文件
			previewMaterial(material) {
				console.log('预览文件:', material.original_file_name);
				console.log('文件信息:', material);

				// 优先使用 DownloadUrl，如果没有则使用其他URL字段
				const downloadUrl = material.DownloadUrl;
				const originalUrl = material.original_file_full_path;
				const fileName = material.original_file_name;

				console.log('使用 DownloadUrl:', downloadUrl);
				console.log('备用 URL:', originalUrl);
				console.log('文件名:', fileName);

				// 获取文件类型
				const fileType = this.getFileType(fileName);
				console.log('文件类型:', fileType);

				// 根据文件类型选择预览方式
				if (fileType === 'video') {
					// 视频文件预览
					this.previewVideo(downloadUrl || originalUrl, fileName);
				} else if (fileType === 'image') {
					// 图片文件预览
					this.previewImage(downloadUrl || originalUrl, fileName);
				} else {
					// 其他文件类型使用原有的文档预览方式
					this.previewWechatWithFallback(downloadUrl, originalUrl, fileName);
				}
			},
			// 拨打电话
			callnum(num) {
				uni.makePhoneCall({
					phoneNumber: num
				});
			},
			// 带回退机制的预览方法
			previewWechatWithFallback(primaryUrl, fallbackUrl, fileName) {
				console.log('尝试预览文档:', fileName);
				console.log('主要URL:', primaryUrl);
				console.log('备用URL:', fallbackUrl);

				// 先尝试主要URL
				this.previewWechatSingle(primaryUrl, (success) => {
					if (!success && fallbackUrl && fallbackUrl !== primaryUrl) {
						console.log('主要URL失败，尝试备用URL');
						this.previewWechatSingle(fallbackUrl, (success) => {
							if (!success) {
								// 两个URL都失败，显示最终错误
								uni.showModal({
									title: '预览失败',
									content: '文件无法预览，可能是文件格式不支持或网络问题。\n\n是否复制文件链接？',
									confirmText: '复制链接',
									cancelText: '取消',
									success: function(modalRes) {
										if (modalRes.confirm) {
											uni.setClipboardData({
												data: primaryUrl,
												success: function() {
													uni.showToast({
														title: '链接已复制',
														icon: 'success'
													});
												}
											});
										}
									}
								});
							}
						});
					}
				});
			},
			// 单次预览尝试
			previewWechatSingle(urlPdf, callback) {
				if (!urlPdf || typeof urlPdf !== 'string') {
					console.error('无效的文档URL:', urlPdf);
					callback(false);
					return;
				}

				uni.showLoading({
					title: '正在加载中..'
				});

				console.log('开始下载文件:', urlPdf);
				uni.downloadFile({
					url: urlPdf,
					success: function(res) {
						console.log('文件下载成功:', res);
						var filePath = res.tempFilePath;

						if (!filePath) {
							console.error('下载成功但临时文件路径为空');
							uni.hideLoading();
							callback(false);
							return;
						}

						console.log('准备打开文档:', filePath);
						uni.openDocument({
							filePath: filePath,
							showMenu: false,
							success: function(res) {
								console.log('打开文档成功:', res);
								uni.hideLoading();
								callback(true);
							},
							fail: function(err) {
								console.error('打开文档失败:', err);
								uni.hideLoading();
								callback(false);
							}
						});
					},
					fail: function(err) {
						console.error('下载文件失败:', err);
						console.error('失败的URL:', urlPdf);
						uni.hideLoading();
						callback(false);
					}
				});
			},
			// 微信小程序预览文档
			previewWechat(urlPdf) {
				console.log('准备预览文档，URL:', urlPdf);

				// 检查URL是否有效
				if (!urlPdf || typeof urlPdf !== 'string') {
					console.error('无效的文档URL:', urlPdf);
					uni.showToast({
						title: '文档链接无效',
						icon: 'none'
					});
					return;
				}

				uni.showLoading({
					title: '正在加载中..'
				});

				console.log('开始下载文件:', urlPdf);
				uni.downloadFile({
					url: urlPdf,
					success: function(res) {
						console.log('文件下载成功:', res);
						var filePath = res.tempFilePath;

						if (!filePath) {
							console.error('下载成功但临时文件路径为空');
							uni.hideLoading();
							uni.showToast({
								title: '文件路径错误',
								icon: 'none'
							});
							return;
						}

						console.log('准备打开文档:', filePath);
						uni.openDocument({
							filePath: filePath,
							showMenu: false, // 禁用菜单，防止下载
							success: function(res) {
								console.log('打开文档成功:', res);
								uni.hideLoading();
							},
							fail: function(err) {
								console.error('打开文档失败:', err);
								uni.hideLoading();
								uni.showToast({
									title: '文档格式不支持或文件损坏',
									icon: 'none'
								});
							}
						});
					},
					fail: function(err) {
						console.error('下载文件失败:', err);
						console.error('失败的URL:', urlPdf);
						uni.hideLoading();

						// 根据错误类型给出不同提示
						let errorMsg = '文件加载失败';
						if (err.errMsg && err.errMsg.includes('ENOENT')) {
							errorMsg = '文件不存在或已被删除';
						} else if (err.errMsg && err.errMsg.includes('network')) {
							errorMsg = '网络连接失败，请检查网络';
						} else if (err.errMsg && err.errMsg.includes('timeout')) {
							errorMsg = '下载超时，请重试';
						}

						uni.showModal({
							title: '预览失败',
							content: errorMsg + '\n\n是否尝试在浏览器中打开？',
							confirmText: '打开',
							cancelText: '取消',
							success: function(modalRes) {
								if (modalRes.confirm) {
									// 尝试用浏览器打开
									uni.setClipboardData({
										data: urlPdf,
										success: function() {
											uni.showToast({
												title: '链接已复制到剪贴板',
												icon: 'success'
											});
										}
									});
								}
							}
						});
					}
				});
			},
			// 获取文件类型
			getFileType(fileName) {
				const ext = fileName.split('.').pop().toLowerCase();

				// 视频类型
				const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'ogg'];
				if (videoTypes.includes(ext)) return 'video';

				// 图片类型
				const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
				if (imageTypes.includes(ext)) return 'image';

				// PDF类型
				if (ext === 'pdf') return 'pdf';

				// 文档类型
				const docTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
				if (docTypes.includes(ext)) return 'document';

				return 'unknown';
			},
			// 获取文件图标
			getMaterialIcon(fileName) {
				const ext = fileName.split('.').pop().toLowerCase();
				switch (ext) {
					case 'pdf':
						return 'paperplane';
					case 'doc':
					case 'docx':
						return 'compose';
					case 'xls':
					case 'xlsx':
						return 'bars';
					case 'ppt':
					case 'pptx':
						return 'videocam';
					case 'mp4':
					case 'avi':
					case 'mov':
					case 'wmv':
					case 'flv':
					case 'webm':
					case 'mkv':
						return 'videocam-filled';
					case 'jpg':
					case 'jpeg':
					case 'png':
					case 'gif':
					case 'bmp':
					case 'webp':
						return 'image';
					default:
						return 'folder';
				}
			},
			// 预览视频文件
			previewVideo(videoUrl, fileName) {
				console.log('预览视频:', fileName);
				console.log('视频URL:', videoUrl);

				// 在微信小程序中，我们可以使用 video 组件来播放视频
				// 这里我们创建一个简单的视频预览弹窗
				this.showVideoModal = true;
				this.currentVideoUrl = videoUrl;
				this.currentVideoName = fileName;
			},
			// 预览图片文件
			previewImage(imageUrl, fileName) {
				console.log('预览图片:', fileName);
				console.log('图片URL:', imageUrl);

				// 使用微信小程序的图片预览功能
				uni.previewImage({
					urls: [imageUrl],
					current: imageUrl,
					success: function() {
						console.log('图片预览成功');
					},
					fail: function(err) {
						console.error('图片预览失败:', err);
						uni.showToast({
							title: '图片预览失败',
							icon: 'none'
						});
					}
				});
			},
			// 关闭视频预览
			closeVideoModal() {
				this.showVideoModal = false;
				this.currentVideoUrl = '';
				this.currentVideoName = '';
			},
			// 获取链接显示文本
			getLinkDisplayText(link) {
				// 如果是微信公众号文章链接，显示"公众号文章"
				if (link.includes('mp.weixin.qq.com')) {
					return '公众号文章';
				}
				// 如果是视频号链接，显示"视频号"
				if (link.includes('channels.weixin.qq.com') || link.includes('video.weixin.qq.com')) {
					return '视频号';
				}
				// 其他链接显示域名
				try {
					const url = new URL(link);
					return url.hostname;
				} catch (error) {
					return '相关链接';
				}
			},
			// 打开相关链接
			openRelatedLink(link) {
				console.log('打开相关链接:', link);

				// 在微信小程序中，外部链接需要复制到剪贴板
				uni.showModal({
					title: '打开链接',
					content: '即将复制链接到剪贴板，请在浏览器中打开',
					confirmText: '复制链接',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							uni.setClipboardData({
								data: link,
								success: () => {
									uni.showToast({
										title: '链接已复制',
										icon: 'success'
									});
								},
								fail: () => {
									uni.showToast({
										title: '复制失败',
										icon: 'none'
									});
								}
							});
						}
					}
				});
			},
			// 格式化文件大小
			formatFileSize(bytes) {
				if (bytes === 0) return '0 B';
				const k = 1024;
				const sizes = ['B', 'KB', 'MB', 'GB'];
				const i = Math.floor(Math.log(bytes) / Math.log(k));
				return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
			}
		}
	}
</script>

<style>
	.container {
		position: relative;
		background: #42f3f933;
		animation: bg-flow 25s linear infinite;
		min-height: 100vh;
		padding: 0;
		padding-bottom: calc(200rpx + env(safe-area-inset-bottom));
	}

	/* 提升内容层级 */
	.category-list,
	.no-result,
	.recommend-section {
		position: relative;
		z-index: 1;
	}

	.category-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 24rpx;
	}

	.category-item {
		width: calc(50% - 12rpx);
		margin-bottom: 24rpx;
		background-color: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
		overflow: hidden;
		position: relative;
	}

	.img {
		width: 100%;
		height: 250rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;
	}

	.img .icon {
		height: 100%;
		width: 100%;
		display: block;
		object-fit: contain;
	}

	.banner {
		padding: 10rpx;
	}

	.banner-left {
		width: 100%;
	}

	.top-blue {
		height: 60rpx;
		width: 100%;
		border-radius: 15rpx;
		background-color: #6d92cc;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.english-name, .english-name-content {
		color: #ffffff;
		font-size: 16rpx;
		text-align: center;
		line-height: 1.4;
		word-wrap: break-word;
		word-break: break-all;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
	}

	.more-text {
		margin-left: 12rpx;
	}

	.english-name-content .more-text {
		margin-top: 4rpx;
		font-size: 14rpx;
		opacity: 0.9;
	}

	.bottom-white {
		color: #6d92cc;
		min-height: 80rpx;
		width: 100%;
		background-color: #fff;
		display: flex;
		align-items: center;
		padding: 10rpx;
		box-sizing: border-box;
	}

	.bottom-white-text {
		flex: 1;
		min-width: 0;
		font-size: 22rpx;
		text-align: center;
		white-space: normal;
		line-height: 1.4;
		margin: 0 8rpx;
	}

	.logo {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		width: 70rpx;
		height: 70rpx;
	}
	
	.logo .icon {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
		padding: 0;
	}
	
	.like-section {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 10rpx;
		flex-shrink: 0;
	}
	
	.like-icon {
		width: 40rpx;
		height: 40rpx;
	}
	
	.pop-animation {
		position: absolute;
		bottom: 100%; /* 从按钮上方开始 */
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		flex-direction: column;
		animation: pop-up 0.8s ease-out forwards;
		pointer-events: none;
	}
	
	.pop-heart {
		font-size: 30rpx;
	}
	
	.pop-count {
		font-size: 24rpx;
		color: #ff6a6a;
		font-weight: bold;
	}

	@keyframes pop-up {
		0% {
			transform: translateX(-50%) scale(0.5);
			opacity: 0;
			bottom: 100%;
		}
		50% {
			transform: translateX(-50%) scale(1.2);
			opacity: 1;
		}
		100% {
			transform: translateX(-50%) scale(1);
			opacity: 0;
			bottom: 150%; /* 向上飘动 */
		}
	}
	
	.recommend-section {
		padding: 24rpx;
		background-color: #f7f8fa;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 24rpx;
		text-align: center;
		color: #333;
	}
	
	.fixed-customer-service {
		position: fixed;
		bottom: 40rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 90%;
		z-index: 10;
	}

	.no-result {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx;
		margin: 40rpx;
		background-color: #f9f9f9;
		border-radius: 20rpx;
		text-align: center;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	}

	.no-result-icon {
		font-size: 100rpx;
		color: #e0e0e0;
		margin-bottom: 20rpx;
	}

	.no-result-text {
		font-size: 32rpx;
		color: #666;
		margin-bottom: 10rpx;
	}

	.no-result-subtext {
		font-size: 24rpx;
		color: #999;
	}
	
	.service-btn {
		display: flex;
		align-items: center;
		padding: 10rpx;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		border-radius: 24rpx;
		box-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
	}

	.text-container {
		flex: 1;
	}

	.btn-text {
		color: #fff;
		font-size: 38rpx;
		font-weight: 600;
		text-align: center;
		display: block;
		margin-bottom: 0;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.btn-subtext {
		color: rgba(255, 255, 255, 0.9);
		font-size: 24rpx;
		line-height: 1.6;
		text-align: center;
		display: block;
		opacity: 0.9;
		margin-bottom: 16rpx;
	}

	/* 交互动画 */
	.service-btn:active {
		transform: scale(0.98) translateY(2rpx);
		box-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);
	}


	/* 悬浮呼吸动画 */
	@keyframes float {
		0% {
			transform: translateY(0);
		}

		50% {
			transform: translateY(-10rpx);
		}

		100% {
			transform: translateY(0);
		}
	}

	.service-btn {
		animation: float 3s ease-in-out infinite;
	}

	/* 流光边框效果 */
	.service-btn::after {
		content: '';
		position: absolute;
		top: -1rpx;
		left: -1rpx;
		right: -1rpx;
		bottom: -1rpx;
		background: linear-gradient(45deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.3) 50%,
				rgba(255, 255, 255, 0) 100%);
		border-radius: 24rpx;
		animation: shine 3s infinite;
		z-index: -1;
	}

	@keyframes shine {
		0% {
			opacity: 0;
			left: -50%;
		}

		50% {
			opacity: 0.4;
		}

		100% {
			opacity: 0;
			left: 150%;
		}
	}

	/* 介绍资料卡片样式 */
	.materials-card {
		margin: 20rpx;
		margin-bottom: 10rpx;
		background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
		border-radius: 12rpx;
		padding: 12rpx 16rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.1);
		border: 1px solid rgba(0, 122, 255, 0.1);
	}

	.materials-link-button {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.materials-link-text {
		margin-left: 10rpx;
		font-size: 26rpx;
		color: #007aff;
		text-decoration: underline;
		font-weight: 600;
	}

	/* 相关链接卡片样式 */
	.links-card {
		margin: 20rpx;
		margin-bottom: 10rpx;
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		border-radius: 12rpx;
		padding: 16rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
		border: 1px solid rgba(0, 0, 0, 0.05);
	}

	.links-header {
		display: flex;
		align-items: center;
		margin-bottom: 12rpx;
	}

	.links-title {
		margin-left: 10rpx;
		font-size: 26rpx;
		color: #333;
		font-weight: 600;
	}

	.links-list {
		background-color: #fff;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.link-item {
		display: flex;
		align-items: center;
		padding: 16rpx 12rpx;
		border-bottom: 1px solid #f0f0f0;
	}

	.link-item:last-child {
		border-bottom: none;
	}

	.link-item:active {
		background-color: #f5f5f5;
	}

	.link-text {
		flex: 1;
		margin-left: 8rpx;
		font-size: 24rpx;
		color: #333;
	}

	/* 联系信息卡片样式 */
	.contact-card {
		margin: 20rpx;
		margin-bottom: 10rpx;
		background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
		border-radius: 12rpx;
		padding: 8rpx 12rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
		border: 1px solid rgba(0, 0, 0, 0.05);
	}

	.contact-item {
		display: flex;
		align-items: center;
		padding: 4rpx 0;
	}

	.contact-item:not(:last-child) {
		border-bottom: 1px solid rgba(0, 0, 0, 0.06);
		margin-bottom: 2rpx;
		padding-bottom: 6rpx;
	}

	.contact-icon {
		width: 28rpx;
		height: 28rpx;
		background-color: rgba(102, 102, 102, 0.1);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 10rpx;
	}

	.contact-content-inline {
		flex: 1;
		display: flex;
		align-items: center;
	}

	.contact-label {
		font-size: 24rpx;
		color: #666;
		font-weight: 500;
	}

	.contact-text {
		font-size: 24rpx;
		color: #333;
		font-weight: 500;
	}

	.phone-link {
		font-size: 24rpx;
		color: #007aff;
		text-decoration: underline;
		font-weight: 500;
	}

	/* 介绍资料样式 */
	.action-section {
		display: flex;
		align-items: center;
		gap: 16rpx;
	}

	.materials-btn {
		display: flex;
		align-items: center;
		background-color: rgba(41, 121, 255, 0.1);
		border-radius: 20rpx;
		padding: 8rpx 12rpx;
		gap: 6rpx;
	}

	.materials-count {
		font-size: 22rpx;
		color: #2979FF;
		font-weight: 500;
	}

	/* 弹窗样式 */
	.materials-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.materials-modal-content {
		width: 90%;
		max-height: 70%;
		background-color: white;
		border-radius: 16rpx;
		overflow: hidden;
	}

	.materials-modal-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 1px solid #eee;
	}

	.materials-modal-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
	}

	.materials-modal-close {
		padding: 10rpx;
	}

	.materials-modal-body {
		max-height: 60vh;
		overflow-y: auto;
		padding: 20rpx;
	}

	.material-item {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-radius: 12rpx;
		background-color: #f8f9fa;
		margin-bottom: 16rpx;
	}

	.material-item:last-child {
		margin-bottom: 0;
	}

	.material-icon {
		margin-right: 20rpx;
	}

	.material-info {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.material-name {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 8rpx;
		word-break: break-all;
	}

	.material-size {
		font-size: 24rpx;
		color: #999;
	}

	.material-action {
		margin-left: 20rpx;
	}

	/* 视频预览弹窗样式 */
	.video-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.9);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 2000;
	}

	.video-modal-content {
		width: 95%;
		max-width: 800rpx;
		background-color: #000;
		border-radius: 16rpx;
		overflow: hidden;
	}

	.video-modal-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: rgba(0, 0, 0, 0.8);
	}

	.video-modal-title {
		font-size: 28rpx;
		color: #fff;
		flex: 1;
		margin-right: 20rpx;
		word-break: break-all;
	}

	.video-modal-close {
		padding: 10rpx;
	}

	.video-modal-body {
		position: relative;
	}

	.video-player {
		width: 100%;
		height: 400rpx;
		background-color: #000;
	}
</style>