{"version": 3, "file": "form.js", "sources": ["pages/form/form.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZm9ybS9mb3JtLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 基本信息卡片 -->\n\t\t<view class=\"info-card\">\n\t\t\t<view class=\"card-header\">产品信息</view>\n\t\t\t<view class=\"card-body\">\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"text\">产品名称: {{productName}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"text\">产品编码: {{productCode}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"text\">产品经理: {{productManager}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"phone-link\" @click=\"callnum(contactPhone)\">联系方式: {{contactPhone}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"text\">业务类型: {{YWLX}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 代理信息卡片 -->\n\t\t<view v-if=\"YWLX === '代理'\" class=\"info-card agent-section\">\n\t\t\t<view class=\"card-header\">代理信息</view>\n\t\t\t<view class=\"card-body\">\n\t\t\t\t<view class=\"form-item link-item\" @click=\"openDLPPGW\">\n\t\t\t\t\t<uni-icons type=\"link\" size=\"18\" color=\"#2979FF\"></uni-icons>\n\t\t\t\t\t<text class=\"link-text\">代理品牌官网</text>\n\t\t\t\t\t<uni-icons type=\"forward\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"full-url\" selectable @tap.stop=\"copyUrl(DLPPGW)\">{{DLPPGW}}</text>\n\t\t\t\t<view class=\"form-item brand-image\">\n\t\t\t\t\t<text class=\"text\">代理品牌:</text>\n\t\t\t\t\t<image\n\t\t\t\t\t\t:src=\"agentBrandImage\"\n\t\t\t\t\t\tmode=\"aspectFit\"\n\t\t\t\t\t\tclass=\"brand-logo\"\n\t\t\t\t\t\t@tap=\"previewAgentImage\"\n\t\t\t\t\t></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 联系信息卡片 -->\n\t\t<view class=\"info-card\">\n\t\t\t<view class=\"card-body\">\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<text class=\"text\">服务内容简介: {{serviceDescription}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t<image v-if=\"highResImageUrl\" :src=\"highResImageUrl\" mode=\"aspectFit\" class=\"image\"\n\t\t\t\t\t\t@tap=\"previewImage\"></image>\n\t\t\t\t\t<image v-else src=\"/static/熙迈LOGO.png\" mode=\"aspectFit\" class=\"image\"></image>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 介绍资料按钮 -->\n\t\t<view v-if=\"introductionMaterials.length > 0\" class=\"info-card materials-section\">\n\t\t\t<view class=\"card-body\">\n\t\t\t\t<view class=\"materials-button\" @tap=\"showMaterialsList\">\n\t\t\t\t\t<uni-icons type=\"folder\" size=\"20\" color=\"#2979FF\"></uni-icons>\n\t\t\t\t\t<text class=\"materials-text\">查看相关资料 ({{introductionMaterials.length}}个文件)</text>\n\t\t\t\t\t<uni-icons type=\"forward\" size=\"14\" color=\"#999\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 相关链接按钮 -->\n\t\t<view v-if=\"relatedLinks.length > 0\" class=\"info-card links-section\">\n\t\t\t<view class=\"card-body\">\n\t\t\t\t<view class=\"links-header\">\n\t\t\t\t\t<uni-icons type=\"link\" size=\"20\" color=\"#2979FF\"></uni-icons>\n\t\t\t\t\t<text class=\"links-title\">相关视频号/公众号 ({{relatedLinks.length}}个链接)</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"links-list\">\n\t\t\t\t\t<view v-for=\"(link, index) in relatedLinks\" :key=\"index\"\n\t\t\t\t\t\t  class=\"link-item\" @tap=\"openRelatedLink(link)\">\n\t\t\t\t\t\t<uni-icons type=\"paperplane-filled\" size=\"16\" color=\"#2979FF\"></uni-icons>\n\t\t\t\t\t\t<text class=\"link-text\">{{ getLinkDisplayText(link) }}</text>\n\t\t\t\t\t\t<uni-icons type=\"right\" size=\"12\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 案例情况卡片 -->\n\t\t<view v-if=\"caseStudies.length > 0\" class=\"info-card case-studies-section\">\n\t\t\t<view class=\"card-header\">相关案例</view>\n\t\t\t<view class=\"card-body\">\n\t\t\t\t<view v-for=\"(study, index) in caseStudies\" :key=\"index\" class=\"case-study-item\">\n\t\t\t\t\t<text class=\"case-study-client\">{{ study.clientName }}</text>\n\t\t\t\t\t<text class=\"case-study-details\">{{ study.details }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<view v-if=\"recommendations.length > 0\" class=\"recommend-section\">\n\t\t  <view class=\"section-title\">您可能对以下产品感兴趣</view>\n\t\t  <view class=\"category-list\">\n\t\t\t\t<view v-for=\"(item, index) in recommendations\" :key=\"index\" class=\"category-item\"\n\t\t\t\t\t@click=\"handleCategoryClick(item)\">\n\t\t\t\t\t<view class=\"img\">\n\t\t\t\t\t\t<image class=\"icon\" :src=\"item.imageUrl || '/static/熙迈LOGO.png'\" mode=\"heightFix\"></image>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"banner\">\n\t\t\t\t\t\t<view class=\"banner-left\">\n\t\t\t\t\t\t\t<view class=\"top-blue\">\n\t\t\t\t\t\t\t\t<text class=\"english-name\">\n\t\t\t\t\t\t\t\t\t{{ item.englishName }}\n\t\t\t\t\t\t\t\t\t<span style=\"display:inline-block;width:16rpx;\"></span>\n\t\t\t\t\t\t\t\t\t<text class=\"more-text\" style=\"margin-left: 12rpx;\">更多&gt;&gt;</text>\n\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"bottom-white\">\n\t\t\t\t\t\t\t\t<view class=\"logo\">\n\t\t\t\t\t\t\t\t\t<image v-if=\"item.YWLX === '代理'\" class=\"icon\"\n\t\t\t\t\t\t\t\t\t\t:src=\"'/static/代理图标.png'\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t\t<image v-else class=\"icon\"\n\t\t\t\t\t\t\t\t\t\t:src=\"'/static/熙迈LOGO.png'\" mode=\"aspectFit\"></image>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<view class=\"bottom-white-text\">{{item.productName}}</view>\n\t\t\t\t\t\t\t\t<view class=\"like-section\" @click.stop=\"handleLike(item)\">\n\t\t\t\t\t\t\t\t\t<image class=\"like-icon\" :src=\"item.isLiked ? '/static/红色小红心.svg' : '/static/灰色小红心.svg'\"></image>\n\t\t\t\t\t\t\t\t\t<view v-if=\"item.showHeart\" class=\"pop-animation\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"pop-heart\">❤️</text>\n\t\t\t\t\t\t\t\t\t\t<text class=\"pop-count\">{{ item.likeCount }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t  </view>\n\t\t</view>\n\n\t\t<!-- 固定底部按钮 -->\n<!-- \t\t<view class=\"get_quote\" @click=\"callnum(contactPhone)\">\n\t\t\t<view class=\"quote-container\">\n\t\t\t\t<uni-icons type=\"notification\" size=\"24\" color=\"#fff\"></uni-icons>\n\t\t\t\t<text class=\"quote-text\">获取报价</text>\n\t\t\t</view>\n\t\t</view> -->\n\t</view>\n\n\t<!-- 介绍资料弹窗 -->\n\t<view v-if=\"showMaterialsModal\" class=\"materials-modal\" @tap=\"hideMaterialsList\">\n\t\t<view class=\"materials-modal-content\" @tap.stop>\n\t\t\t<view class=\"materials-modal-header\">\n\t\t\t\t<text class=\"materials-modal-title\">相关资料</text>\n\t\t\t\t<view class=\"materials-modal-close\" @tap=\"hideMaterialsList\">\n\t\t\t\t\t<uni-icons type=\"close\" size=\"20\" color=\"#666\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"materials-modal-body\">\n\t\t\t\t<view v-for=\"(material, index) in introductionMaterials\" :key=\"index\"\n\t\t\t\t\t  class=\"material-item\" @tap=\"previewMaterial(material)\">\n\t\t\t\t\t<view class=\"material-icon\">\n\t\t\t\t\t\t<uni-icons :type=\"getMaterialIcon(material.original_file_name)\" size=\"24\" color=\"#2979FF\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"material-info\">\n\t\t\t\t\t\t<text class=\"material-name\">{{ material.original_file_name }}</text>\n\t\t\t\t\t\t<text class=\"material-size\">{{ formatFileSize(material.file_size) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"material-action\">\n\t\t\t\t\t\t<uni-icons type=\"eye\" size=\"16\" color=\"#999\"></uni-icons>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\n\t<!-- 视频预览弹窗 -->\n\t<view v-if=\"showVideoModal\" class=\"video-modal\" @tap=\"closeVideoModal\">\n\t\t<view class=\"video-modal-content\" @tap.stop>\n\t\t\t<view class=\"video-modal-header\">\n\t\t\t\t<text class=\"video-modal-title\">{{ currentVideoName }}</text>\n\t\t\t\t<view class=\"video-modal-close\" @tap=\"closeVideoModal\">\n\t\t\t\t\t<uni-icons type=\"close\" size=\"20\" color=\"#fff\"></uni-icons>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"video-modal-body\">\n\t\t\t\t<video\n\t\t\t\t\t:src=\"currentVideoUrl\"\n\t\t\t\t\tcontrols\n\t\t\t\t\tautoplay\n\t\t\t\t\t:show-fullscreen-btn=\"true\"\n\t\t\t\t\t:show-play-btn=\"true\"\n\t\t\t\t\t:show-center-play-btn=\"true\"\n\t\t\t\t\tclass=\"video-player\"\n\t\t\t\t></video>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n\n\t<view class=\"fixed-customer-service\">\n\t\t<button class=\"service-btn\"  @click=\"handleContact(productManager)\">\n\t\t\t<view class=\"text-container\">\n\t\t\t\t<text class=\"btn-text\">微信客服</text>\n\t\t\t\t<text class=\"btn-subtext\">如有需求，请点我联系</text>\n\t\t\t</view>\n\t\t</button>\n\t</view>\n</template>\n\n<script>\n\timport {\n\t\tFIELD_MAPPING,\n\t\tPRODUCT_MANAGER_SERVICE_LINKS\n\t} from '@/config/fields.js';\n\timport {\n\t\tobjectToParams\n\t} from '@/utils/url.js';\n\timport ShareUtils from '@/utils/share.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcontactPhone: '',\n\t\t\t\tproductName: '',\n\t\t\t\tproductCode: '',\n\t\t\t\tproductManager: '',\n\t\t\t\tcontactInfo: '',\n\t\t\t\tserviceDescription: '',\n\t\t\t\timageUrl: '',\n\t\t\t\thighResImageUrl: '',\n\t\t\t\tYWLX: '',\n\t\t\t\tagentBrandImage: '',\n\t\t\t\tDLPPGW: '',\n\t\t\t\tchanpingtu: '',\n\t\t\t\tenglishName: '',\n\t\t\t\tDLPPLOGO: '',\n\t\t\t\trecommendations: [],\n\t\t\t\tlikesLog: {}, // 新增点赞记录\n\t\t\t\tcaseStudies: [], // 新增案例情况\n\t\t\t\tintroductionMaterials: [], // 介绍资料\n\t\t\t\trelatedLinks: [], // 相关视频号/公众号链接\n\t\t\t\tshowMaterialsModal: false, // 控制资料弹窗显示\n\t\t\t\trowid: '', // 当前产品的rowid\n\t\t\t\tshowVideoModal: false, // 控制视频预览弹窗显示\n\t\t\t\tcurrentVideoUrl: '', // 当前预览的视频URL\n\t\t\t\tcurrentVideoName: '' // 当前预览的视频文件名\n\t\t\t}\n\t\t},\n\t\tasync onLoad(options) {\n\n\n\t\t\t// 检查是否是分享模式\n\t\t\tif (options.shareMode === '1') {\n\t\t\t\t// 分享模式：只有基本信息，需要重新获取数据\n\t\t\t\tthis.productName = decodeURIComponent(options.productName || '');\n\t\t\t\tthis.productCode = decodeURIComponent(options.productCode || '');\n\t\t\t\tthis.rowid = decodeURIComponent(options.rowid || ''); // 添加rowid处理\n\t\t\t\tconsole.log('🔄 分享模式，产品信息:', {\n\t\t\t\t\tproductName: this.productName,\n\t\t\t\t\tproductCode: this.productCode,\n\t\t\t\t\trowid: this.rowid\n\t\t\t\t});\n\n\n\n\t\t\t\tif (!this.productName && !this.productCode) {\n\t\t\t\t\tconsole.log('❌ 缺少必要的产品信息');\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '提示',\n\t\t\t\t\t\tcontent: '分享链接中缺少产品信息，可能是因为产品名称过长导致的。请从产品列表重新进入此页面。',\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tconfirmText: '返回首页',\n\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\t\turl: '/pages/home/<USER>'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tthis.loadDataFromAPI();\n\t\t\t} else {\n\t\t\t\tconsole.log('📝 正常模式，解析URL参数中的完整数据');\n\t\t\t\t// 正常模式：从URL参数解析所有数据\n\t\t\t\tthis.serviceDescription = decodeURIComponent(options.serviceDescription || '');\n\t\t\t\tthis.productName = decodeURIComponent(options.productName || '');\n\t\t\t\tthis.productCode = decodeURIComponent(options.productCode || '');\n\t\t\t\tthis.productManager = decodeURIComponent(options.productManager || '');\n\t\t\t\tthis.contactInfo = decodeURIComponent(options.contactInfo || '');\n\t\t\t\tthis.YWLX = decodeURIComponent(options.YWLX || '');\n\t\t\t\tthis.contactPhone = decodeURIComponent(options.contactPhone || '');\n\t\t\t\tthis.agentBrandImage = decodeURIComponent(options.agentBrandImage || '');\n\t\t\t\tthis.DLPPGW = decodeURIComponent(options.DLPPGW || '');\n\t\t\t\tthis.chanpingtu = decodeURIComponent(options.chanpingtu || '');\n\t\t\t\tthis.englishName = decodeURIComponent(options.englishName || '');\n\t\t\t\tthis.DLPPLOGO = decodeURIComponent(options.DLPPLOGO || '');\n\t\t\t\tif (this.contactPhone.startsWith('+86')) {\n\t\t\t\t\tthis.contactPhone = this.contactPhone.substring(3); // 去掉前3个字符\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tthis.recommendations = JSON.parse(decodeURIComponent(options.recommendations || '[]'))\n\t\t\t\t\tthis.recommendations.sort((a, b) => b.likeCount - a.likeCount);\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('解析推荐数据失败:', error)\n\t\t\t\t}\n\t\t\t\t// 解析案例情况数据\n\t\t\t\ttry {\n\t\t\t\t\tthis.caseStudies = JSON.parse(decodeURIComponent(options.caseStudies || '[]'));\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('解析案例情况数据失败:', error);\n\t\t\t\t}\n\t\t\t\t// 解析介绍资料数据\n\t\t\t\ttry {\n\t\t\t\t\tthis.introductionMaterials = JSON.parse(decodeURIComponent(options.introductionMaterials || '[]'));\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('解析介绍资料数据失败:', error);\n\t\t\t\t}\n\t\t\t\t// 解析相关链接数据\n\t\t\t\ttry {\n\t\t\t\t\tconst relatedLinksStr = decodeURIComponent(options.relatedLinks || '');\n\t\t\t\t\tconsole.log('🔗 正常模式解析相关链接:', relatedLinksStr);\n\t\t\t\t\tif (relatedLinksStr) {\n\t\t\t\t\t\t// 用逗号分隔链接，并过滤空字符串\n\t\t\t\t\t\tthis.relatedLinks = relatedLinksStr.split(',')\n\t\t\t\t\t\t\t.map(link => link.trim())\n\t\t\t\t\t\t\t.filter(link => link.length > 0);\n\t\t\t\t\t\tconsole.log('✅ 正常模式相关链接解析完成:', this.relatedLinks);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.relatedLinks = [];\n\t\t\t\t\t\tconsole.log('❌ 正常模式相关链接数据为空');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('解析相关链接数据失败:', error);\n\t\t\t\t\tthis.relatedLinks = [];\n\t\t\t\t}\n\t\t\t}\n\t\t\t// 解析图片URL\n\t\t\ttry {\n\t\t\t\tconst imageData = decodeURIComponent(options.imageUrl || '');\n\t\t\t\tconst chanpingtuData = decodeURIComponent(options.chanpingtu || '');\n\n\t\t\t\tif (imageData.startsWith('http')) {\n\t\t\t\t\tthis.imageUrl = imageData;\n\t\t\t\t} else {\n\t\t\t\t\t// 使用getImageUrl方法获取最高质量的图片\n\t\t\t\t\tthis.imageUrl = this.getImageUrl(imageData);\n\t\t\t\t}\n\n\t\t\t\t// 优先使用chanpingtu作为高清图片\n\t\t\t\tif (chanpingtuData) {\n\t\t\t\t\tif (chanpingtuData.startsWith('http')) {\n\t\t\t\t\t\tthis.highResImageUrl = chanpingtuData;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.highResImageUrl = this.getImageUrl(chanpingtuData);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.highResImageUrl = this.imageUrl;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('解析图片URL失败:', error);\n\t\t\t\tthis.imageUrl = '/static/熙迈LOGO.png';\n\t\t\t\tthis.highResImageUrl = this.imageUrl;\n\t\t\t}\n\t\t\ttry {\n\t\t\t\tconst dlppData = decodeURIComponent(options.agentBrandImage || '');\n\t\t\t\t// console.log('Raw dlppData:', dlppData); // 打印原始数据\n\n\t\t\t\tif (dlppData.startsWith('/static/') || dlppData.startsWith('http://') || dlppData.startsWith('https://')) {\n\t\t\t\t\tthis.agentBrandImage = dlppData;\n\t\t\t\t} else {\n\t\t\t\t\t// 否则尝试解析为 JSON\n\t\t\t\t\tconst dlppArray = JSON.parse(dlppData || '[]');\n\t\t\t\t\tif (dlppArray.length > 0 && dlppArray[0].fileUrl) {\n\t\t\t\t\t\tthis.agentBrandImage = dlppArray[0].fileUrl.startsWith('http') ?\n\t\t\t\t\t\t\tdlppArray[0].fileUrl :\n\t\t\t\t\t\t\t`https://${dlppArray[0].fileUrl}`;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.agentBrandImage = '/static/代理图标.png'; // 默认值\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('解析代理品牌图片失败:', error);\n\t\t\t\tthis.agentBrandImage = '/static/代理图标.png'; // 默认值\n\t\t\t}\n\n\t\t\t// 如果没有推荐产品数据，则自动获取\n\t\t\tif (this.recommendations.length === 0) {\n\t\t\t\tawait this.loadRecommendationsAndCases();\n\t\t\t}\n\n\n\t\t},\n\n\t\t// 分享功能\n\t\tonShareAppMessage(res) {\n\t\t\tconsole.log('=== Form页面分享给好友 ===');\n\t\t\tconsole.log('分享触发参数:', res);\n\t\t\tconsole.log('当前页面数据:', {\n\t\t\t\tproductName: this.productName,\n\t\t\t\tproductCode: this.productCode,\n\t\t\t\tproductManager: this.productManager,\n\t\t\t\timageUrl: this.imageUrl,\n\t\t\t\trecommendationsCount: this.recommendations.length\n\t\t\t});\n\n\t\t\tconst shareTitle = this.productName ? `${this.productName} - 熙迈科技` : '熙迈科技产品详情';\n\n\t\t\t// 由于分享链接长度限制，优先传递产品代码（通常较短）\n\t\t\tlet sharePath;\n\t\t\tif (this.productCode) {\n\t\t\t\t// 优先使用产品代码，因为通常比产品名称短\n\t\t\t\tsharePath = `pages/form/form?productCode=${encodeURIComponent(this.productCode)}&shareMode=1`;\n\t\t\t} else if (this.productName) {\n\t\t\t\t// 如果没有产品代码，使用产品名称\n\t\t\t\tsharePath = `pages/form/form?productName=${encodeURIComponent(this.productName)}&shareMode=1`;\n\t\t\t} else {\n\t\t\t\t// 都没有的话，只传递分享模式\n\t\t\t\tsharePath = `pages/form/form?shareMode=1`;\n\t\t\t}\n\n\t\t\t// 检查URL长度，如果超过限制则进一步简化\n\t\t\tif (sharePath.length > 200) {\n\t\t\t\tconsole.log('⚠️ 分享路径过长，进一步简化');\n\t\t\t\tif (this.productCode && this.productCode.length < 50) {\n\t\t\t\t\t// 如果产品代码不太长，保留产品代码\n\t\t\t\t\tsharePath = `pages/form/form?productCode=${encodeURIComponent(this.productCode)}&shareMode=1`;\n\t\t\t\t} else {\n\t\t\t\t\t// 否则只保留分享模式，页面将显示错误提示\n\t\t\t\t\tsharePath = `pages/form/form?shareMode=1`;\n\t\t\t\t\tconsole.log('⚠️ 产品信息过长，分享链接将无法包含产品信息');\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconsole.log('构建的分享路径:', sharePath);\n\t\t\tconsole.log('分享路径长度:', sharePath.length);\n\n\t\t\tconst shareConfig = ShareUtils.getDefaultShareConfig({\n\t\t\t\ttitle: shareTitle,\n\t\t\t\tpath: sharePath,\n\t\t\t\timageUrl: this.highResImageUrl || this.imageUrl || '/static/熙迈LOGO.png'\n\t\t\t});\n\n\t\t\tconsole.log('最终分享配置:', shareConfig);\n\t\t\tconsole.log('=== Form页面分享配置完成 ===');\n\n\t\t\treturn shareConfig;\n\t\t},\n\n\t\tonShareTimeline(res) {\n\t\t\tconsole.log('=== Form页面分享到朋友圈 ===');\n\t\t\tconsole.log('分享触发参数:', res);\n\n\t\t\tconst shareTitle = this.productName ? `${this.productName} - 熙迈科技` : '熙迈科技产品详情';\n\n\t\t\t// 由于分享链接长度限制，优先传递产品代码（通常较短）\n\t\t\tlet sharePath;\n\t\t\tif (this.productCode) {\n\t\t\t\t// 优先使用产品代码，因为通常比产品名称短\n\t\t\t\tsharePath = `pages/form/form?productCode=${encodeURIComponent(this.productCode)}&shareMode=1`;\n\t\t\t} else if (this.productName) {\n\t\t\t\t// 如果没有产品代码，使用产品名称\n\t\t\t\tsharePath = `pages/form/form?productName=${encodeURIComponent(this.productName)}&shareMode=1`;\n\t\t\t} else {\n\t\t\t\t// 都没有的话，只传递分享模式\n\t\t\t\tsharePath = `pages/form/form?shareMode=1`;\n\t\t\t}\n\n\t\t\t// 检查URL长度，如果超过限制则进一步简化\n\t\t\tif (sharePath.length > 200) {\n\t\t\t\tconsole.log('⚠️ 分享路径过长，进一步简化');\n\t\t\t\tif (this.productCode && this.productCode.length < 50) {\n\t\t\t\t\t// 如果产品代码不太长，保留产品代码\n\t\t\t\t\tsharePath = `pages/form/form?productCode=${encodeURIComponent(this.productCode)}&shareMode=1`;\n\t\t\t\t} else {\n\t\t\t\t\t// 否则只保留分享模式，页面将显示错误提示\n\t\t\t\t\tsharePath = `pages/form/form?shareMode=1`;\n\t\t\t\t\tconsole.log('⚠️ 产品信息过长，分享链接将无法包含产品信息');\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconsole.log('构建的分享路径:', sharePath);\n\n\t\t\tconst shareConfig = ShareUtils.getDefaultShareConfig({\n\t\t\t\ttitle: shareTitle,\n\t\t\t\tpath: sharePath,\n\t\t\t\timageUrl: this.highResImageUrl || this.imageUrl || '/static/熙迈LOGO.png'\n\t\t\t});\n\n\t\t\tconsole.log('最终分享配置:', shareConfig);\n\t\t\tconsole.log('=== Form页面朋友圈分享配置完成 ===');\n\n\t\t\treturn shareConfig;\n\t\t},\n\n\t\tmethods: {\n\t\t\t// 从API重新加载数据（用于分享模式）\n\t\t\tasync loadDataFromAPI() {\n\n\n\t\t\t\ttry {\n\t\t\t\t\t// 根据产品名称或产品编码搜索对应的产品数据\n\t\t\t\t\tconst filters = [];\n\n\t\t\t\t\t// 优先使用产品编码搜索，因为它更精确\n\t\t\t\t\tif (this.productCode) {\n\n\t\t\t\t\t\tfilters.push({\n\t\t\t\t\t\t\tcontrolId: FIELD_MAPPING.productCode, // 使用配置中的产品编码字段ID\n\t\t\t\t\t\t\tdataType: 2,\n\t\t\t\t\t\t\tspliceType: 1,\n\t\t\t\t\t\t\tfilterType: 2, // 等于（完全匹配）\n\t\t\t\t\t\t\tvalue: this.productCode\n\t\t\t\t\t\t});\n\t\t\t\t\t} else if (this.productName) {\n\n\t\t\t\t\t\t// 如果没有产品编码，则使用产品名称\n\t\t\t\t\t\tfilters.push({\n\t\t\t\t\t\t\tcontrolId: FIELD_MAPPING.productName, // 使用配置中的产品名称字段ID\n\t\t\t\t\t\t\tdataType: 2,\n\t\t\t\t\t\t\tspliceType: 1,\n\t\t\t\t\t\t\tfilterType: 2, // 等于（完全匹配）\n\t\t\t\t\t\t\tvalue: this.productName\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\n\n\n\t\t\t\t\tif (filters.length === 0) {\n\t\t\t\t\t\tconsole.log('❌ 没有有效的搜索条件');\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '缺少产品信息',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tconst requestData = {\n\t\t\t\t\t\tappKey: '984e1ff028f80150',\n\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\n\t\t\t\t\t\tworksheetId: 'fenlei',\n\t\t\t\t\t\tpageSize: 1,\n\t\t\t\t\t\tpageIndex: 1,\n\t\t\t\t\t\tlistType: 0,\n\t\t\t\t\t\tcontrols: [],\n\t\t\t\t\t\tfilters: filters\n\t\t\t\t\t};\n\n\n\n\t\t\t\t\tconst response = await uni.request({\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\tdata: requestData,\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\n\n\t\t\t\t\tif (response.data && response.data.data) {\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif (response.data && response.data.data && response.data.data.rows && response.data.data.rows.length > 0) {\n\t\t\t\t\t\tconst productData = response.data.data.rows[0];\n\t\t\t\t\t\tconst formattedData = this.formatAPIData(productData);\n\n\t\t\t\t\t\t// 更新页面数据\n\t\t\t\t\t\tthis.productName = formattedData.productName || '';  // 添加产品名称\n\t\t\t\t\t\tthis.productCode = formattedData.productCode || this.productCode;  // 更新产品编码\n\t\t\t\t\t\tthis.serviceDescription = formattedData.serviceDescription || '';\n\t\t\t\t\t\tthis.productManager = formattedData.productManager || '';\n\t\t\t\t\t\tthis.contactInfo = formattedData.contactInfo || '';\n\t\t\t\t\t\tthis.YWLX = formattedData.YWLX || '';\n\t\t\t\t\t\tthis.contactPhone = formattedData.contactPhone || '';\n\t\t\t\t\t\t// 处理代理品牌图片（已在formatAPIData中处理过，直接赋值）\n\t\t\t\t\t\tthis.agentBrandImage = formattedData.agentBrandImage || '/static/代理图标.png';\n\t\t\t\t\t\tthis.DLPPGW = formattedData.DLPPGW || '';\n\t\t\t\t\t\tthis.chanpingtu = formattedData.chanpingtu || '';\n\t\t\t\t\t\tthis.englishName = formattedData.englishName || '';\n\t\t\t\t\t\tthis.DLPPLOGO = formattedData.DLPPLOGO || '';\n\t\t\t\t\t\tthis.imageUrl = formattedData.imageUrl || '/static/熙迈LOGO.png';  // 添加产品图片\n\t\t\t\t\t\t// 优先使用chanpingtu字段作为高清图片，如果没有则使用imageUrl\n\t\t\t\t\t\tthis.highResImageUrl = formattedData.chanpingtu || formattedData.imageUrl || '/static/熙迈LOGO.png';  // 添加高清图片\n\t\t\t\t\t\tthis.introductionMaterials = formattedData.introductionMaterials || [];  // 添加介绍资料\n\t\t\t\t\t\tthis.relatedLinks = formattedData.relatedLinks || [];  // 添加相关链接\n\t\t\t\t\t\tconsole.log('🔗 重新加载数据时设置相关链接:', this.relatedLinks);\n\t\t\t\t\t\tthis.rowid = formattedData.rowId || '';  // 添加rowid\n\n\t\t\t\t\t\tif (this.contactPhone.startsWith('+86')) {\n\t\t\t\t\t\t\tthis.contactPhone = this.contactPhone.substring(3);\n\t\t\t\t\t\t}\n\n\n\t\t\t\t\t\t// 获取推荐数据和案例数据\n\t\t\t\t\t\tawait this.loadRecommendationsAndCases();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log('❌ 未找到匹配的产品数据');\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '未找到产品信息',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('重新加载数据失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '加载数据失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 加载推荐数据和案例数据\n\t\t\tasync loadRecommendationsAndCases() {\n\t\t\t\ttry {\n\t\t\t\t\t// 如果有rowid，使用关联API获取精确的推荐和案例数据\n\t\t\t\t\tif (this.rowid) {\n\t\t\t\t\t\tconsole.log('使用rowid获取关联数据:', this.rowid);\n\n\t\t\t\t\t\t// 获取推荐数据\n\t\t\t\t\t\tconst recommendResponse = await uni.request({\n\t\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',\n\t\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\n\t\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\n\t\t\t\t\t\t\t\tworksheetId: 'fenlei',\n\t\t\t\t\t\t\t\trowId: this.rowid,\n\t\t\t\t\t\t\t\tcontrolId: 'GLCP', // 推荐产品字段ID\n\t\t\t\t\t\t\t\tpageSize: 10,\n\t\t\t\t\t\t\t\tpageIndex: 1\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tif (recommendResponse.data && recommendResponse.data.data && recommendResponse.data.data.rows) {\n\t\t\t\t\t\t\tthis.recommendations = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));\n\t\t\t\t\t\t\tthis.recommendations.sort((a, b) => b.likeCount - a.likeCount);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 获取案例数据\n\t\t\t\t\t\tconst caseStudiesResponse = await uni.request({\n\t\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',\n\t\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\n\t\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\n\t\t\t\t\t\t\t\tworksheetId: 'fenlei',\n\t\t\t\t\t\t\t\trowId: this.rowid,\n\t\t\t\t\t\t\t\tcontrolId: 'ALQK', // 案例字段ID\n\t\t\t\t\t\t\t\tpageSize: 10,\n\t\t\t\t\t\t\t\tpageIndex: 1\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tif (caseStudiesResponse.data && caseStudiesResponse.data.data && caseStudiesResponse.data.data.rows) {\n\t\t\t\t\t\t\tthis.caseStudies = caseStudiesResponse.data.data.rows.map(row => ({\n\t\t\t\t\t\t\t\tclientName: row[FIELD_MAPPING.caseClientName] || '',\n\t\t\t\t\t\t\t\tdetails: row[FIELD_MAPPING.caseDetails] || ''\n\t\t\t\t\t\t\t}));\n\t\t\t\t\t\t\tconsole.log('✅ 获取到案例数据，数量:', this.caseStudies.length);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.caseStudies = [];\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.log('没有rowid，使用通用推荐数据');\n\n\t\t\t\t\t\t// 降级方案：获取通用推荐数据\n\t\t\t\t\t\tconst recommendResponse = await uni.request({\n\t\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',\n\t\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\n\t\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\n\t\t\t\t\t\t\t\tworksheetId: 'fenlei',\n\t\t\t\t\t\t\t\tpageSize: 6,\n\t\t\t\t\t\t\t\tpageIndex: 1,\n\t\t\t\t\t\t\t\tlistType: 0,\n\t\t\t\t\t\t\t\tcontrols: [],\n\t\t\t\t\t\t\t\tfilters: []\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tif (recommendResponse.data && recommendResponse.data.data && recommendResponse.data.data.rows) {\n\t\t\t\t\t\t\t// 随机选择一些产品作为推荐\n\t\t\t\t\t\t\tconst allItems = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));\n\t\t\t\t\t\t\tconst shuffled = allItems.sort(() => 0.5 - Math.random());\n\t\t\t\t\t\t\tthis.recommendations = shuffled.slice(0, 4); // 取前4个\n\t\t\t\t\t\t\tthis.recommendations.sort((a, b) => b.likeCount - a.likeCount);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 没有rowid时，案例数据为空\n\t\t\t\t\t\tthis.caseStudies = [];\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 加载推荐数据和案例数据失败:', error);\n\t\t\t\t\tthis.recommendations = [];\n\t\t\t\t\tthis.caseStudies = [];\n\t\t\t\t}\n\t\t\t},\n\n\t\t\thandleLike(item) {\n\t\t\t\t// 1. Toggle the UI state immediately (Optimistic Update)\n\t\t\t\titem.isLiked = !item.isLiked;\n\t\t\t\t\n\t\t\t\tlet webhookAction = '';\n\t\t\t\n\t\t\t\tif (item.isLiked) {\n\t\t\t\t\t// --- UI & Local State Update for LIKE ---\n\t\t\t\t\titem.likeCount++;\n\t\t\t\t\twebhookAction = 'increment';\n\t\t\t\t\tthis.$set(this.likesLog, item.rowId, true);\n\t\t\t\n\t\t\t\t\t// Trigger animation\n\t\t\t\t\tthis.$set(item, 'showHeart', true);\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.$set(item, 'showHeart', false);\n\t\t\t\t\t}, 600);\n\t\t\t\t} else {\n\t\t\t\t\t// --- UI & Local State Update for UNLIKE ---\n\t\t\t\t\titem.likeCount--;\n\t\t\t\t\twebhookAction = 'decrement';\n\t\t\t\t\tthis.$delete(this.likesLog, item.rowId);\n\t\t\t\t}\n\t\t\t\n\t\t\t\t// Update local storage for persistence across sessions\n\t\t\t\tuni.setStorageSync('likes_log', this.likesLog);\n\t\t\t\n\t\t\t\t// 2. Send the corresponding command to the webhook\n\t\t\t\tuni.request({\n\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/workflow/hooks/Njg2Y2ZhNDk3MDMwNzAyNDViNDMxOTcx',\n\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\theader: {\n\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t},\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: webhookAction,\n\t\t\t\t\t\tid: item.rowId\n\t\t\t\t\t},\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconsole.log(`Webhook '${webhookAction}' for rowid ${item.rowId} sent successfully. Response:`, res.data);\n\t\t\t\t\t},\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error(`Webhook '${webhookAction}' for rowid ${item.rowId} failed:`, err);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\thandleContact(pm) {\n\t\t\t\t// console.log(pm);\n\t\t\t\tlet serviceLink = 'https://work.weixin.qq.com/kfid/kfcaf7fbb93aa905a54'; // 默认链接\n\t\t\t\tconst managerLink = PRODUCT_MANAGER_SERVICE_LINKS.find(item => item.manager === pm);\n\t\t\t\tif (managerLink) {\n\t\t\t\t\tserviceLink = managerLink.serviceLink;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\twx.openCustomerServiceChat({\n\t\t\t\t\textInfo: {\n\t\t\t\t\t\turl: serviceLink\n\t\t\t\t\t},\n\t\t\t\t\tcorpId: 'wwa76e36d25343b6b9',\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\tformatAPIData(row) {\n\t\t\t\tconst formattedItem = {\n\t\t\t\t\trowId: row.rowid || '',\n\t\t\t\t\tlikeCount: parseInt(row['DZS']) || 0,\n\t\t\t\t\tisLiked: false,\n\t\t\t\t\tshowHeart: false\n\t\t\t\t};\n\t\t\t\tObject.keys(FIELD_MAPPING).forEach(key => {\n\t\t\t\t\tconst apiFieldId = FIELD_MAPPING[key];\n\t\t\t\t\tformattedItem[key] = row[apiFieldId] || '';\n\t\t\t\t});\n\t\t\t\n\t\t\t\t// 添加图片URL处理\n\t\t\t\tif (formattedItem.imageUrl || row[FIELD_MAPPING.imageUrl]) {\n\t\t\t\t\tformattedItem.imageUrl = this.getImageUrl(formattedItem.imageUrl || row[FIELD_MAPPING.imageUrl]);\n\t\t\t\t}\n\n\t\t\t\t// 处理产品图字段\n\t\t\t\tif (formattedItem.chanpingtu || row[FIELD_MAPPING.chanpingtu]) {\n\t\t\t\t\tformattedItem.chanpingtu = this.getImageUrl(formattedItem.chanpingtu || row[FIELD_MAPPING.chanpingtu]);\n\t\t\t\t}\n\n\t\t\t\t// 处理代理品牌图片字段\n\t\t\t\tconst agentBrandImageData = formattedItem.agentBrandImage || row[FIELD_MAPPING.agentBrandImage];\n\t\t\t\tif (agentBrandImageData) {\n\t\t\t\t\tformattedItem.agentBrandImage = this.getImageUrl(agentBrandImageData);\n\t\t\t\t} else {\n\t\t\t\t\tformattedItem.agentBrandImage = '/static/代理图标.png';\n\t\t\t\t}\n\n\t\t\t\t// 处理介绍资料字段\n\t\t\t\tconst introMaterialsData = formattedItem.introductionMaterials || row[FIELD_MAPPING.introductionMaterials];\n\t\t\t\tif (introMaterialsData) {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tformattedItem.introductionMaterials = Array.isArray(introMaterialsData) ?\n\t\t\t\t\t\t\tintroMaterialsData : JSON.parse(introMaterialsData);\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tconsole.error('解析介绍资料失败:', error);\n\t\t\t\t\t\tformattedItem.introductionMaterials = [];\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tformattedItem.introductionMaterials = [];\n\t\t\t\t}\n\n\t\t\t\t// 处理相关链接字段\n\t\t\t\tconst relatedLinksData = formattedItem.relatedLinks || row[FIELD_MAPPING.relatedLinks];\n\t\t\t\tconsole.log('🔗 处理相关链接字段:', {\n\t\t\t\t\tfieldMapping: FIELD_MAPPING.relatedLinks,\n\t\t\t\t\trawData: relatedLinksData,\n\t\t\t\t\tdataType: typeof relatedLinksData\n\t\t\t\t});\n\t\t\t\tif (relatedLinksData && typeof relatedLinksData === 'string') {\n\t\t\t\t\t// 用逗号分隔链接，并过滤空字符串\n\t\t\t\t\tformattedItem.relatedLinks = relatedLinksData.split(',')\n\t\t\t\t\t\t.map(link => link.trim())\n\t\t\t\t\t\t.filter(link => link.length > 0);\n\t\t\t\t\tconsole.log('✅ 相关链接处理完成:', formattedItem.relatedLinks);\n\t\t\t\t} else {\n\t\t\t\t\tformattedItem.relatedLinks = [];\n\t\t\t\t\tconsole.log('❌ 相关链接数据为空或格式不正确');\n\t\t\t\t}\n\n\t\t\t\treturn formattedItem;\n\t\t\t},\n\t\t\tgetImageUrl(imageData) {\n\t\t\t\ttry {\n\t\t\t\t\t// 如果已经是HTTP URL，直接返回（去掉查询参数）\n\t\t\t\t\tif (typeof imageData === 'string' && imageData.startsWith('http')) {\n\t\t\t\t\t\tlet cleanUrl = imageData.includes('?') ? imageData.split('?')[0] : imageData;\n\t\t\t\t\t\treturn cleanUrl;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 如果是空值，返回默认图\n\t\t\t\t\tif (!imageData) {\n\t\t\t\t\t\treturn '/static/熙迈LOGO.png';\n\t\t\t\t\t}\n\n\t\t\t\t\t// 支持直接传入数组或JSON字符串\n\t\t\t\t\tconst parsedData = Array.isArray(imageData) ? imageData : JSON.parse(imageData);\n\n\t\t\t\t\tif (!Array.isArray(parsedData) || parsedData.length === 0) {\n\t\t\t\t\t\tconsole.warn(\"图片数据格式错误或为空\");\n\t\t\t\t\t\treturn '/static/熙迈LOGO.png';\n\t\t\t\t\t}\n\n\t\t\t\t\t// 获取第一个图片对象\n\t\t\t\t\tconst imageItem = parsedData[0];\n\n\t\t\t\t\t// 返回优先级：large_thumbnail_full_path > original_file_full_path > thumbnail_full_path > preview_url > 默认图\n\t\t\t\t\tlet imageUrl = imageItem.large_thumbnail_full_path ||\n\t\t\t\t\t\timageItem.original_file_full_path ||\n\t\t\t\t\t\timageItem.thumbnail_full_path ||\n\t\t\t\t\t\timageItem.preview_url ||\n\t\t\t\t\t\t'/static/熙迈LOGO.png';\n\n\t\t\t\t\t// 去掉查询参数，因为小程序可能不支持某些图片处理参数\n\t\t\t\t\tif (imageUrl && imageUrl.includes('?')) {\n\t\t\t\t\t\timageUrl = imageUrl.split('?')[0];\n\t\t\t\t\t}\n\n\t\t\t\t\treturn imageUrl;\n\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"解析图片URL失败:\", error.message, \"原始数据:\", imageData);\n\t\t\t\t\treturn '/static/熙迈LOGO.png';\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync handleCategoryClick(item) {\n\t\t\t\tlet recommendations = [];\n\t\t\t\ttry {\n\t\t\t\t\tconst subCategoryResponse = await uni.request({\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\n\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\n\t\t\t\t\t\t\tworksheetId: 'fenlei',\n\t\t\t\t\t\t\tpageSize: 50,\n\t\t\t\t\t\t\tpageIndex: 1,\n\t\t\t\t\t\t\trowId: item.rowId,\n\t\t\t\t\t\t\tcontrolId: '67b2dd3aef727a4cd047da37',\n\t\t\t\t\t\t\tgetSystemControl: false\n\t\t\t\t\t\t},\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tconst recommendResponse = await uni.request({\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\n\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\n\t\t\t\t\t\t\tworksheetId: 'fenlei',\n\t\t\t\t\t\t\tpageSize: 50,\n\t\t\t\t\t\t\tpageIndex: 1,\n\t\t\t\t\t\t\trowId: item.rowId,\n\t\t\t\t\t\t\tcontrolId: 'GLCP',\n\t\t\t\t\t\t\tgetSystemControl: false\n\t\t\t\t\t\t},\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\n\t\t\t\t\tif (subCategoryResponse.statusCode !== 200) {\n\t\t\t\t\t\tconsole.error('请求子分类失败:', response);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '请求子分类失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\n\t\t\t\t\tif (!subCategoryResponse.data || !subCategoryResponse.data.data) {\n\t\t\t\t\t\tconsole.error('接口返回数据格式异常:', subCategoryResponse.data);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '数据格式异常',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\n\t\t\t\t\tif (!subCategoryResponse.data.data.rows || subCategoryResponse.data.data.rows.length === 0) {\n\t\t\t\t\t\t// 尝试使用短链接跳转，避免URL过长\n\t\t\t\t\t\tconsole.log('=== Form页面跳转到Form页面 ===');\n\t\t\t\t\t\tconsole.log('跳转的产品信息:', {\n\t\t\t\t\t\t\tproductName: item.productName,\n\t\t\t\t\t\t\tproductCode: item.productCode,\n\t\t\t\t\t\t\tproductManager: item.productManager\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\t// 先尝试生成短链接\n\t\t\t\t\t\tthis.generateShortLinkForProduct(item);\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\n\t\t\t\t\t// 使用简化模式跳转到category页面，避免URL过长\n\t\t\t\t\tconst categoryUrl = `/pages/category/category?title=${encodeURIComponent(item.productName)}&parentImage=${encodeURIComponent(item.imageUrl || this.imageUrl || '/static/熙迈LOGO.png')}&shareMode=1`;\n\n\t\t\t\t\tconsole.log('=== Form页面跳转到Category页面（简化模式）===');\n\t\t\t\t\tconsole.log('跳转URL:', categoryUrl);\n\t\t\t\t\tconsole.log('URL长度:', categoryUrl.length);\n\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: categoryUrl\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取子分类失败:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取子分类失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 为产品生成短链接并跳转\n\t\t\tasync generateShortLinkForProduct(item) {\n\t\t\t\tconsole.log('=== 开始处理产品跳转 ===');\n\t\t\t\tconsole.log('产品信息:', item);\n\n\t\t\t\t// 直接使用简化模式跳转，不使用短链接\n\t\t\t\tlet targetUrl;\n\t\t\t\tif (item.productCode) {\n\t\t\t\t\ttargetUrl = `/pages/form/form?productCode=${encodeURIComponent(item.productCode)}&shareMode=1`;\n\t\t\t\t} else if (item.productName) {\n\t\t\t\t\ttargetUrl = `/pages/form/form?productName=${encodeURIComponent(item.productName)}&shareMode=1`;\n\t\t\t\t} else {\n\t\t\t\t\ttargetUrl = `/pages/form/form?shareMode=1`;\n\t\t\t\t}\n\n\t\t\t\tconsole.log('构建的跳转URL:', targetUrl);\n\t\t\t\tconsole.log('URL长度:', targetUrl.length);\n\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('开始跳转...');\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: targetUrl,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tconsole.log('跳转成功:', res);\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('跳转失败:', err);\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '跳转失败',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('跳转异常:', error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '跳转异常',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tcopyUrl(url) {\n\t\t\t\tuni.setClipboardData({\n\t\t\t\t\tdata: url,\n\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '网址已复制',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\t\t\topenDLPPGW() {\n\t\t\t\tif (this.DLPPGW) {\n\t\t\t\t\t// 检查链接是否以 http 或 https 开头\n\t\t\t\t\tif (!this.DLPPGW.startsWith('http://') && !this.DLPPGW.startsWith('https://')) {\n\t\t\t\t\t\tthis.DLPPGW = 'https://' + this.DLPPGW; // 自动补全协议\n\t\t\t\t\t}\n\t\t\t\t\t// 跳转到代理品牌官网\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/pages/WebView/WebView?url=${encodeURIComponent(this.DLPPGW)}`\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '代理品牌官网链接为空',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tpreviewImage() {\n\t\t\t\tconsole.log('图片被点击了！');\n\t\t\t\tif (this.highResImageUrl) {\n\t\t\t\t\tuni.previewImage({\n\t\t\t\t\t\turls: [this.highResImageUrl] // 使用高清图片URL进行预览\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tconsole.error('图片URL为空');\n\t\t\t\t}\n\t\t\t},\n\t\t\t//拨打电话\n\t\t\tcallnum(num) {\n\t\t\t\tuni.makePhoneCall({\n\t\t\t\t\tphoneNumber: num //仅为示例\n\t\t\t\t});\n\t\t\t},\n\t\t\tpreviewAgentImage() {\n\t\t\t\tif (this.agentBrandImage) {\n\t\t\t\t\tuni.previewImage({\n\t\t\t\t\t\turls: [this.agentBrandImage]\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 显示资料列表\n\t\t\tshowMaterialsList() {\n\t\t\t\tthis.showMaterialsModal = true;\n\t\t\t},\n\t\t\t// 隐藏资料列表\n\t\t\thideMaterialsList() {\n\t\t\t\tthis.showMaterialsModal = false;\n\t\t\t},\n\t\t\t// 预览资料文件\n\t\t\tpreviewMaterial(material) {\n\t\t\t\t// 优先使用 DownloadUrl，如果失败再尝试 original_file_full_path\n\t\t\t\tconst downloadUrl = material.DownloadUrl;\n\t\t\t\tconst originalUrl = material.original_file_full_path;\n\t\t\t\tconst fileName = material.original_file_name;\n\n\t\t\t\tconsole.log('预览文件:', fileName);\n\t\t\t\tconsole.log('DownloadUrl:', downloadUrl);\n\t\t\t\tconsole.log('original_file_full_path:', originalUrl);\n\n\t\t\t\t// 获取文件类型\n\t\t\t\tconst fileType = this.getFileType(fileName);\n\t\t\t\tconsole.log('文件类型:', fileType);\n\n\t\t\t\t// 根据文件类型选择预览方式\n\t\t\t\tif (fileType === 'video') {\n\t\t\t\t\t// 视频文件预览\n\t\t\t\t\tthis.previewVideo(downloadUrl || originalUrl, fileName);\n\t\t\t\t} else if (fileType === 'image') {\n\t\t\t\t\t// 图片文件预览\n\t\t\t\t\tthis.previewImage(downloadUrl || originalUrl, fileName);\n\t\t\t\t} else {\n\t\t\t\t\t// 其他文件类型使用原有的文档预览方式\n\t\t\t\t\tthis.previewWechatWithFallback(downloadUrl, originalUrl, fileName);\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 带回退机制的预览方法\n\t\t\tpreviewWechatWithFallback(primaryUrl, fallbackUrl, fileName) {\n\t\t\t\tconsole.log('尝试预览文档:', fileName);\n\t\t\t\tconsole.log('主要URL:', primaryUrl);\n\t\t\t\tconsole.log('备用URL:', fallbackUrl);\n\n\t\t\t\t// 先尝试主要URL\n\t\t\t\tthis.previewWechatSingle(primaryUrl, (success) => {\n\t\t\t\t\tif (!success && fallbackUrl && fallbackUrl !== primaryUrl) {\n\t\t\t\t\t\tconsole.log('主要URL失败，尝试备用URL');\n\t\t\t\t\t\tthis.previewWechatSingle(fallbackUrl, (success) => {\n\t\t\t\t\t\t\tif (!success) {\n\t\t\t\t\t\t\t\t// 两个URL都失败，显示最终错误\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '预览失败',\n\t\t\t\t\t\t\t\t\tcontent: '文件无法预览，可能是文件格式不支持或网络问题。\\n\\n是否复制文件链接？',\n\t\t\t\t\t\t\t\t\tconfirmText: '复制链接',\n\t\t\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\t\t\tsuccess: function(modalRes) {\n\t\t\t\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\t\t\t\t\t\t\tdata: primaryUrl,\n\t\t\t\t\t\t\t\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '链接已复制',\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 单次预览尝试\n\t\t\tpreviewWechatSingle(urlPdf, callback) {\n\t\t\t\tif (!urlPdf || typeof urlPdf !== 'string') {\n\t\t\t\t\tconsole.error('无效的文档URL:', urlPdf);\n\t\t\t\t\tcallback(false);\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '正在加载中..'\n\t\t\t\t});\n\n\t\t\t\tconsole.log('开始下载文件:', urlPdf);\n\t\t\t\tuni.downloadFile({\n\t\t\t\t\turl: urlPdf,\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tconsole.log('文件下载成功:', res);\n\t\t\t\t\t\tvar filePath = res.tempFilePath;\n\n\t\t\t\t\t\tif (!filePath) {\n\t\t\t\t\t\t\tconsole.error('下载成功但临时文件路径为空');\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tcallback(false);\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconsole.log('准备打开文档:', filePath);\n\t\t\t\t\t\tuni.openDocument({\n\t\t\t\t\t\t\tfilePath: filePath,\n\t\t\t\t\t\t\tshowMenu: false,\n\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\tconsole.log('打开文档成功:', res);\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tcallback(true);\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\t\t\tconsole.error('打开文档失败:', err);\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tcallback(false);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\tconsole.error('下载文件失败:', err);\n\t\t\t\t\t\tconsole.error('失败的URL:', urlPdf);\n\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\tcallback(false);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 微信小程序预览文档\n\t\t\tpreviewWechat(urlPdf) {\n\t\t\t\tconsole.log('准备预览文档，URL:', urlPdf);\n\n\t\t\t\t// 检查URL是否有效\n\t\t\t\tif (!urlPdf || typeof urlPdf !== 'string') {\n\t\t\t\t\tconsole.error('无效的文档URL:', urlPdf);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '文档链接无效',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '正在加载中..'\n\t\t\t\t});\n\n\t\t\t\tconsole.log('开始下载文件:', urlPdf);\n\t\t\t\tuni.downloadFile({\n\t\t\t\t\turl: urlPdf,\n\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\tconsole.log('文件下载成功:', res);\n\t\t\t\t\t\tvar filePath = res.tempFilePath;\n\n\t\t\t\t\t\tif (!filePath) {\n\t\t\t\t\t\t\tconsole.error('下载成功但临时文件路径为空');\n\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '文件路径错误',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconsole.log('准备打开文档:', filePath);\n\t\t\t\t\t\tuni.openDocument({\n\t\t\t\t\t\t\tfilePath: filePath,\n\t\t\t\t\t\t\tshowMenu: false, // 禁用菜单，防止下载\n\t\t\t\t\t\t\tsuccess: function(res) {\n\t\t\t\t\t\t\t\tconsole.log('打开文档成功:', res);\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\t\t\tconsole.error('打开文档失败:', err);\n\t\t\t\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\ttitle: '文档格式不支持或文件损坏',\n\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\tconsole.error('下载文件失败:', err);\n\t\t\t\t\t\tconsole.error('失败的URL:', urlPdf);\n\t\t\t\t\t\tuni.hideLoading();\n\n\t\t\t\t\t\t// 根据错误类型给出不同提示\n\t\t\t\t\t\tlet errorMsg = '文件加载失败';\n\t\t\t\t\t\tif (err.errMsg && err.errMsg.includes('ENOENT')) {\n\t\t\t\t\t\t\terrorMsg = '文件不存在或已被删除';\n\t\t\t\t\t\t} else if (err.errMsg && err.errMsg.includes('network')) {\n\t\t\t\t\t\t\terrorMsg = '网络连接失败，请检查网络';\n\t\t\t\t\t\t} else if (err.errMsg && err.errMsg.includes('timeout')) {\n\t\t\t\t\t\t\terrorMsg = '下载超时，请重试';\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '预览失败',\n\t\t\t\t\t\t\tcontent: errorMsg + '\\n\\n是否尝试在浏览器中打开？',\n\t\t\t\t\t\t\tconfirmText: '打开',\n\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\tsuccess: function(modalRes) {\n\t\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\t\t// 尝试用浏览器打开\n\t\t\t\t\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\t\t\t\t\tdata: urlPdf,\n\t\t\t\t\t\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '链接已复制到剪贴板',\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 获取文件类型\n\t\t\tgetFileType(fileName) {\n\t\t\t\tconst ext = fileName.split('.').pop().toLowerCase();\n\n\t\t\t\t// 视频类型\n\t\t\t\tconst videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'ogg'];\n\t\t\t\tif (videoTypes.includes(ext)) return 'video';\n\n\t\t\t\t// 图片类型\n\t\t\t\tconst imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\n\t\t\t\tif (imageTypes.includes(ext)) return 'image';\n\n\t\t\t\t// PDF类型\n\t\t\t\tif (ext === 'pdf') return 'pdf';\n\n\t\t\t\t// 文档类型\n\t\t\t\tconst docTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];\n\t\t\t\tif (docTypes.includes(ext)) return 'document';\n\n\t\t\t\treturn 'unknown';\n\t\t\t},\n\t\t\t// 获取文件图标\n\t\t\tgetMaterialIcon(fileName) {\n\t\t\t\tconst ext = fileName.split('.').pop().toLowerCase();\n\t\t\t\tswitch (ext) {\n\t\t\t\t\tcase 'pdf':\n\t\t\t\t\t\treturn 'paperplane';\n\t\t\t\t\tcase 'doc':\n\t\t\t\t\tcase 'docx':\n\t\t\t\t\t\treturn 'compose';\n\t\t\t\t\tcase 'xls':\n\t\t\t\t\tcase 'xlsx':\n\t\t\t\t\t\treturn 'bars';\n\t\t\t\t\tcase 'ppt':\n\t\t\t\t\tcase 'pptx':\n\t\t\t\t\t\treturn 'videocam';\n\t\t\t\t\tcase 'mp4':\n\t\t\t\t\tcase 'avi':\n\t\t\t\t\tcase 'mov':\n\t\t\t\t\tcase 'wmv':\n\t\t\t\t\tcase 'flv':\n\t\t\t\t\tcase 'webm':\n\t\t\t\t\tcase 'mkv':\n\t\t\t\t\t\treturn 'videocam-filled';\n\t\t\t\t\tcase 'jpg':\n\t\t\t\t\tcase 'jpeg':\n\t\t\t\t\tcase 'png':\n\t\t\t\t\tcase 'gif':\n\t\t\t\t\tcase 'bmp':\n\t\t\t\t\tcase 'webp':\n\t\t\t\t\t\treturn 'image';\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn 'folder';\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 预览视频文件\n\t\t\tpreviewVideo(videoUrl, fileName) {\n\t\t\t\tconsole.log('预览视频:', fileName);\n\t\t\t\tconsole.log('视频URL:', videoUrl);\n\n\t\t\t\t// 在微信小程序中，我们可以使用 video 组件来播放视频\n\t\t\t\t// 这里我们创建一个简单的视频预览弹窗\n\t\t\t\tthis.showVideoModal = true;\n\t\t\t\tthis.currentVideoUrl = videoUrl;\n\t\t\t\tthis.currentVideoName = fileName;\n\t\t\t},\n\t\t\t// 预览图片文件\n\t\t\tpreviewImage(imageUrl, fileName) {\n\t\t\t\tconsole.log('预览图片:', fileName);\n\t\t\t\tconsole.log('图片URL:', imageUrl);\n\n\t\t\t\t// 使用微信小程序的图片预览功能\n\t\t\t\tuni.previewImage({\n\t\t\t\t\turls: [imageUrl],\n\t\t\t\t\tcurrent: imageUrl,\n\t\t\t\t\tsuccess: function() {\n\t\t\t\t\t\tconsole.log('图片预览成功');\n\t\t\t\t\t},\n\t\t\t\t\tfail: function(err) {\n\t\t\t\t\t\tconsole.error('图片预览失败:', err);\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '图片预览失败',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 关闭视频预览\n\t\t\tcloseVideoModal() {\n\t\t\t\tthis.showVideoModal = false;\n\t\t\t\tthis.currentVideoUrl = '';\n\t\t\t\tthis.currentVideoName = '';\n\t\t\t},\n\t\t\t// 获取链接显示文本\n\t\t\tgetLinkDisplayText(link) {\n\t\t\t\t// 如果是微信公众号文章链接，显示\"公众号文章\"\n\t\t\t\tif (link.includes('mp.weixin.qq.com')) {\n\t\t\t\t\treturn '公众号文章';\n\t\t\t\t}\n\t\t\t\t// 如果是视频号链接，显示\"视频号\"\n\t\t\t\tif (link.includes('channels.weixin.qq.com') || link.includes('video.weixin.qq.com')) {\n\t\t\t\t\treturn '视频号';\n\t\t\t\t}\n\t\t\t\t// 其他链接显示域名\n\t\t\t\ttry {\n\t\t\t\t\tconst url = new URL(link);\n\t\t\t\t\treturn url.hostname;\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn '相关链接';\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 打开相关链接\n\t\t\topenRelatedLink(link) {\n\t\t\t\tconsole.log('打开相关链接:', link);\n\n\t\t\t\t// 获取链接显示名称作为页面标题\n\t\t\t\tconst linkTitle = this.getLinkDisplayText(link);\n\n\t\t\t\t// 跳转到 webview 页面\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: `/pages/WebView/WebView?url=${encodeURIComponent(link)}&title=${encodeURIComponent(linkTitle)}`,\n\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\tconsole.error('跳转到 webview 页面失败:', err);\n\t\t\t\t\t\t// 如果跳转失败，回退到复制链接的方式\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '打开链接',\n\t\t\t\t\t\t\tcontent: '无法直接打开链接，是否复制到剪贴板？',\n\t\t\t\t\t\t\tconfirmText: '复制链接',\n\t\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\t\tuni.setClipboardData({\n\t\t\t\t\t\t\t\t\t\tdata: link,\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '链接已复制',\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t\tfail: () => {\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '复制失败',\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\t\t\t// 格式化文件大小\n\t\t\tformatFileSize(bytes) {\n\t\t\t\tif (bytes === 0) return '0 B';\n\t\t\t\tconst k = 1024;\n\t\t\t\tconst sizes = ['B', 'KB', 'MB', 'GB'];\n\t\t\t\tconst i = Math.floor(Math.log(bytes) / Math.log(k));\n\t\t\t\treturn parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tbackground-color: #f7f8fa;\n\t\tpadding-bottom: 200rpx;\n\t\t/* 增加底部内边距 */\n\t}\n\n\t.info-card {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\tmargin: 24rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n\t\toverflow: hidden;\n\t}\n\n\t.card-header {\n\t\tpadding: 24rpx;\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t}\n\n\t.card-body {\n\t\tpadding: 24rpx;\n\t}\n\n\t.form-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #555;\n\t}\n\n\t.form-item:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.text {\n\t\tword-break: break-all;\n\t}\n\t\n\t.full-url {\n\t\tdisplay: block;\n\t\tbackground-color: #f5f5f5;\n\t\tpadding: 10rpx 15rpx;\n\t\tborder-radius: 8rpx;\n\t\tfont-size: 24rpx;\n\t\tcolor: #007aff;\n\t\tword-break: break-all;\n\t\tmargin-top: -10rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.phone-link {\n\t\tcolor: #007aff;\n\t\ttext-decoration: underline;\n\t}\n\n\t.link-item {\n\t\tjustify-content: space-between;\n\t\tpadding: 10rpx 0;\n\t}\n\n\t.link-text {\n\t\tcolor: #007aff;\n\t\tfont-weight: 500;\n\t}\n\n\t.image,\n\t.brand-logo {\n\t\twidth: 100%;\n\t\theight: 400rpx;\n\t\tborder-radius: 12rpx;\n\t\tmargin-top: 20rpx;\n\t}\n\n\t.brand-image {\n\t\tflex-direction: column;\n\t\talign-items: flex-start;\n\t}\n\n\t.brand-logo {\n\t\theight: 150rpx;\n\t\twidth: 200rpx;\n\t\tmax-width: 100%;\n\t\talign-self: flex-start;\n\t\tbackground-color: #f5f5f5;\n\t\tborder: 1px solid #eee;\n\t}\n\n\t.debug-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tmargin: 5rpx 0;\n\t}\n\n\t.agent-section .card-header {\n\t\tbackground-color: #eef5ff;\n\t\tcolor: #0052cc;\n\t}\n\t\n\t.case-studies-section .card-header {\n\t\tbackground-color: #e6f7f2;\n\t\tcolor: #006442;\n\t}\n\t\n\t.case-study-item {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: flex-start;\n\t\tpadding: 16rpx 0;\n\t\tborder-bottom: 1rpx solid #f0f0f0;\n\t}\n\t\n\t.case-study-item:last-child {\n\t\tborder-bottom: none;\n\t\tpadding-bottom: 0;\n\t}\n\t\n\t.case-study-client {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.case-study-details {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.category-list {\n\t\tdisplay: flex;\n\t\tflex-wrap: wrap;\n\t\tjustify-content: space-between;\n\t\tpadding: 0; /* 在父级.recommend-section中控制 */\n\t}\n\t\n\t.category-item {\n\t\twidth: calc(50% - 12rpx);\n\t\tmargin-bottom: 24rpx;\n\t\tbackground-color: #fff;\n\t\tborder-radius: 16rpx;\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\n\t\toverflow: hidden;\n\t\tposition: relative;\n\t}\n\t\n\t.img {\n\t\twidth: 100%;\n\t\theight: 250rpx;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\toverflow: hidden;\n\t}\n\t\n\t.img .icon {\n\t\theight: 100%;\n\t\twidth: 100%;\n\t\tdisplay: block;\n\t\tobject-fit: contain;\n\t}\n\t\n\t.banner {\n\t\tpadding: 10rpx;\n\t}\n\t\n\t.banner-left {\n\t\twidth: 100%;\n\t}\n\t\n\t.top-blue {\n\t\theight: 60rpx;\n\t\twidth: 100%;\n\t\tborder-radius: 15rpx;\n\t\tbackground-color: #6d92cc;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t}\n\t\n\t.english-name {\n\t\tcolor: #ffffff;\n\t\tfont-size: 16rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t.more-text {\n\t\tmargin-left: 12rpx;\n\t}\n\t\n\t.bottom-white {\n\t\tcolor: #6d92cc;\n\t\tmin-height: 80rpx;\n\t\twidth: 100%;\n\t\tbackground-color: #fff;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 10rpx;\n\t\tbox-sizing: border-box;\n\t}\n\t\n\t.bottom-white-text {\n\t\tflex: 1;\n\t\tmin-width: 0;\n\t\tfont-size: 22rpx;\n\t\ttext-align: center;\n\t\twhite-space: normal;\n\t\tline-height: 1.4;\n\t\tmargin: 0 8rpx;\n\t}\n\t\n\t.logo {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tflex-shrink: 0;\n\t\twidth: 70rpx;\n\t\theight: 70rpx;\n\t}\n\t\n\t.logo .icon {\n\t\tmax-width: 100%;\n\t\tmax-height: 100%;\n\t\tobject-fit: contain;\n\t\tpadding: 0;\n\t}\n\t\n\t.like-section {\n\t\tposition: relative;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tpadding: 0 10rpx;\n\t\tflex-shrink: 0;\n\t}\n\t\n\t.like-icon {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t}\n\n\t.pop-animation {\n\t\tposition: absolute;\n\t\tbottom: 100%; /* 从按钮上方开始 */\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tflex-direction: column;\n\t\tanimation: pop-up 0.8s ease-out forwards;\n\t\tpointer-events: none;\n\t}\n\t\n\t.pop-heart {\n\t\tfont-size: 30rpx;\n\t}\n\t\n\t.pop-count {\n\t\tfont-size: 24rpx;\n\t\tcolor: #ff6a6a;\n\t\tfont-weight: bold;\n\t}\n\n\t@keyframes pop-up {\n\t\t0% {\n\t\t\ttransform: translateX(-50%) scale(0.5);\n\t\t\topacity: 0;\n\t\t\tbottom: 100%;\n\t\t}\n\t\t50% {\n\t\t\ttransform: translateX(-50%) scale(1.2);\n\t\t\topacity: 1;\n\t\t}\n\t\t100% {\n\t\t\ttransform: translateX(-50%) scale(1);\n\t\t\topacity: 0;\n\t\t\tbottom: 150%; /* 向上飘动 */\n\t\t}\n\t}\n\n\t.recommend-section {\n\t\tpadding: 24rpx;\n\t\tbackground-color: #f7f8fa;\n\t}\n\n\t.section-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 24rpx;\n\t\ttext-align: center;\n\t\tcolor: #333;\n\t}\n\n\t.fixed-customer-service {\n\t\tposition: fixed;\n\t\tbottom: 40rpx;\n\t\tleft: 50%;\n\t\ttransform: translateX(-50%);\n\t\twidth: 90%;\n\t\tz-index: 10;\n\t}\n\n\t.service-btn {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 10rpx;\n\t\tbackground: linear-gradient(135deg, #2979FF, #00B4FF);\n\t\tborder-radius: 24rpx;\n\t\tbox-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);\n\t\ttransition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\n\t\tbackdrop-filter: blur(10px);\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.1);\n\t}\n\n\t.text-container {\n\t\tflex: 1;\n\t}\n\n\t.btn-text {\n\t\tcolor: #fff;\n\t\tfont-size: 38rpx;\n\t\tfont-weight: 600;\n\t\ttext-align: center;\n\t\tdisplay: block;\n\t\tmargin-bottom: 0;\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.btn-subtext {\n\t\tcolor: rgba(255, 255, 255, 0.9);\n\t\tfont-size: 24rpx;\n\t\tline-height: 1.6;\n\t\ttext-align: center;\n\t\tdisplay: block;\n\t\topacity: 0.9;\n\t\tmargin-bottom: 16rpx;\n\t}\n\n\t.service-btn:active {\n\t\ttransform: scale(0.98) translateY(2rpx);\n\t\tbox-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);\n\t}\n\n\t@keyframes float {\n\t\t0% {\n\t\t\ttransform: translateY(0);\n\t\t}\n\n\t\t50% {\n\t\t\ttransform: translateY(-10rpx);\n\t\t}\n\n\t\t100% {\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t.service-btn {\n\t\tanimation: float 3s ease-in-out infinite;\n\t}\n\n\t.service-btn::after {\n\t\tcontent: '';\n\t\tposition: absolute;\n\t\ttop: -1rpx;\n\t\tleft: -1rpx;\n\t\tright: -1rpx;\n\t\tbottom: -1rpx;\n\t\tbackground: linear-gradient(45deg,\n\t\t\t\trgba(255, 255, 255, 0) 0%,\n\t\t\t\trgba(255, 255, 255, 0.3) 50%,\n\t\t\t\trgba(255, 255, 255, 0) 100%);\n\t\tborder-radius: 24rpx;\n\t\tanimation: shine 3s infinite;\n\t\tz-index: -1;\n\t}\n\n\t@keyframes shine {\n\t\t0% {\n\t\t\topacity: 0;\n\t\t\tleft: -50%;\n\t\t}\n\n\t\t50% {\n\t\t\topacity: 0.4;\n\t\t}\n\n\t\t100% {\n\t\t\topacity: 0;\n\t\t\tleft: 150%;\n\t\t}\n\t}\n\n\t/* 介绍资料样式 */\n\t.materials-section {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.materials-button {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 20rpx 0;\n\t\tborder-radius: 12rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tpadding: 20rpx;\n\t}\n\n\t.materials-text {\n\t\tflex: 1;\n\t\tmargin-left: 12rpx;\n\t\tcolor: #2979FF;\n\t\tfont-weight: 500;\n\t}\n\n\t/* 相关链接样式 */\n\t.links-section {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.links-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 16rpx;\n\t}\n\n\t.links-title {\n\t\tmargin-left: 12rpx;\n\t\tfont-size: 30rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t}\n\n\t.links-list {\n\t\tbackground-color: #f8f9fa;\n\t\tborder-radius: 12rpx;\n\t\toverflow: hidden;\n\t}\n\n\t.link-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 24rpx 20rpx;\n\t\tborder-bottom: 1px solid #eee;\n\t\tbackground-color: #fff;\n\t\tmargin-bottom: 2rpx;\n\t\tborder-radius: 8rpx;\n\t}\n\n\t.link-item:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.link-item:active {\n\t\tbackground-color: #f5f5f5;\n\t}\n\n\t.link-text {\n\t\tflex: 1;\n\t\tmargin-left: 12rpx;\n\t\tcolor: #333;\n\t\tfont-size: 28rpx;\n\t}\n\n\t/* 弹窗样式 */\n\t.materials-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 1000;\n\t}\n\n\t.materials-modal-content {\n\t\twidth: 90%;\n\t\tmax-height: 70%;\n\t\tbackground-color: white;\n\t\tborder-radius: 16rpx;\n\t\toverflow: hidden;\n\t}\n\n\t.materials-modal-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 30rpx;\n\t\tborder-bottom: 1px solid #eee;\n\t}\n\n\t.materials-modal-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: 600;\n\t\tcolor: #333;\n\t}\n\n\t.materials-modal-close {\n\t\tpadding: 10rpx;\n\t}\n\n\t.materials-modal-body {\n\t\tmax-height: 60vh;\n\t\toverflow-y: auto;\n\t\tpadding: 20rpx;\n\t}\n\n\t.material-item {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tpadding: 20rpx;\n\t\tborder-radius: 12rpx;\n\t\tbackground-color: #f8f9fa;\n\t\tmargin-bottom: 16rpx;\n\t}\n\n\t.material-item:last-child {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.material-icon {\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.material-info {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t.material-name {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tmargin-bottom: 8rpx;\n\t\tword-break: break-all;\n\t}\n\n\t.material-size {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\n\t.material-action {\n\t\tmargin-left: 20rpx;\n\t}\n\n\t/* 视频预览弹窗样式 */\n\t.video-modal {\n\t\tposition: fixed;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\twidth: 100%;\n\t\theight: 100%;\n\t\tbackground-color: rgba(0, 0, 0, 0.9);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tz-index: 2000;\n\t}\n\n\t.video-modal-content {\n\t\twidth: 95%;\n\t\tmax-width: 800rpx;\n\t\tbackground-color: #000;\n\t\tborder-radius: 16rpx;\n\t\toverflow: hidden;\n\t}\n\n\t.video-modal-header {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tpadding: 20rpx 30rpx;\n\t\tbackground-color: rgba(0, 0, 0, 0.8);\n\t}\n\n\t.video-modal-title {\n\t\tfont-size: 28rpx;\n\t\tcolor: #fff;\n\t\tflex: 1;\n\t\tmargin-right: 20rpx;\n\t\tword-break: break-all;\n\t}\n\n\t.video-modal-close {\n\t\tpadding: 10rpx;\n\t}\n\n\t.video-modal-body {\n\t\tposition: relative;\n\t}\n\n\t.video-player {\n\t\twidth: 100%;\n\t\theight: 400rpx;\n\t\tbackground-color: #000;\n\t}\n</style>", "import MiniProgramPage from 'D:/小程序项目/熙迈门户/XMMH/pages/form/form.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "response", "FIELD_MAPPING", "PRODUCT_MANAGER_SERVICE_LINKS", "res"], "mappings": ";;;;;;EA2NE,OAAA;AACC,WAAA;AAAA,MACC,cAAA;AAAA,MACA,aAAA;AAAA,MACA,aAAA;AAAA,MACA,gBAAA;AAAA,MACA,aAAA;AAAA,MACA,oBAAA;AAAA;MAEA,iBAAA;AAAA,MACA,MAAA;AAAA,MACA,iBAAA;AAAA,MACA,QAAA;AAAA;MAEA,aAAA;AAAA;MAEA,iBAAA,CAAA;AAAA,MACA,UAAA,CAAA;AAAA;AAAA;;MAEA,uBAAA,CAAA;AAAA;AAAA;;MAEA,oBAAA;AAAA;AAAA,MACA,OAAA;AAAA;AAAA,MACA,gBAAA;AAAA;AAAA,MACA,iBAAA;AAAA;AAAA,MACA,kBAAA;AAAA;AAAA,IACD;AAAA;EAED,MAAA,OAAA,SAAA;;;;;;;;QAYG,OAAA,KAAA;AAAA,MACD,CAAA;AAIA,UAAA,CAAA,KAAA,eAAA,CAAA,KAAA,aAAA;;AAECA,sBAAAA,MAAA,UAAA;AAAA;;UAGC,YAAA;AAAA,UACA,aAAA;AAAA;AAECA,0BAAAA,MAAA,UAAA;AAAA,cACC,KAAA;AAAA,YACD,CAAA;AAAA,UACD;AAAA,QACD,CAAA;AACA;AAAA,MACD;;;AAIAA,oBAAAA,MAAA,MAAA,OAAA,8BAAA,uBAAA;AAEA,WAAA,qBAAA,mBAAA,QAAA,sBAAA,EAAA;;;AAGA,WAAA,iBAAA,mBAAA,QAAA,kBAAA,EAAA;;;AAGA,WAAA,eAAA,mBAAA,QAAA,gBAAA,EAAA;AACA,WAAA,kBAAA,mBAAA,QAAA,mBAAA,EAAA;;;;;AAKA,UAAA,KAAA,aAAA,WAAA,KAAA,GAAA;;MAEA;AAEA,UAAA;AACC,aAAA,kBAAA,KAAA,MAAA,mBAAA,QAAA,mBAAA,IAAA,CAAA;;;AAGAA,sBAAAA,MAAA,MAAA,SAAA,8BAAA,aAAA,KAAA;AAAA,MACD;AAEA,UAAA;AACC,aAAA,cAAA,KAAA,MAAA,mBAAA,QAAA,eAAA,IAAA,CAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,8BAAA,eAAA,KAAA;AAAA,MACD;AAEA,UAAA;;;AAGCA,sBAAA,MAAA,MAAA,SAAA,8BAAA,eAAA,KAAA;AAAA,MACD;AAEA,UAAA;AACC,cAAA,kBAAA,mBAAA,QAAA,gBAAA,EAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,8BAAA,kBAAA,eAAA;AACA,YAAA,iBAAA;AAEC,eAAA,eAAA,gBAAA,MAAA,GAAA;AAGAA,wBAAA,MAAA,MAAA,OAAA,8BAAA,mBAAA,KAAA,YAAA;AAAA;AAEA,eAAA,eAAA;;QAED;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,8BAAA,eAAA,KAAA;AACA,aAAA,eAAA;MACD;AAAA,IACD;AAEA,QAAA;;AAEC,YAAA,iBAAA,mBAAA,QAAA,cAAA,EAAA;AAEA,UAAA,UAAA,WAAA,MAAA,GAAA;;;AAIC,aAAA,WAAA,KAAA,YAAA,SAAA;AAAA,MACD;AAGA,UAAA,gBAAA;AACC,YAAA,eAAA,WAAA,MAAA,GAAA;AACC,eAAA,kBAAA;AAAA;;QAGD;AAAA;AAEA,aAAA,kBAAA,KAAA;AAAA,MACD;AAAA;AAEAA,oBAAA,MAAA,MAAA,SAAA,8BAAA,cAAA,KAAA;AACA,WAAA,WAAA;AACA,WAAA,kBAAA,KAAA;AAAA,IACD;AACA,QAAA;;;;;AAQE,cAAA,YAAA,KAAA,MAAA,YAAA,IAAA;AACA,YAAA,UAAA,SAAA,KAAA,UAAA,CAAA,EAAA,SAAA;2EAEE,UAAA,CAAA,EAAA,UACA,WAAA,UAAA,CAAA,EAAA,OAAA;AAAA;AAED,eAAA,kBAAA;AAAA,QACD;AAAA,MACD;AAAA;AAEAA,oBAAA,MAAA,MAAA,SAAA,8BAAA,eAAA,KAAA;AACA,WAAA,kBAAA;AAAA,IACD;AAGA,QAAA,KAAA,gBAAA,WAAA,GAAA;AACC,YAAA,KAAA;IACD;AAAA;;;AAOAA,kBAAAA,MAAA,MAAA,OAAA,8BAAA,qBAAA;;;;;MAKC,gBAAA,KAAA;AAAA;;IAGD,CAAA;AAEA,UAAA,aAAA,KAAA,cAAA,GAAA,KAAA,WAAA,YAAA;;AAIA,QAAA,KAAA,aAAA;AAEC,kBAAA,+BAAA,mBAAA,KAAA,WAAA,CAAA;AAAA;AAGA,kBAAA,+BAAA,mBAAA,KAAA,WAAA,CAAA;AAAA;AAGA,kBAAA;AAAA,IACD;;;;AAOE,oBAAA,+BAAA,mBAAA,KAAA,WAAA,CAAA;AAAA;AAGA,oBAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,8BAAA,yBAAA;AAAA,MACD;AAAA,IACD;AAEAA,kBAAA,MAAA,MAAA,OAAA,8BAAA,YAAA,SAAA;AACAA,kBAAA,MAAA,MAAA,OAAA,8BAAA,WAAA,UAAA,MAAA;;MAGC,OAAA;AAAA,MACA,MAAA;AAAA,MACA,UAAA,KAAA,mBAAA,KAAA,YAAA;AAAA,IACD,CAAA;AAEAA,kBAAA,MAAA,MAAA,OAAA,8BAAA,WAAA,WAAA;AACAA,kBAAAA,MAAA,MAAA,OAAA,8BAAA,sBAAA;AAEA,WAAA;AAAA;EAGD,gBAAA,KAAA;AACCA,kBAAAA,MAAA,MAAA,OAAA,8BAAA,sBAAA;;AAGA,UAAA,aAAA,KAAA,cAAA,GAAA,KAAA,WAAA,YAAA;;AAIA,QAAA,KAAA,aAAA;AAEC,kBAAA,+BAAA,mBAAA,KAAA,WAAA,CAAA;AAAA;AAGA,kBAAA,+BAAA,mBAAA,KAAA,WAAA,CAAA;AAAA;AAGA,kBAAA;AAAA,IACD;;;;AAOE,oBAAA,+BAAA,mBAAA,KAAA,WAAA,CAAA;AAAA;AAGA,oBAAA;AACAA,sBAAAA,MAAA,MAAA,OAAA,8BAAA,yBAAA;AAAA,MACD;AAAA,IACD;AAEAA,kBAAA,MAAA,MAAA,OAAA,8BAAA,YAAA,SAAA;;MAGC,OAAA;AAAA,MACA,MAAA;AAAA,MACA,UAAA,KAAA,mBAAA,KAAA,YAAA;AAAA,IACD,CAAA;AAEAA,kBAAA,MAAA,MAAA,OAAA,8BAAA,WAAA,WAAA;AACAA,kBAAAA,MAAA,MAAA,OAAA,8BAAA,yBAAA;AAEA,WAAA;AAAA;EAGD,SAAA;AAAA;AAAA;AAKE,UAAA;AAEC,cAAA,UAAA,CAAA;AAGA,YAAA,KAAA,aAAA;;;;;;;;YAOE,OAAA,KAAA;AAAA,UACD,CAAA;AAAA;;;;;;;;YASC,OAAA,KAAA;AAAA,UACD,CAAA;AAAA,QACD;;;AAMCA,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AACA;AAAA,QACD;AAEA,cAAA,cAAA;AAAA;;UAGC,aAAA;AAAA;;;;UAKA;AAAA;AAKD,cAAAC,YAAA,MAAAD,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;UAEA,MAAA;AAAA,UACA,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;AAIA,YAAAC,UAAA,QAAAA,UAAA,KAAA,MAAA;AAAA,QAEA;;AAGC,gBAAA,cAAAA,UAAA,KAAA,KAAA,KAAA,CAAA;;;AAKA,eAAA,cAAA,cAAA,eAAA,KAAA;;;;AAIA,eAAA,OAAA,cAAA,QAAA;;AAGA,eAAA,kBAAA,cAAA,mBAAA;AACA,eAAA,SAAA,cAAA,UAAA;AACA,eAAA,aAAA,cAAA,cAAA;;AAEA,eAAA,WAAA,cAAA,YAAA;AACA,eAAA,WAAA,cAAA,YAAA;;AAGA,eAAA,wBAAA,cAAA,yBAAA,CAAA;;AAEAD,wBAAA,MAAA,MAAA,OAAA,8BAAA,qBAAA,KAAA,YAAA;AACA,eAAA,QAAA,cAAA,SAAA;AAEA,cAAA,KAAA,aAAA,WAAA,KAAA,GAAA;;UAEA;AAIA,gBAAA,KAAA;;;AAGAA,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AAAA,QACD;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,8BAAA,aAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AAAA,MACD;AAAA;;IAID,MAAA,8BAAA;AACC,UAAA;AAEC,YAAA,KAAA,OAAA;AACCA,wBAAA,MAAA,MAAA,OAAA,8BAAA,kBAAA,KAAA,KAAA;AAGA,gBAAA,oBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,YACC,KAAA;AAAA;;;;cAKC,aAAA;AAAA,cACA,OAAA,KAAA;AAAA;;;;;YAKD,QAAA;AAAA,cACC,gBAAA;AAAA,YACD;AAAA,UACD,CAAA;AAEA,cAAA,kBAAA,QAAA,kBAAA,KAAA,QAAA,kBAAA,KAAA,KAAA,MAAA;AACC,iBAAA,kBAAA,kBAAA,KAAA,KAAA,KAAA,IAAA,SAAA,KAAA,cAAA,GAAA,CAAA;;UAED;AAGA,gBAAA,sBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,YACC,KAAA;AAAA;;;;cAKC,aAAA;AAAA,cACA,OAAA,KAAA;AAAA;;;;;YAKD,QAAA;AAAA,cACC,gBAAA;AAAA,YACD;AAAA,UACD,CAAA;;AAGC,iBAAA,cAAA,oBAAA,KAAA,KAAA,KAAA,IAAA,UAAA;AAAA;cAEC,SAAA,IAAAE,4BAAA,WAAA,KAAA;AAAA,YACD,EAAA;AACAF,gCAAA,MAAA,OAAA,8BAAA,iBAAA,KAAA,YAAA,MAAA;AAAA;AAEA,iBAAA,cAAA;UACD;AAAA;AAEAA,wBAAAA,MAAA,MAAA,OAAA,8BAAA,kBAAA;AAGA,gBAAA,oBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,YACC,KAAA;AAAA;;;;cAKC,aAAA;AAAA;;;;cAKA,SAAA,CAAA;AAAA;YAED,QAAA;AAAA,cACC,gBAAA;AAAA,YACD;AAAA,UACD,CAAA;AAEA,cAAA,kBAAA,QAAA,kBAAA,KAAA,QAAA,kBAAA,KAAA,KAAA,MAAA;AAEC,kBAAA,WAAA,kBAAA,KAAA,KAAA,KAAA,IAAA,SAAA,KAAA,cAAA,GAAA,CAAA;;;;UAID;AAGA,eAAA,cAAA;QACD;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,8BAAA,oBAAA,KAAA;;AAEA,aAAA,cAAA;MACD;AAAA;IAGD,WAAA,MAAA;;AAIC,UAAA,gBAAA;AAEA,UAAA,KAAA,SAAA;AAEC,aAAA;;AAEA,aAAA,KAAA,KAAA,UAAA,KAAA,OAAA,IAAA;AAGA,aAAA,KAAA,MAAA,aAAA,IAAA;AACA,mBAAA,MAAA;AACC,eAAA,KAAA,MAAA,aAAA,KAAA;AAAA,QACD,GAAA,GAAA;AAAA;AAGA,aAAA;;AAEA,aAAA,QAAA,KAAA,UAAA,KAAA,KAAA;AAAA,MACD;AAGAA,oBAAAA,MAAA,eAAA,aAAA,KAAA,QAAA;;QAIC,KAAA;AAAA;QAEA,QAAA;AAAA,UACC,gBAAA;AAAA;;UAGA,QAAA;AAAA;;QAGD,SAAA,CAAA,QAAA;AACCA,wBAAAA,MAAA,MAAA,OAAA,8BAAA,YAAA,aAAA,eAAA,KAAA,KAAA,iCAAA,IAAA,IAAA;AAAA;;;QAID;AAAA,MACD,CAAA;AAAA;IAED,cAAA,IAAA;AAEC,UAAA,cAAA;AACA,YAAA,cAAAG,cAAAA,8BAAA,KAAA,UAAA,KAAA,YAAA,EAAA;AACA,UAAA,aAAA;AACC,sBAAA,YAAA;AAAA,MACD;;QAGC,SAAA;AAAA,UACC,KAAA;AAAA;;;QAID;AAAA;;IAGF,cAAA,KAAA;AACC,YAAA,gBAAA;AAAA,QACC,OAAA,IAAA,SAAA;AAAA,QACA,WAAA,SAAA,IAAA,KAAA,CAAA,KAAA;AAAA;QAEA,WAAA;AAAA;AAED,aAAA,KAAAD,cAAAA,aAAA,EAAA,QAAA,SAAA;AACC,cAAA,aAAAA,4BAAA,GAAA;AACA,sBAAA,GAAA,IAAA,IAAA,UAAA,KAAA;AAAA,MACD,CAAA;;AAIC,sBAAA,WAAA,KAAA,YAAA,cAAA,YAAA,IAAAA,cAAAA,cAAA,QAAA,CAAA;AAAA,MACD;;;MAKA;AAGA,YAAA,sBAAA,cAAA,mBAAA,IAAAA,cAAAA,cAAA,eAAA;;AAEC,sBAAA,kBAAA,KAAA,YAAA,mBAAA;AAAA;;MAGD;;;AAKC,YAAA;AACC,wBAAA,wBAAA,MAAA,QAAA,kBAAA;;AAGAF,wBAAA,MAAA,MAAA,SAAA,8BAAA,aAAA,KAAA;AACA,wBAAA,wBAAA;QACD;AAAA;AAEA,sBAAA,wBAAA;MACD;AAGA,YAAA,mBAAA,cAAA,gBAAA,IAAAE,cAAAA,cAAA,YAAA;;QAEC,cAAAA,cAAA,cAAA;AAAA;QAEA,UAAA,OAAA;AAAA,MACD,CAAA;;;AAMCF,sBAAA,MAAA,MAAA,OAAA,8BAAA,eAAA,cAAA,YAAA;AAAA;;AAGAA,sBAAAA,MAAA,MAAA,OAAA,8BAAA,kBAAA;AAAA,MACD;AAEA,aAAA;AAAA;;AAGA,UAAA;;AAGE,cAAA,WAAA,UAAA,SAAA,GAAA,IAAA,UAAA,MAAA,GAAA,EAAA,CAAA,IAAA;AACA,iBAAA;AAAA,QACD;AAGA,YAAA,CAAA,WAAA;;QAEA;AAGA,cAAA,aAAA,MAAA,QAAA,SAAA,IAAA,YAAA,KAAA,MAAA,SAAA;;;;QAKA;;8DAOC,UAAA,2BACA,UAAA,gDAEA;AAGD,YAAA,YAAA,SAAA,SAAA,GAAA,GAAA;AACC,qBAAA,SAAA,MAAA,GAAA,EAAA,CAAA;AAAA,QACD;AAEA,eAAA;AAAA;AAGAA,4BAAA,MAAA,SAAA,8BAAA,cAAA,MAAA,SAAA,SAAA,SAAA;;MAED;AAAA;IAED,MAAA,oBAAA,MAAA;AAEC,UAAA;AACC,cAAA,sBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;;;;YAKC,aAAA;AAAA;;YAGA,OAAA,KAAA;AAAA,YACA,WAAA;AAAA,YACA,kBAAA;AAAA;UAED,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;AACA,cAAA,oBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;;;;YAKC,aAAA;AAAA;;YAGA,OAAA,KAAA;AAAA,YACA,WAAA;AAAA,YACA,kBAAA;AAAA;UAED,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;AAEA,YAAA,oBAAA,eAAA,KAAA;AACCA,wBAAA,MAAA,MAAA,SAAA,8BAAA,YAAA,QAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AACA;AAAA,QACD;;AAICA,wBAAA,MAAA,MAAA,SAAA,8BAAA,eAAA,oBAAA,IAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AACA;AAAA,QACD;AAEA,YAAA,CAAA,oBAAA,KAAA,KAAA,QAAA,oBAAA,KAAA,KAAA,KAAA,WAAA,GAAA;AAECA,wBAAAA,MAAA,MAAA,OAAA,8BAAA,yBAAA;;;;YAIC,gBAAA,KAAA;AAAA,UACD,CAAA;AAGA,eAAA,4BAAA,IAAA;AACA;AAAA,QACD;AAGA,cAAA,cAAA,kCAAA,mBAAA,KAAA,WAAA,CAAA,gBAAA,mBAAA,KAAA,YAAA,KAAA,YAAA,oBAAA,CAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,8BAAA,kCAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,8BAAA,UAAA,WAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,8BAAA,UAAA,YAAA,MAAA;AAEAA,sBAAAA,MAAA,WAAA;AAAA,UACC,KAAA;AAAA,QACD,CAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,8BAAA,YAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AAAA,MACD;AAAA;;IAID,MAAA,4BAAA,MAAA;;;;AAMC,UAAA,KAAA,aAAA;AACC,oBAAA,gCAAA,mBAAA,KAAA,WAAA,CAAA;AAAA;AAEA,oBAAA,gCAAA,mBAAA,KAAA,WAAA,CAAA;AAAA;AAEA,oBAAA;AAAA,MACD;AAEAA,oBAAA,MAAA,MAAA,OAAA,8BAAA,aAAA,SAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,8BAAA,UAAA,UAAA,MAAA;AAEA,UAAA;;AAECA,sBAAAA,MAAA,WAAA;AAAA;UAEC,SAAA,CAAA,QAAA;;;;;AAKCA,0BAAAA,MAAA,UAAA;AAAA;;YAGA,CAAA;AAAA,UACD;AAAA,QACD,CAAA;AAAA;;AAGAA,sBAAAA,MAAA,UAAA;AAAA;;QAGA,CAAA;AAAA,MACD;AAAA;;AAIAA,oBAAAA,MAAA,iBAAA;AAAA,QACC,MAAA;AAAA;AAECA,wBAAAA,MAAA,UAAA;AAAA;;;QAID;AAAA;;;AAID,UAAA,KAAA,QAAA;AAEC,YAAA,CAAA,KAAA,OAAA,WAAA,SAAA,KAAA,CAAA,KAAA,OAAA,WAAA,UAAA,GAAA;AACC,eAAA,SAAA,aAAA,KAAA;AAAA,QACD;AAEAA,sBAAAA,MAAA,WAAA;AAAA,UACC,KAAA,8BAAA,mBAAA,KAAA,MAAA,CAAA;AAAA,QACD,CAAA;AAAA;AAEAA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AAAA,MACD;AAAA;IAED,eAAA;;;AAGEA,sBAAAA,MAAA,aAAA;AAAA,UACC,MAAA,CAAA,KAAA,eAAA;AAAA;AAAA,QACD,CAAA;AAAA;;MAGD;AAAA;;;AAIAA,oBAAAA,MAAA,cAAA;AAAA,QACC,aAAA;AAAA;AAAA,MACD,CAAA;AAAA;IAED,oBAAA;;AAEEA,sBAAAA,MAAA,aAAA;AAAA;QAEA,CAAA;AAAA,MACD;AAAA;;IAGD,oBAAA;;;;IAIA,oBAAA;;;;;AAMC,YAAA,cAAA,SAAA;;AAEA,YAAA,WAAA,SAAA;AAEAA,oBAAA,MAAA,MAAA,OAAA,+BAAA,SAAA,QAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,+BAAA,gBAAA,WAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,+BAAA,4BAAA,WAAA;AAGA,YAAA,WAAA,KAAA,YAAA,QAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,+BAAA,SAAA,QAAA;;;;;;AAWC,aAAA,0BAAA,aAAA,aAAA,QAAA;AAAA,MACD;AAAA;;;AAIAA,oBAAA,MAAA,MAAA,OAAA,+BAAA,WAAA,QAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,+BAAA,UAAA,UAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,+BAAA,UAAA,WAAA;;;AAKEA,wBAAAA,MAAA,MAAA,OAAA,+BAAA,iBAAA;;;AAIEA,4BAAAA,MAAA,UAAA;AAAA;;gBAGC,aAAA;AAAA,gBACA,YAAA;AAAA;AAEC,sBAAA,SAAA,SAAA;AACCA,kCAAAA,MAAA,iBAAA;AAAA,sBACC,MAAA;AAAA,sBACA,SAAA,WAAA;AACCA,sCAAAA,MAAA,UAAA;AAAA;;wBAGA,CAAA;AAAA,sBACD;AAAA,oBACD,CAAA;AAAA,kBACD;AAAA,gBACD;AAAA,cACD,CAAA;AAAA,YACD;AAAA,UACD,CAAA;AAAA,QACD;AAAA,MACD,CAAA;AAAA;;IAGD,oBAAA,QAAA,UAAA;AACC,UAAA,CAAA,UAAA,OAAA,WAAA,UAAA;AACCA,sBAAA,MAAA,MAAA,SAAA,+BAAA,aAAA,MAAA;AACA,iBAAA,KAAA;AACA;AAAA,MACD;AAEAA,oBAAAA,MAAA,YAAA;AAAA,QACC,OAAA;AAAA,MACD,CAAA;AAEAA,oBAAA,MAAA,MAAA,OAAA,+BAAA,WAAA,MAAA;AACAA,oBAAAA,MAAA,aAAA;AAAA;QAEC,SAAA,SAAA,KAAA;;;;AAKEA,0BAAAA,MAAA,MAAA,SAAA,+BAAA,eAAA;AACAA,0BAAA,MAAA,YAAA;AACA,qBAAA,KAAA;AACA;AAAA,UACD;AAEAA,wBAAA,MAAA,MAAA,OAAA,+BAAA,WAAA,QAAA;AACAA,wBAAAA,MAAA,aAAA;AAAA,YACC;AAAA,YACA,UAAA;AAAA,YACA,SAAA,SAAAI,MAAA;;AAECJ,4BAAA,MAAA,YAAA;AACA,uBAAA,IAAA;AAAA;YAED,MAAA,SAAA,KAAA;;AAECA,4BAAA,MAAA,YAAA;AACA,uBAAA,KAAA;AAAA,YACD;AAAA,UACD,CAAA;AAAA;QAED,MAAA,SAAA,KAAA;;AAECA,wBAAA,MAAA,MAAA,SAAA,+BAAA,WAAA,MAAA;AACAA,wBAAA,MAAA,YAAA;AACA,mBAAA,KAAA;AAAA,QACD;AAAA,MACD,CAAA;AAAA;;IAGD,cAAA,QAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,+BAAA,eAAA,MAAA;AAGA,UAAA,CAAA,UAAA,OAAA,WAAA,UAAA;AACCA,sBAAA,MAAA,MAAA,SAAA,+BAAA,aAAA,MAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AACA;AAAA,MACD;AAEAA,oBAAAA,MAAA,YAAA;AAAA,QACC,OAAA;AAAA,MACD,CAAA;AAEAA,oBAAA,MAAA,MAAA,OAAA,+BAAA,WAAA,MAAA;AACAA,oBAAAA,MAAA,aAAA;AAAA;QAEC,SAAA,SAAA,KAAA;;;;AAKEA,0BAAAA,MAAA,MAAA,SAAA,+BAAA,eAAA;AACAA,0BAAA,MAAA,YAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACC,OAAA;AAAA;YAED,CAAA;AACA;AAAA,UACD;AAEAA,wBAAA,MAAA,MAAA,OAAA,+BAAA,WAAA,QAAA;AACAA,wBAAAA,MAAA,aAAA;AAAA,YACC;AAAA;;YAEA,SAAA,SAAAI,MAAA;;AAECJ,4BAAA,MAAA,YAAA;AAAA;YAED,MAAA,SAAA,KAAA;;AAECA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACC,OAAA;AAAA;cAED,CAAA;AAAA,YACD;AAAA,UACD,CAAA;AAAA;QAED,MAAA,SAAA,KAAA;;AAECA,wBAAA,MAAA,MAAA,SAAA,+BAAA,WAAA,MAAA;AACAA,wBAAA,MAAA,YAAA;AAGA,cAAA,WAAA;AACA,cAAA,IAAA,UAAA,IAAA,OAAA,SAAA,QAAA,GAAA;AACC,uBAAA;AAAA;;;AAIA,uBAAA;AAAA,UACD;AAEAA,wBAAAA,MAAA,UAAA;AAAA;YAEC,SAAA,WAAA;AAAA,YACA,aAAA;AAAA,YACA,YAAA;AAAA;AAEC,kBAAA,SAAA,SAAA;AAECA,8BAAAA,MAAA,iBAAA;AAAA;kBAEC,SAAA,WAAA;AACCA,kCAAAA,MAAA,UAAA;AAAA,sBACC,OAAA;AAAA;oBAED,CAAA;AAAA,kBACD;AAAA,gBACD,CAAA;AAAA,cACD;AAAA,YACD;AAAA,UACD,CAAA;AAAA,QACD;AAAA,MACD,CAAA;AAAA;;IAGD,YAAA,UAAA;;AAIC,YAAA,aAAA,CAAA,OAAA,OAAA,OAAA,OAAA,OAAA,QAAA,OAAA,KAAA;AACA,UAAA,WAAA,SAAA,GAAA;AAAA,eAAA;;AAIA,UAAA,WAAA,SAAA,GAAA;AAAA,eAAA;;;;AAOA,UAAA,SAAA,SAAA,GAAA;AAAA,eAAA;AAEA,aAAA;AAAA;;;;;QAMC,KAAA;AACC,iBAAA;AAAA,QACD,KAAA;AAAA;AAEC,iBAAA;AAAA,QACD,KAAA;AAAA;;QAGA,KAAA;AAAA;AAEC,iBAAA;AAAA,QACD,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA;QAEA,KAAA;;QAEA,KAAA;AAAA;QAEA,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA;;QAGA;AACC,iBAAA;AAAA,MACF;AAAA;;IAGD,aAAA,UAAA,UAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,+BAAA,SAAA,QAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,+BAAA,UAAA,QAAA;;;AAMA,WAAA,mBAAA;AAAA;;IAGD,aAAA,UAAA,UAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,+BAAA,SAAA,QAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,+BAAA,UAAA,QAAA;AAGAA,oBAAAA,MAAA,aAAA;AAAA,QACC,MAAA,CAAA,QAAA;AAAA,QACA,SAAA;AAAA,QACA,SAAA,WAAA;;;QAGA,MAAA,SAAA,KAAA;;AAECA,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AAAA,QACD;AAAA,MACD,CAAA;AAAA;;IAGD,kBAAA;;;;;;;AAQC,UAAA,KAAA,SAAA,kBAAA,GAAA;;MAEA;AAEA,UAAA,KAAA,SAAA,wBAAA,KAAA,KAAA,SAAA,qBAAA,GAAA;;MAEA;AAEA,UAAA;AACC,cAAA,MAAA,IAAA,IAAA,IAAA;AACA,eAAA,IAAA;AAAA;;MAGD;AAAA;;IAGD,gBAAA,MAAA;;AAIC,YAAA,YAAA,KAAA,mBAAA,IAAA;AAGAA,oBAAAA,MAAA,WAAA;AAAA;;AAGEA,wBAAA,MAAA,MAAA,SAAA,+BAAA,qBAAA,GAAA;AAEAA,wBAAAA,MAAA,UAAA;AAAA;;YAGC,aAAA;AAAA,YACA,YAAA;AAAA,YACA,SAAA,CAAA,QAAA;AACC,kBAAA,IAAA,SAAA;AACCA,8BAAAA,MAAA,iBAAA;AAAA,kBACC,MAAA;AAAA;AAECA,kCAAAA,MAAA,UAAA;AAAA;;oBAGA,CAAA;AAAA;kBAED,MAAA,MAAA;AACCA,kCAAAA,MAAA,UAAA;AAAA;;oBAGA,CAAA;AAAA,kBACD;AAAA,gBACD,CAAA;AAAA,cACD;AAAA,YACD;AAAA,UACD,CAAA;AAAA,QACD;AAAA,MACD,CAAA;AAAA;;IAGD,eAAA,OAAA;;;;AAGC,YAAA,QAAA,CAAA,KAAA,MAAA,MAAA,IAAA;;AAEA,aAAA,YAAA,QAAA,KAAA,IAAA,GAAA,CAAA,GAAA,QAAA,CAAA,CAAA,IAAA,MAAA,MAAA,CAAA;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC75CD,GAAG,WAAW,eAAe;"}