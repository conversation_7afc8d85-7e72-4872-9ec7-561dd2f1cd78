# 相关视频号/公众号链接功能实现文档

## 功能概述

为微信小程序添加了相关视频号/公众号链接功能，支持在产品详情页面和分类页面显示和访问相关链接。用户可以点击链接复制到剪贴板，然后在浏览器中打开。

## 实现的功能

### 1. 字段配置
- 在 `config/fields.js` 中添加了新的字段映射：
  ```javascript
  relatedLinks: '688b2e5fa849420e13f6a32d'  // 相关视频号/公众号链接
  ```

### 2. 数据格式支持
- 支持用逗号分隔的多个链接
- 自动过滤空字符串和空白字符
- 示例数据格式：`https://mp.weixin.qq.com/s/abc123,https://channels.weixin.qq.com/def456`

### 3. 智能链接识别
- 自动识别公众号文章链接（mp.weixin.qq.com）
- 自动识别视频号链接（channels.weixin.qq.com, video.weixin.qq.com）
- 其他链接显示域名

### 4. 用户交互
- 点击链接显示确认对话框
- 复制链接到剪贴板
- 提供用户友好的提示信息

## 修改的文件

### 1. config/fields.js
```javascript
// 添加新的字段映射
relatedLinks: '688b2e5fa849420e13f6a32d'  // 相关视频号/公众号链接
```

### 2. pages/form/form.vue
- 添加相关链接数据字段
- 添加相关链接显示模板
- 添加链接处理方法
- 添加相关样式

### 3. pages/category/category.vue
- 添加相关链接数据字段
- 添加相关链接显示模板
- 添加链接处理方法
- 添加相关样式
- 在页面跳转时传递相关链接数据

### 4. pages/index/index.vue
- 在页面跳转时传递相关链接数据

## 新增的方法

### getLinkDisplayText(link)
```javascript
// 获取链接显示文本
getLinkDisplayText(link) {
    // 如果是微信公众号文章链接，显示"公众号文章"
    if (link.includes('mp.weixin.qq.com')) {
        return '公众号文章';
    }
    // 如果是视频号链接，显示"视频号"
    if (link.includes('channels.weixin.qq.com') || link.includes('video.weixin.qq.com')) {
        return '视频号';
    }
    // 其他链接显示域名
    try {
        const url = new URL(link);
        return url.hostname;
    } catch (error) {
        return '相关链接';
    }
}
```

### openRelatedLink(link)
```javascript
// 打开相关链接
openRelatedLink(link) {
    console.log('打开相关链接:', link);
    
    // 在微信小程序中，外部链接需要复制到剪贴板
    uni.showModal({
        title: '打开链接',
        content: '即将复制链接到剪贴板，请在浏览器中打开',
        confirmText: '复制链接',
        cancelText: '取消',
        success: (res) => {
            if (res.confirm) {
                uni.setClipboardData({
                    data: link,
                    success: () => {
                        uni.showToast({
                            title: '链接已复制',
                            icon: 'success'
                        });
                    },
                    fail: () => {
                        uni.showToast({
                            title: '复制失败',
                            icon: 'none'
                        });
                    }
                });
            }
        }
    });
}
```

## 数据处理逻辑

### 1. 数据解析
```javascript
// 解析相关链接数据（支持逗号分隔）
const relatedLinksStr = decodeURIComponent(options.relatedLinks || '');
if (relatedLinksStr) {
    // 用逗号分隔链接，并过滤空字符串
    this.relatedLinks = relatedLinksStr.split(',')
        .map(link => link.trim())
        .filter(link => link.length > 0);
} else {
    this.relatedLinks = [];
}
```

### 2. API数据处理
```javascript
// 处理相关链接字段
const relatedLinksData = formattedItem.relatedLinks || row[FIELD_MAPPING.relatedLinks];
if (relatedLinksData && typeof relatedLinksData === 'string') {
    // 用逗号分隔链接，并过滤空字符串
    formattedItem.relatedLinks = relatedLinksData.split(',')
        .map(link => link.trim())
        .filter(link => link.length > 0);
} else {
    formattedItem.relatedLinks = [];
}
```

## 页面显示

### 1. Form 页面显示
```vue
<!-- 相关链接按钮 -->
<view v-if="relatedLinks.length > 0" class="info-card links-section">
    <view class="card-body">
        <view class="links-header">
            <uni-icons type="link" size="20" color="#2979FF"></uni-icons>
            <text class="links-title">相关视频号/公众号 ({{relatedLinks.length}}个链接)</text>
        </view>
        <view class="links-list">
            <view v-for="(link, index) in relatedLinks" :key="index" 
                  class="link-item" @tap="openRelatedLink(link)">
                <uni-icons type="paperplane-filled" size="16" color="#2979FF"></uni-icons>
                <text class="link-text">{{ getLinkDisplayText(link) }}</text>
                <uni-icons type="right" size="12" color="#999"></uni-icons>
            </view>
        </view>
    </view>
</view>
```

### 2. Category 页面显示
```vue
<!-- 父级分类相关链接 -->
<view v-if="currentRelatedLinks.length > 0" class="links-card">
    <view class="links-header">
        <uni-icons type="link" size="18" color="#007aff"></uni-icons>
        <text class="links-title">相关视频号/公众号 ({{currentRelatedLinks.length}}个链接)</text>
    </view>
    <view class="links-list">
        <view v-for="(link, index) in currentRelatedLinks" :key="index" 
              class="link-item" @tap="openRelatedLink(link)">
            <uni-icons type="paperplane-filled" size="14" color="#007aff"></uni-icons>
            <text class="link-text">{{ getLinkDisplayText(link) }}</text>
            <uni-icons type="right" size="10" color="#999"></uni-icons>
        </view>
    </view>
</view>
```

## 样式设计

### 1. Form 页面样式
- 使用卡片式设计，与介绍资料保持一致
- 蓝色主题色 (#2979FF)
- 响应式布局

### 2. Category 页面样式
- 使用渐变背景卡片设计
- 蓝色主题色 (#007aff)
- 紧凑型布局

## 使用方式

### 1. 数据录入
在后台管理系统的相关字段中输入链接，多个链接用逗号分隔：
```
https://mp.weixin.qq.com/s/nIkBc49Tto_yCwC1Np_y4g,https://channels.weixin.qq.com/video123
```

### 2. 用户操作
1. 用户在产品详情页面或分类页面看到相关链接
2. 点击任意链接
3. 系统显示确认对话框
4. 用户确认后，链接被复制到剪贴板
5. 用户可以在浏览器中粘贴并打开链接

## 测试用例

### 1. 单个链接测试
- 输入：`https://mp.weixin.qq.com/s/abc123`
- 预期：显示"公众号文章"

### 2. 多个链接测试
- 输入：`https://mp.weixin.qq.com/s/abc123,https://channels.weixin.qq.com/def456`
- 预期：显示2个链接，分别为"公众号文章"和"视频号"

### 3. 空数据测试
- 输入：空字符串或null
- 预期：不显示相关链接区域

### 4. 格式错误测试
- 输入：`invalid-url,https://mp.weixin.qq.com/s/abc123`
- 预期：显示"相关链接"和"公众号文章"

## 注意事项

### 1. 微信小程序限制
- 微信小程序不能直接打开外部链接
- 只能复制链接到剪贴板
- 用户需要手动在浏览器中打开

### 2. 数据格式要求
- 链接必须是完整的URL格式
- 多个链接用英文逗号分隔
- 避免在链接中包含逗号

### 3. 用户体验
- 提供清晰的操作提示
- 显示友好的链接类型名称
- 复制成功后给出反馈

### 4. 性能考虑
- 链接数量建议控制在10个以内
- 避免过长的URL影响页面性能
