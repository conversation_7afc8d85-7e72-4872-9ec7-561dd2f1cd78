# 视频预览功能使用示例

## 基本使用

### 1. 数据格式要求

相关资料数据需要包含以下字段：

```javascript
const material = {
    original_file_name: "产品介绍视频.mp4",  // 文件名（必需）
    DownloadUrl: "https://example.com/video.mp4",  // 主要下载链接（推荐）
    original_file_full_path: "https://example.com/backup-video.mp4",  // 备用链接（可选）
    file_size: 10485760  // 文件大小（字节）
};
```

### 2. 支持的视频格式

目前支持以下视频格式：
- MP4 (推荐)
- AVI
- MOV
- WMV
- FLV
- WebM
- MKV
- OGG

### 3. 支持的图片格式

同时支持以下图片格式的预览：
- JPG/JPEG
- PNG
- GIF
- BMP
- WebP

## 代码示例

### 1. 在现有页面中使用

如果你想在其他页面中使用相同的功能，可以复制以下代码：

```vue
<template>
    <!-- 资料列表 -->
    <view v-for="(material, index) in materials" :key="index" 
          class="material-item" @tap="previewMaterial(material)">
        <view class="material-icon">
            <uni-icons :type="getMaterialIcon(material.original_file_name)" 
                      size="24" color="#2979FF"></uni-icons>
        </view>
        <view class="material-info">
            <text class="material-name">{{ material.original_file_name }}</text>
            <text class="material-size">{{ formatFileSize(material.file_size) }}</text>
        </view>
    </view>

    <!-- 视频预览弹窗 -->
    <view v-if="showVideoModal" class="video-modal" @tap="closeVideoModal">
        <view class="video-modal-content" @tap.stop>
            <view class="video-modal-header">
                <text class="video-modal-title">{{ currentVideoName }}</text>
                <view class="video-modal-close" @tap="closeVideoModal">
                    <uni-icons type="close" size="20" color="#fff"></uni-icons>
                </view>
            </view>
            <view class="video-modal-body">
                <video 
                    :src="currentVideoUrl" 
                    controls 
                    autoplay 
                    :show-fullscreen-btn="true"
                    :show-play-btn="true"
                    :show-center-play-btn="true"
                    class="video-player"
                ></video>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            materials: [],
            showVideoModal: false,
            currentVideoUrl: '',
            currentVideoName: ''
        }
    },
    methods: {
        // 获取文件类型
        getFileType(fileName) {
            const ext = fileName.split('.').pop().toLowerCase();
            
            const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'ogg'];
            if (videoTypes.includes(ext)) return 'video';
            
            const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
            if (imageTypes.includes(ext)) return 'image';
            
            if (ext === 'pdf') return 'pdf';
            
            const docTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
            if (docTypes.includes(ext)) return 'document';
            
            return 'unknown';
        },

        // 获取文件图标
        getMaterialIcon(fileName) {
            const ext = fileName.split('.').pop().toLowerCase();
            switch (ext) {
                case 'pdf': return 'paperplane';
                case 'doc':
                case 'docx': return 'compose';
                case 'xls':
                case 'xlsx': return 'bars';
                case 'ppt':
                case 'pptx': return 'videocam';
                case 'mp4':
                case 'avi':
                case 'mov':
                case 'wmv':
                case 'flv':
                case 'webm':
                case 'mkv': return 'videocam-filled';
                case 'jpg':
                case 'jpeg':
                case 'png':
                case 'gif':
                case 'bmp':
                case 'webp': return 'image';
                default: return 'folder';
            }
        },

        // 预览资料文件
        previewMaterial(material) {
            const downloadUrl = material.DownloadUrl;
            const originalUrl = material.original_file_full_path;
            const fileName = material.original_file_name;

            const fileType = this.getFileType(fileName);

            if (fileType === 'video') {
                this.previewVideo(downloadUrl || originalUrl, fileName);
            } else if (fileType === 'image') {
                this.previewImage(downloadUrl || originalUrl, fileName);
            } else {
                // 其他文件类型的处理逻辑
                this.previewDocument(downloadUrl, originalUrl, fileName);
            }
        },

        // 预览视频文件
        previewVideo(videoUrl, fileName) {
            this.showVideoModal = true;
            this.currentVideoUrl = videoUrl;
            this.currentVideoName = fileName;
        },

        // 预览图片文件
        previewImage(imageUrl, fileName) {
            uni.previewImage({
                urls: [imageUrl],
                current: imageUrl,
                success: function() {
                    console.log('图片预览成功');
                },
                fail: function(err) {
                    console.error('图片预览失败:', err);
                    uni.showToast({
                        title: '图片预览失败',
                        icon: 'none'
                    });
                }
            });
        },

        // 关闭视频预览
        closeVideoModal() {
            this.showVideoModal = false;
            this.currentVideoUrl = '';
            this.currentVideoName = '';
        },

        // 格式化文件大小
        formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    }
}
</script>
```

### 2. 样式定义

```css
/* 视频预览弹窗样式 */
.video-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
}

.video-modal-content {
    width: 95%;
    max-width: 800rpx;
    background-color: #000;
    border-radius: 16rpx;
    overflow: hidden;
}

.video-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 30rpx;
    background-color: rgba(0, 0, 0, 0.8);
}

.video-modal-title {
    font-size: 28rpx;
    color: #fff;
    flex: 1;
    margin-right: 20rpx;
    word-break: break-all;
}

.video-modal-close {
    padding: 10rpx;
}

.video-modal-body {
    position: relative;
}

.video-player {
    width: 100%;
    height: 400rpx;
    background-color: #000;
}

/* 资料列表样式 */
.material-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    border-bottom: 1px solid #eee;
    background-color: #fff;
}

.material-item:active {
    background-color: #f5f5f5;
}

.material-icon {
    margin-right: 20rpx;
}

.material-info {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.material-name {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 8rpx;
    word-break: break-all;
}

.material-size {
    font-size: 24rpx;
    color: #999;
}
```

## 高级用法

### 1. 自定义视频播放器配置

```javascript
// 在 video 组件中添加更多配置
<video 
    :src="currentVideoUrl" 
    controls 
    autoplay 
    :show-fullscreen-btn="true"
    :show-play-btn="true"
    :show-center-play-btn="true"
    :enable-progress-gesture="true"
    :show-progress="true"
    :show-loading="true"
    class="video-player"
    @error="onVideoError"
    @play="onVideoPlay"
    @pause="onVideoPause"
></video>
```

### 2. 添加视频事件处理

```javascript
methods: {
    onVideoError(e) {
        console.error('视频播放错误:', e);
        uni.showToast({
            title: '视频播放失败',
            icon: 'none'
        });
    },
    
    onVideoPlay(e) {
        console.log('视频开始播放');
    },
    
    onVideoPause(e) {
        console.log('视频暂停播放');
    }
}
```

### 3. 添加加载状态

```javascript
data() {
    return {
        // ...
        videoLoading: false
    }
},

methods: {
    previewVideo(videoUrl, fileName) {
        this.videoLoading = true;
        this.showVideoModal = true;
        this.currentVideoUrl = videoUrl;
        this.currentVideoName = fileName;
        
        // 模拟加载完成
        setTimeout(() => {
            this.videoLoading = false;
        }, 1000);
    }
}
```

## 注意事项

### 1. 视频URL要求
- 必须是 HTTPS 协议
- 服务器需要支持跨域访问
- 建议使用 CDN 加速

### 2. 文件大小建议
- 视频文件建议不超过 50MB
- 图片文件建议不超过 10MB
- 大文件可能导致加载缓慢

### 3. 兼容性考虑
- 不同微信版本可能支持的视频格式不同
- 建议优先使用 MP4 格式
- 提供降级方案（如跳转到外部播放器）

### 4. 用户体验优化
- 添加加载状态提示
- 提供播放失败的错误处理
- 支持全屏播放
- 考虑网络状况提醒用户
