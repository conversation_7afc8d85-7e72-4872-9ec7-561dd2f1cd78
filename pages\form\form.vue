<template>
	<view class="container">
		<!-- 基本信息卡片 -->
		<view class="info-card">
			<view class="card-header">产品信息</view>
			<view class="card-body">
				<view class="form-item">
					<text class="text">产品名称: {{productName}}</text>
				</view>
				<view class="form-item">
					<text class="text">产品编码: {{productCode}}</text>
				</view>
				<view class="form-item">
					<text class="text">产品经理: {{productManager}}</text>
				</view>
				<view class="form-item">
					<text class="phone-link" @click="callnum(contactPhone)">联系方式: {{contactPhone}}</text>
				</view>
				<view class="form-item">
					<text class="text">业务类型: {{YWLX}}</text>
				</view>
			</view>
		</view>

		<!-- 代理信息卡片 -->
		<view v-if="YWLX === '代理'" class="info-card agent-section">
			<view class="card-header">代理信息</view>
			<view class="card-body">
				<view class="form-item link-item" @click="openDLPPGW">
					<uni-icons type="link" size="18" color="#2979FF"></uni-icons>
					<text class="link-text">代理品牌官网</text>
					<uni-icons type="forward" size="14" color="#999"></uni-icons>
				</view>
				<text class="full-url" selectable @tap.stop="copyUrl(DLPPGW)">{{DLPPGW}}</text>
				<view class="form-item brand-image">
					<text class="text">代理品牌:</text>
					<image
						:src="agentBrandImage"
						mode="aspectFit"
						class="brand-logo"
						@tap="previewAgentImage"
					></image>
				</view>
			</view>
		</view>

		<!-- 联系信息卡片 -->
		<view class="info-card">
			<view class="card-body">
				<view class="form-item">
					<text class="text">服务内容简介: {{serviceDescription}}</text>
				</view>
				<view class="form-item">
					<image v-if="highResImageUrl" :src="highResImageUrl" mode="aspectFit" class="image"
						@tap="previewImage"></image>
					<image v-else src="/static/熙迈LOGO.png" mode="aspectFit" class="image"></image>
				</view>
			</view>
		</view>

		<!-- 介绍资料按钮 -->
		<view v-if="introductionMaterials.length > 0" class="info-card materials-section">
			<view class="card-body">
				<view class="materials-button" @tap="showMaterialsList">
					<uni-icons type="folder" size="20" color="#2979FF"></uni-icons>
					<text class="materials-text">查看相关资料 ({{introductionMaterials.length}}个文件)</text>
					<uni-icons type="forward" size="14" color="#999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 案例情况卡片 -->
		<view v-if="caseStudies.length > 0" class="info-card case-studies-section">
			<view class="card-header">相关案例</view>
			<view class="card-body">
				<view v-for="(study, index) in caseStudies" :key="index" class="case-study-item">
					<text class="case-study-client">{{ study.clientName }}</text>
					<text class="case-study-details">{{ study.details }}</text>
				</view>
			</view>
		</view>

		<view v-if="recommendations.length > 0" class="recommend-section">
		  <view class="section-title">您可能对以下产品感兴趣</view>
		  <view class="category-list">
				<view v-for="(item, index) in recommendations" :key="index" class="category-item"
					@click="handleCategoryClick(item)">
					<view class="img">
						<image class="icon" :src="item.imageUrl || '/static/熙迈LOGO.png'" mode="heightFix"></image>
					</view>
					<view class="banner">
						<view class="banner-left">
							<view class="top-blue">
								<text class="english-name">
									{{ item.englishName }}
									<span style="display:inline-block;width:16rpx;"></span>
									<text class="more-text" style="margin-left: 12rpx;">更多&gt;&gt;</text>
								</text>
							</view>
							<view class="bottom-white">
								<view class="logo">
									<image v-if="item.YWLX === '代理'" class="icon"
										:src="'/static/代理图标.png'" mode="aspectFit"></image>
									<image v-else class="icon"
										:src="'/static/熙迈LOGO.png'" mode="aspectFit"></image>
								</view>
								<view class="bottom-white-text">{{item.productName}}</view>
								<view class="like-section" @click.stop="handleLike(item)">
									<image class="like-icon" :src="item.isLiked ? '/static/红色小红心.svg' : '/static/灰色小红心.svg'"></image>
									<view v-if="item.showHeart" class="pop-animation">
										<text class="pop-heart">❤️</text>
										<text class="pop-count">{{ item.likeCount }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
		  </view>
		</view>

		<!-- 固定底部按钮 -->
<!-- 		<view class="get_quote" @click="callnum(contactPhone)">
			<view class="quote-container">
				<uni-icons type="notification" size="24" color="#fff"></uni-icons>
				<text class="quote-text">获取报价</text>
			</view>
		</view> -->
	</view>

	<!-- 介绍资料弹窗 -->
	<view v-if="showMaterialsModal" class="materials-modal" @tap="hideMaterialsList">
		<view class="materials-modal-content" @tap.stop>
			<view class="materials-modal-header">
				<text class="materials-modal-title">相关资料</text>
				<view class="materials-modal-close" @tap="hideMaterialsList">
					<uni-icons type="close" size="20" color="#666"></uni-icons>
				</view>
			</view>
			<view class="materials-modal-body">
				<view v-for="(material, index) in introductionMaterials" :key="index"
					  class="material-item" @tap="previewMaterial(material)">
					<view class="material-icon">
						<uni-icons :type="getMaterialIcon(material.original_file_name)" size="24" color="#2979FF"></uni-icons>
					</view>
					<view class="material-info">
						<text class="material-name">{{ material.original_file_name }}</text>
						<text class="material-size">{{ formatFileSize(material.file_size) }}</text>
					</view>
					<view class="material-action">
						<uni-icons type="eye" size="16" color="#999"></uni-icons>
					</view>
				</view>
			</view>
		</view>
	</view>

	<view class="fixed-customer-service">
		<button class="service-btn"  @click="handleContact(productManager)">
			<view class="text-container">
				<text class="btn-text">微信客服</text>
				<text class="btn-subtext">如有需求，请点我联系</text>
			</view>
		</button>
	</view>
</template>

<script>
	import {
		FIELD_MAPPING,
		PRODUCT_MANAGER_SERVICE_LINKS
	} from '@/config/fields.js';
	import {
		objectToParams
	} from '@/utils/url.js';
	import ShareUtils from '@/utils/share.js';

	export default {
		data() {
			return {
				contactPhone: '',
				productName: '',
				productCode: '',
				productManager: '',
				contactInfo: '',
				serviceDescription: '',
				imageUrl: '',
				highResImageUrl: '',
				YWLX: '',
				agentBrandImage: '',
				DLPPGW: '',
				chanpingtu: '',
				englishName: '',
				DLPPLOGO: '',
				recommendations: [],
				likesLog: {}, // 新增点赞记录
				caseStudies: [], // 新增案例情况
				introductionMaterials: [], // 介绍资料
				showMaterialsModal: false, // 控制资料弹窗显示
				rowid: '' // 当前产品的rowid
			}
		},
		async onLoad(options) {


			// 检查是否是分享模式
			if (options.shareMode === '1') {
				// 分享模式：只有基本信息，需要重新获取数据
				this.productName = decodeURIComponent(options.productName || '');
				this.productCode = decodeURIComponent(options.productCode || '');
				this.rowid = decodeURIComponent(options.rowid || ''); // 添加rowid处理



				if (!this.productName && !this.productCode) {
					console.log('❌ 缺少必要的产品信息');
					uni.showModal({
						title: '提示',
						content: '分享链接中缺少产品信息，可能是因为产品名称过长导致的。请从产品列表重新进入此页面。',
						showCancel: false,
						confirmText: '返回首页',
						success: () => {
							uni.switchTab({
								url: '/pages/home/<USER>'
							});
						}
					});
					return;
				}

				this.loadDataFromAPI();
			} else {
				console.log('📝 正常模式，解析URL参数中的完整数据');
				// 正常模式：从URL参数解析所有数据
				this.serviceDescription = decodeURIComponent(options.serviceDescription || '');
				this.productName = decodeURIComponent(options.productName || '');
				this.productCode = decodeURIComponent(options.productCode || '');
				this.productManager = decodeURIComponent(options.productManager || '');
				this.contactInfo = decodeURIComponent(options.contactInfo || '');
				this.YWLX = decodeURIComponent(options.YWLX || '');
				this.contactPhone = decodeURIComponent(options.contactPhone || '');
				this.agentBrandImage = decodeURIComponent(options.agentBrandImage || '');
				this.DLPPGW = decodeURIComponent(options.DLPPGW || '');
				this.chanpingtu = decodeURIComponent(options.chanpingtu || '');
				this.englishName = decodeURIComponent(options.englishName || '');
				this.DLPPLOGO = decodeURIComponent(options.DLPPLOGO || '');
				if (this.contactPhone.startsWith('+86')) {
					this.contactPhone = this.contactPhone.substring(3); // 去掉前3个字符
				}

				try {
					this.recommendations = JSON.parse(decodeURIComponent(options.recommendations || '[]'))
					this.recommendations.sort((a, b) => b.likeCount - a.likeCount);
				} catch (error) {
					console.error('解析推荐数据失败:', error)
				}
				// 解析案例情况数据
				try {
					this.caseStudies = JSON.parse(decodeURIComponent(options.caseStudies || '[]'));
				} catch (error) {
					console.error('解析案例情况数据失败:', error);
				}
				// 解析介绍资料数据
				try {
					this.introductionMaterials = JSON.parse(decodeURIComponent(options.introductionMaterials || '[]'));
				} catch (error) {
					console.error('解析介绍资料数据失败:', error);
				}
			}
			// 解析图片URL
			try {
				const imageData = decodeURIComponent(options.imageUrl || '');
				const chanpingtuData = decodeURIComponent(options.chanpingtu || '');

				if (imageData.startsWith('http')) {
					this.imageUrl = imageData;
				} else {
					// 使用getImageUrl方法获取最高质量的图片
					this.imageUrl = this.getImageUrl(imageData);
				}

				// 优先使用chanpingtu作为高清图片
				if (chanpingtuData) {
					if (chanpingtuData.startsWith('http')) {
						this.highResImageUrl = chanpingtuData;
					} else {
						this.highResImageUrl = this.getImageUrl(chanpingtuData);
					}
				} else {
					this.highResImageUrl = this.imageUrl;
				}
			} catch (error) {
				console.error('解析图片URL失败:', error);
				this.imageUrl = '/static/熙迈LOGO.png';
				this.highResImageUrl = this.imageUrl;
			}
			try {
				const dlppData = decodeURIComponent(options.agentBrandImage || '');
				// console.log('Raw dlppData:', dlppData); // 打印原始数据

				if (dlppData.startsWith('/static/') || dlppData.startsWith('http://') || dlppData.startsWith('https://')) {
					this.agentBrandImage = dlppData;
				} else {
					// 否则尝试解析为 JSON
					const dlppArray = JSON.parse(dlppData || '[]');
					if (dlppArray.length > 0 && dlppArray[0].fileUrl) {
						this.agentBrandImage = dlppArray[0].fileUrl.startsWith('http') ?
							dlppArray[0].fileUrl :
							`https://${dlppArray[0].fileUrl}`;
					} else {
						this.agentBrandImage = '/static/代理图标.png'; // 默认值
					}
				}
			} catch (error) {
				console.error('解析代理品牌图片失败:', error);
				this.agentBrandImage = '/static/代理图标.png'; // 默认值
			}

			// 如果没有推荐产品数据，则自动获取
			if (this.recommendations.length === 0) {
				await this.loadRecommendationsAndCases();
			}


		},

		// 分享功能
		onShareAppMessage(res) {
			console.log('=== Form页面分享给好友 ===');
			console.log('分享触发参数:', res);
			console.log('当前页面数据:', {
				productName: this.productName,
				productCode: this.productCode,
				productManager: this.productManager,
				imageUrl: this.imageUrl,
				recommendationsCount: this.recommendations.length
			});

			const shareTitle = this.productName ? `${this.productName} - 熙迈科技` : '熙迈科技产品详情';

			// 由于分享链接长度限制，优先传递产品代码（通常较短）
			let sharePath;
			if (this.productCode) {
				// 优先使用产品代码，因为通常比产品名称短
				sharePath = `pages/form/form?productCode=${encodeURIComponent(this.productCode)}&shareMode=1`;
			} else if (this.productName) {
				// 如果没有产品代码，使用产品名称
				sharePath = `pages/form/form?productName=${encodeURIComponent(this.productName)}&shareMode=1`;
			} else {
				// 都没有的话，只传递分享模式
				sharePath = `pages/form/form?shareMode=1`;
			}

			// 检查URL长度，如果超过限制则进一步简化
			if (sharePath.length > 200) {
				console.log('⚠️ 分享路径过长，进一步简化');
				if (this.productCode && this.productCode.length < 50) {
					// 如果产品代码不太长，保留产品代码
					sharePath = `pages/form/form?productCode=${encodeURIComponent(this.productCode)}&shareMode=1`;
				} else {
					// 否则只保留分享模式，页面将显示错误提示
					sharePath = `pages/form/form?shareMode=1`;
					console.log('⚠️ 产品信息过长，分享链接将无法包含产品信息');
				}
			}

			console.log('构建的分享路径:', sharePath);
			console.log('分享路径长度:', sharePath.length);

			const shareConfig = ShareUtils.getDefaultShareConfig({
				title: shareTitle,
				path: sharePath,
				imageUrl: this.highResImageUrl || this.imageUrl || '/static/熙迈LOGO.png'
			});

			console.log('最终分享配置:', shareConfig);
			console.log('=== Form页面分享配置完成 ===');

			return shareConfig;
		},

		onShareTimeline(res) {
			console.log('=== Form页面分享到朋友圈 ===');
			console.log('分享触发参数:', res);

			const shareTitle = this.productName ? `${this.productName} - 熙迈科技` : '熙迈科技产品详情';

			// 由于分享链接长度限制，优先传递产品代码（通常较短）
			let sharePath;
			if (this.productCode) {
				// 优先使用产品代码，因为通常比产品名称短
				sharePath = `pages/form/form?productCode=${encodeURIComponent(this.productCode)}&shareMode=1`;
			} else if (this.productName) {
				// 如果没有产品代码，使用产品名称
				sharePath = `pages/form/form?productName=${encodeURIComponent(this.productName)}&shareMode=1`;
			} else {
				// 都没有的话，只传递分享模式
				sharePath = `pages/form/form?shareMode=1`;
			}

			// 检查URL长度，如果超过限制则进一步简化
			if (sharePath.length > 200) {
				console.log('⚠️ 分享路径过长，进一步简化');
				if (this.productCode && this.productCode.length < 50) {
					// 如果产品代码不太长，保留产品代码
					sharePath = `pages/form/form?productCode=${encodeURIComponent(this.productCode)}&shareMode=1`;
				} else {
					// 否则只保留分享模式，页面将显示错误提示
					sharePath = `pages/form/form?shareMode=1`;
					console.log('⚠️ 产品信息过长，分享链接将无法包含产品信息');
				}
			}

			console.log('构建的分享路径:', sharePath);

			const shareConfig = ShareUtils.getDefaultShareConfig({
				title: shareTitle,
				path: sharePath,
				imageUrl: this.highResImageUrl || this.imageUrl || '/static/熙迈LOGO.png'
			});

			console.log('最终分享配置:', shareConfig);
			console.log('=== Form页面朋友圈分享配置完成 ===');

			return shareConfig;
		},

		methods: {
			// 从API重新加载数据（用于分享模式）
			async loadDataFromAPI() {


				try {
					// 根据产品名称或产品编码搜索对应的产品数据
					const filters = [];

					// 优先使用产品编码搜索，因为它更精确
					if (this.productCode) {

						filters.push({
							controlId: FIELD_MAPPING.productCode, // 使用配置中的产品编码字段ID
							dataType: 2,
							spliceType: 1,
							filterType: 2, // 等于（完全匹配）
							value: this.productCode
						});
					} else if (this.productName) {

						// 如果没有产品编码，则使用产品名称
						filters.push({
							controlId: FIELD_MAPPING.productName, // 使用配置中的产品名称字段ID
							dataType: 2,
							spliceType: 1,
							filterType: 2, // 等于（完全匹配）
							value: this.productName
						});
					}



					if (filters.length === 0) {
						console.log('❌ 没有有效的搜索条件');
						uni.showToast({
							title: '缺少产品信息',
							icon: 'none'
						});
						return;
					}

					const requestData = {
						appKey: '984e1ff028f80150',
						sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
						worksheetId: 'fenlei',
						pageSize: 1,
						pageIndex: 1,
						listType: 0,
						controls: [],
						filters: filters
					};



					const response = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',
						method: 'POST',
						data: requestData,
						header: {
							'Content-Type': 'application/json'
						}
					});



					if (response.data && response.data.data) {

					}

					if (response.data && response.data.data && response.data.data.rows && response.data.data.rows.length > 0) {
						const productData = response.data.data.rows[0];
						const formattedData = this.formatAPIData(productData);

						// 更新页面数据
						this.productName = formattedData.productName || '';  // 添加产品名称
						this.productCode = formattedData.productCode || this.productCode;  // 更新产品编码
						this.serviceDescription = formattedData.serviceDescription || '';
						this.productManager = formattedData.productManager || '';
						this.contactInfo = formattedData.contactInfo || '';
						this.YWLX = formattedData.YWLX || '';
						this.contactPhone = formattedData.contactPhone || '';
						// 处理代理品牌图片（已在formatAPIData中处理过，直接赋值）
						this.agentBrandImage = formattedData.agentBrandImage || '/static/代理图标.png';
						this.DLPPGW = formattedData.DLPPGW || '';
						this.chanpingtu = formattedData.chanpingtu || '';
						this.englishName = formattedData.englishName || '';
						this.DLPPLOGO = formattedData.DLPPLOGO || '';
						this.imageUrl = formattedData.imageUrl || '/static/熙迈LOGO.png';  // 添加产品图片
						// 优先使用chanpingtu字段作为高清图片，如果没有则使用imageUrl
						this.highResImageUrl = formattedData.chanpingtu || formattedData.imageUrl || '/static/熙迈LOGO.png';  // 添加高清图片
						this.introductionMaterials = formattedData.introductionMaterials || [];  // 添加介绍资料
						this.rowid = formattedData.rowId || '';  // 添加rowid

						if (this.contactPhone.startsWith('+86')) {
							this.contactPhone = this.contactPhone.substring(3);
						}


						// 获取推荐数据和案例数据
						await this.loadRecommendationsAndCases();
					} else {
						console.log('❌ 未找到匹配的产品数据');
						uni.showToast({
							title: '未找到产品信息',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('重新加载数据失败:', error);
					uni.showToast({
						title: '加载数据失败',
						icon: 'none'
					});
				}
			},

			// 加载推荐数据和案例数据
			async loadRecommendationsAndCases() {
				try {
					// 如果有rowid，使用关联API获取精确的推荐和案例数据
					if (this.rowid) {
						console.log('使用rowid获取关联数据:', this.rowid);

						// 获取推荐数据
						const recommendResponse = await uni.request({
							url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',
							method: 'POST',
							data: {
								appKey: '984e1ff028f80150',
								sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
								worksheetId: 'fenlei',
								rowId: this.rowid,
								controlId: 'GLCP', // 推荐产品字段ID
								pageSize: 10,
								pageIndex: 1
							},
							header: {
								'Content-Type': 'application/json'
							}
						});

						if (recommendResponse.data && recommendResponse.data.data && recommendResponse.data.data.rows) {
							this.recommendations = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));
							this.recommendations.sort((a, b) => b.likeCount - a.likeCount);
						}

						// 获取案例数据
						const caseStudiesResponse = await uni.request({
							url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',
							method: 'POST',
							data: {
								appKey: '984e1ff028f80150',
								sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
								worksheetId: 'fenlei',
								rowId: this.rowid,
								controlId: 'ALQK', // 案例字段ID
								pageSize: 10,
								pageIndex: 1
							},
							header: {
								'Content-Type': 'application/json'
							}
						});

						if (caseStudiesResponse.data && caseStudiesResponse.data.data && caseStudiesResponse.data.data.rows) {
							this.caseStudies = caseStudiesResponse.data.data.rows.map(row => ({
								clientName: row[FIELD_MAPPING.caseClientName] || '',
								details: row[FIELD_MAPPING.caseDetails] || ''
							}));
							console.log('✅ 获取到案例数据，数量:', this.caseStudies.length);
						} else {
							this.caseStudies = [];
						}
					} else {
						console.log('没有rowid，使用通用推荐数据');

						// 降级方案：获取通用推荐数据
						const recommendResponse = await uni.request({
							url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',
							method: 'POST',
							data: {
								appKey: '984e1ff028f80150',
								sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
								worksheetId: 'fenlei',
								pageSize: 6,
								pageIndex: 1,
								listType: 0,
								controls: [],
								filters: []
							},
							header: {
								'Content-Type': 'application/json'
							}
						});

						if (recommendResponse.data && recommendResponse.data.data && recommendResponse.data.data.rows) {
							// 随机选择一些产品作为推荐
							const allItems = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));
							const shuffled = allItems.sort(() => 0.5 - Math.random());
							this.recommendations = shuffled.slice(0, 4); // 取前4个
							this.recommendations.sort((a, b) => b.likeCount - a.likeCount);
						}

						// 没有rowid时，案例数据为空
						this.caseStudies = [];
					}
				} catch (error) {
					console.error('❌ 加载推荐数据和案例数据失败:', error);
					this.recommendations = [];
					this.caseStudies = [];
				}
			},

			handleLike(item) {
				// 1. Toggle the UI state immediately (Optimistic Update)
				item.isLiked = !item.isLiked;
				
				let webhookAction = '';
			
				if (item.isLiked) {
					// --- UI & Local State Update for LIKE ---
					item.likeCount++;
					webhookAction = 'increment';
					this.$set(this.likesLog, item.rowId, true);
			
					// Trigger animation
					this.$set(item, 'showHeart', true);
					setTimeout(() => {
						this.$set(item, 'showHeart', false);
					}, 600);
				} else {
					// --- UI & Local State Update for UNLIKE ---
					item.likeCount--;
					webhookAction = 'decrement';
					this.$delete(this.likesLog, item.rowId);
				}
			
				// Update local storage for persistence across sessions
				uni.setStorageSync('likes_log', this.likesLog);
			
				// 2. Send the corresponding command to the webhook
				uni.request({
					url: 'https://dmit.duoningbio.com/api/workflow/hooks/Njg2Y2ZhNDk3MDMwNzAyNDViNDMxOTcx',
					method: 'POST',
					header: {
						'Content-Type': 'application/json'
					},
					data: {
						action: webhookAction,
						id: item.rowId
					},
					success: (res) => {
						console.log(`Webhook '${webhookAction}' for rowid ${item.rowId} sent successfully. Response:`, res.data);
					},
					fail: (err) => {
						console.error(`Webhook '${webhookAction}' for rowid ${item.rowId} failed:`, err);
					}
				});
			},
			handleContact(pm) {
				// console.log(pm);
				let serviceLink = 'https://work.weixin.qq.com/kfid/kfcaf7fbb93aa905a54'; // 默认链接
				const managerLink = PRODUCT_MANAGER_SERVICE_LINKS.find(item => item.manager === pm);
				if (managerLink) {
					serviceLink = managerLink.serviceLink;
				}
				
				wx.openCustomerServiceChat({
					extInfo: {
						url: serviceLink
					},
					corpId: 'wwa76e36d25343b6b9',
					success(res) {
					}
				})
			},
			formatAPIData(row) {
				const formattedItem = {
					rowId: row.rowid || '',
					likeCount: parseInt(row['DZS']) || 0,
					isLiked: false,
					showHeart: false
				};
				Object.keys(FIELD_MAPPING).forEach(key => {
					const apiFieldId = FIELD_MAPPING[key];
					formattedItem[key] = row[apiFieldId] || '';
				});
			
				// 添加图片URL处理
				if (formattedItem.imageUrl || row[FIELD_MAPPING.imageUrl]) {
					formattedItem.imageUrl = this.getImageUrl(formattedItem.imageUrl || row[FIELD_MAPPING.imageUrl]);
				}

				// 处理产品图字段
				if (formattedItem.chanpingtu || row[FIELD_MAPPING.chanpingtu]) {
					formattedItem.chanpingtu = this.getImageUrl(formattedItem.chanpingtu || row[FIELD_MAPPING.chanpingtu]);
				}

				// 处理代理品牌图片字段
				const agentBrandImageData = formattedItem.agentBrandImage || row[FIELD_MAPPING.agentBrandImage];
				if (agentBrandImageData) {
					formattedItem.agentBrandImage = this.getImageUrl(agentBrandImageData);
				} else {
					formattedItem.agentBrandImage = '/static/代理图标.png';
				}

				// 处理介绍资料字段
				const introMaterialsData = formattedItem.introductionMaterials || row[FIELD_MAPPING.introductionMaterials];
				if (introMaterialsData) {
					try {
						formattedItem.introductionMaterials = Array.isArray(introMaterialsData) ?
							introMaterialsData : JSON.parse(introMaterialsData);
					} catch (error) {
						console.error('解析介绍资料失败:', error);
						formattedItem.introductionMaterials = [];
					}
				} else {
					formattedItem.introductionMaterials = [];
				}

				return formattedItem;
			},
			getImageUrl(imageData) {
				try {
					// 如果已经是HTTP URL，直接返回（去掉查询参数）
					if (typeof imageData === 'string' && imageData.startsWith('http')) {
						let cleanUrl = imageData.includes('?') ? imageData.split('?')[0] : imageData;
						return cleanUrl;
					}

					// 如果是空值，返回默认图
					if (!imageData) {
						return '/static/熙迈LOGO.png';
					}

					// 支持直接传入数组或JSON字符串
					const parsedData = Array.isArray(imageData) ? imageData : JSON.parse(imageData);

					if (!Array.isArray(parsedData) || parsedData.length === 0) {
						console.warn("图片数据格式错误或为空");
						return '/static/熙迈LOGO.png';
					}

					// 获取第一个图片对象
					const imageItem = parsedData[0];

					// 返回优先级：large_thumbnail_full_path > original_file_full_path > thumbnail_full_path > preview_url > 默认图
					let imageUrl = imageItem.large_thumbnail_full_path ||
						imageItem.original_file_full_path ||
						imageItem.thumbnail_full_path ||
						imageItem.preview_url ||
						'/static/熙迈LOGO.png';

					// 去掉查询参数，因为小程序可能不支持某些图片处理参数
					if (imageUrl && imageUrl.includes('?')) {
						imageUrl = imageUrl.split('?')[0];
					}

					return imageUrl;

				} catch (error) {
					console.error("解析图片URL失败:", error.message, "原始数据:", imageData);
					return '/static/熙迈LOGO.png';
				}
			},
			async handleCategoryClick(item) {
				let recommendations = [];
				try {
					const subCategoryResponse = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',
						method: 'POST',
						data: {
							appKey: '984e1ff028f80150',
							sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
							worksheetId: 'fenlei',
							pageSize: 50,
							pageIndex: 1,
							rowId: item.rowId,
							controlId: '67b2dd3aef727a4cd047da37',
							getSystemControl: false
						},
						header: {
							'Content-Type': 'application/json'
						}
					});
					const recommendResponse = await uni.request({
						url: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',
						method: 'POST',
						data: {
							appKey: '984e1ff028f80150',
							sign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',
							worksheetId: 'fenlei',
							pageSize: 50,
							pageIndex: 1,
							rowId: item.rowId,
							controlId: 'GLCP',
							getSystemControl: false
						},
						header: {
							'Content-Type': 'application/json'
						}
					});
			
					if (subCategoryResponse.statusCode !== 200) {
						console.error('请求子分类失败:', response);
						uni.showToast({
							title: '请求子分类失败',
							icon: 'none'
						});
						return;
					}
					
			
					if (!subCategoryResponse.data || !subCategoryResponse.data.data) {
						console.error('接口返回数据格式异常:', subCategoryResponse.data);
						uni.showToast({
							title: '数据格式异常',
							icon: 'none'
						});
						return;
					}
			
					if (!subCategoryResponse.data.data.rows || subCategoryResponse.data.data.rows.length === 0) {
						// 尝试使用短链接跳转，避免URL过长
						console.log('=== Form页面跳转到Form页面 ===');
						console.log('跳转的产品信息:', {
							productName: item.productName,
							productCode: item.productCode,
							productManager: item.productManager
						});

						// 先尝试生成短链接
						this.generateShortLinkForProduct(item);
						return;
					}
			
					// 使用简化模式跳转到category页面，避免URL过长
					const categoryUrl = `/pages/category/category?title=${encodeURIComponent(item.productName)}&parentImage=${encodeURIComponent(item.imageUrl || this.imageUrl || '/static/熙迈LOGO.png')}&shareMode=1`;

					console.log('=== Form页面跳转到Category页面（简化模式）===');
					console.log('跳转URL:', categoryUrl);
					console.log('URL长度:', categoryUrl.length);

					uni.navigateTo({
						url: categoryUrl
					});
				} catch (error) {
					console.error('获取子分类失败:', error);
					uni.showToast({
						title: '获取子分类失败',
						icon: 'none'
					});
				}
			},

			// 为产品生成短链接并跳转
			async generateShortLinkForProduct(item) {
				console.log('=== 开始处理产品跳转 ===');
				console.log('产品信息:', item);

				// 直接使用简化模式跳转，不使用短链接
				let targetUrl;
				if (item.productCode) {
					targetUrl = `/pages/form/form?productCode=${encodeURIComponent(item.productCode)}&shareMode=1`;
				} else if (item.productName) {
					targetUrl = `/pages/form/form?productName=${encodeURIComponent(item.productName)}&shareMode=1`;
				} else {
					targetUrl = `/pages/form/form?shareMode=1`;
				}

				console.log('构建的跳转URL:', targetUrl);
				console.log('URL长度:', targetUrl.length);

				try {
					console.log('开始跳转...');
					uni.navigateTo({
						url: targetUrl,
						success: (res) => {
							console.log('跳转成功:', res);
						},
						fail: (err) => {
							console.error('跳转失败:', err);
							uni.showToast({
								title: '跳转失败',
								icon: 'none'
							});
						}
					});
				} catch (error) {
					console.error('跳转异常:', error);
					uni.showToast({
						title: '跳转异常',
						icon: 'none'
					});
				}
			},

			copyUrl(url) {
				uni.setClipboardData({
					data: url,
					success: () => {
						uni.showToast({
							title: '网址已复制',
							icon: 'none'
						})
					}
				})
			},
			openDLPPGW() {
				if (this.DLPPGW) {
					// 检查链接是否以 http 或 https 开头
					if (!this.DLPPGW.startsWith('http://') && !this.DLPPGW.startsWith('https://')) {
						this.DLPPGW = 'https://' + this.DLPPGW; // 自动补全协议
					}
					// 跳转到代理品牌官网
					uni.navigateTo({
						url: `/pages/WebView/WebView?url=${encodeURIComponent(this.DLPPGW)}`
					});
				} else {
					uni.showToast({
						title: '代理品牌官网链接为空',
						icon: 'none'
					});
				}
			},
			previewImage() {
				console.log('图片被点击了！');
				if (this.highResImageUrl) {
					uni.previewImage({
						urls: [this.highResImageUrl] // 使用高清图片URL进行预览
					});
				} else {
					console.error('图片URL为空');
				}
			},
			//拨打电话
			callnum(num) {
				uni.makePhoneCall({
					phoneNumber: num //仅为示例
				});
			},
			previewAgentImage() {
				if (this.agentBrandImage) {
					uni.previewImage({
						urls: [this.agentBrandImage]
					});
				}
			},
			// 显示资料列表
			showMaterialsList() {
				this.showMaterialsModal = true;
			},
			// 隐藏资料列表
			hideMaterialsList() {
				this.showMaterialsModal = false;
			},
			// 预览资料文件
			previewMaterial(material) {
				// 优先使用 DownloadUrl，如果失败再尝试 original_file_full_path
				const downloadUrl = material.DownloadUrl;
				const originalUrl = material.original_file_full_path;

				console.log('预览文件:', material.original_file_name);
				console.log('DownloadUrl:', downloadUrl);
				console.log('original_file_full_path:', originalUrl);

				// 先尝试 DownloadUrl
				this.previewWechatWithFallback(downloadUrl, originalUrl, material.original_file_name);
			},
			// 带回退机制的预览方法
			previewWechatWithFallback(primaryUrl, fallbackUrl, fileName) {
				console.log('尝试预览文档:', fileName);
				console.log('主要URL:', primaryUrl);
				console.log('备用URL:', fallbackUrl);

				// 先尝试主要URL
				this.previewWechatSingle(primaryUrl, (success) => {
					if (!success && fallbackUrl && fallbackUrl !== primaryUrl) {
						console.log('主要URL失败，尝试备用URL');
						this.previewWechatSingle(fallbackUrl, (success) => {
							if (!success) {
								// 两个URL都失败，显示最终错误
								uni.showModal({
									title: '预览失败',
									content: '文件无法预览，可能是文件格式不支持或网络问题。\n\n是否复制文件链接？',
									confirmText: '复制链接',
									cancelText: '取消',
									success: function(modalRes) {
										if (modalRes.confirm) {
											uni.setClipboardData({
												data: primaryUrl,
												success: function() {
													uni.showToast({
														title: '链接已复制',
														icon: 'success'
													});
												}
											});
										}
									}
								});
							}
						});
					}
				});
			},
			// 单次预览尝试
			previewWechatSingle(urlPdf, callback) {
				if (!urlPdf || typeof urlPdf !== 'string') {
					console.error('无效的文档URL:', urlPdf);
					callback(false);
					return;
				}

				uni.showLoading({
					title: '正在加载中..'
				});

				console.log('开始下载文件:', urlPdf);
				uni.downloadFile({
					url: urlPdf,
					success: function(res) {
						console.log('文件下载成功:', res);
						var filePath = res.tempFilePath;

						if (!filePath) {
							console.error('下载成功但临时文件路径为空');
							uni.hideLoading();
							callback(false);
							return;
						}

						console.log('准备打开文档:', filePath);
						uni.openDocument({
							filePath: filePath,
							showMenu: false,
							success: function(res) {
								console.log('打开文档成功:', res);
								uni.hideLoading();
								callback(true);
							},
							fail: function(err) {
								console.error('打开文档失败:', err);
								uni.hideLoading();
								callback(false);
							}
						});
					},
					fail: function(err) {
						console.error('下载文件失败:', err);
						console.error('失败的URL:', urlPdf);
						uni.hideLoading();
						callback(false);
					}
				});
			},
			// 微信小程序预览文档
			previewWechat(urlPdf) {
				console.log('准备预览文档，URL:', urlPdf);

				// 检查URL是否有效
				if (!urlPdf || typeof urlPdf !== 'string') {
					console.error('无效的文档URL:', urlPdf);
					uni.showToast({
						title: '文档链接无效',
						icon: 'none'
					});
					return;
				}

				uni.showLoading({
					title: '正在加载中..'
				});

				console.log('开始下载文件:', urlPdf);
				uni.downloadFile({
					url: urlPdf,
					success: function(res) {
						console.log('文件下载成功:', res);
						var filePath = res.tempFilePath;

						if (!filePath) {
							console.error('下载成功但临时文件路径为空');
							uni.hideLoading();
							uni.showToast({
								title: '文件路径错误',
								icon: 'none'
							});
							return;
						}

						console.log('准备打开文档:', filePath);
						uni.openDocument({
							filePath: filePath,
							showMenu: false, // 禁用菜单，防止下载
							success: function(res) {
								console.log('打开文档成功:', res);
								uni.hideLoading();
							},
							fail: function(err) {
								console.error('打开文档失败:', err);
								uni.hideLoading();
								uni.showToast({
									title: '文档格式不支持或文件损坏',
									icon: 'none'
								});
							}
						});
					},
					fail: function(err) {
						console.error('下载文件失败:', err);
						console.error('失败的URL:', urlPdf);
						uni.hideLoading();

						// 根据错误类型给出不同提示
						let errorMsg = '文件加载失败';
						if (err.errMsg && err.errMsg.includes('ENOENT')) {
							errorMsg = '文件不存在或已被删除';
						} else if (err.errMsg && err.errMsg.includes('network')) {
							errorMsg = '网络连接失败，请检查网络';
						} else if (err.errMsg && err.errMsg.includes('timeout')) {
							errorMsg = '下载超时，请重试';
						}

						uni.showModal({
							title: '预览失败',
							content: errorMsg + '\n\n是否尝试在浏览器中打开？',
							confirmText: '打开',
							cancelText: '取消',
							success: function(modalRes) {
								if (modalRes.confirm) {
									// 尝试用浏览器打开
									uni.setClipboardData({
										data: urlPdf,
										success: function() {
											uni.showToast({
												title: '链接已复制到剪贴板',
												icon: 'success'
											});
										}
									});
								}
							}
						});
					}
				});
			},
			// 获取文件图标
			getMaterialIcon(fileName) {
				const ext = fileName.split('.').pop().toLowerCase();
				switch (ext) {
					case 'pdf':
						return 'paperplane';
					case 'doc':
					case 'docx':
						return 'compose';
					case 'xls':
					case 'xlsx':
						return 'bars';
					case 'ppt':
					case 'pptx':
						return 'videocam';
					default:
						return 'folder';
				}
			},
			// 格式化文件大小
			formatFileSize(bytes) {
				if (bytes === 0) return '0 B';
				const k = 1024;
				const sizes = ['B', 'KB', 'MB', 'GB'];
				const i = Math.floor(Math.log(bytes) / Math.log(k));
				return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
			}
		}
	}
</script>

<style>
	.container {
		background-color: #f7f8fa;
		padding-bottom: 200rpx;
		/* 增加底部内边距 */
	}

	.info-card {
		background-color: #ffffff;
		border-radius: 16rpx;
		margin: 24rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;
	}

	.card-header {
		padding: 24rpx;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		border-bottom: 1rpx solid #f0f0f0;
	}

	.card-body {
		padding: 24rpx;
	}

	.form-item {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		font-size: 28rpx;
		color: #555;
	}

	.form-item:last-child {
		margin-bottom: 0;
	}

	.text {
		word-break: break-all;
	}
	
	.full-url {
		display: block;
		background-color: #f5f5f5;
		padding: 10rpx 15rpx;
		border-radius: 8rpx;
		font-size: 24rpx;
		color: #007aff;
		word-break: break-all;
		margin-top: -10rpx;
		margin-bottom: 20rpx;
	}

	.phone-link {
		color: #007aff;
		text-decoration: underline;
	}

	.link-item {
		justify-content: space-between;
		padding: 10rpx 0;
	}

	.link-text {
		color: #007aff;
		font-weight: 500;
	}

	.image,
	.brand-logo {
		width: 100%;
		height: 400rpx;
		border-radius: 12rpx;
		margin-top: 20rpx;
	}

	.brand-image {
		flex-direction: column;
		align-items: flex-start;
	}

	.brand-logo {
		height: 150rpx;
		width: 200rpx;
		max-width: 100%;
		align-self: flex-start;
		background-color: #f5f5f5;
		border: 1px solid #eee;
	}

	.debug-text {
		font-size: 24rpx;
		color: #999;
		margin: 5rpx 0;
	}

	.agent-section .card-header {
		background-color: #eef5ff;
		color: #0052cc;
	}
	
	.case-studies-section .card-header {
		background-color: #e6f7f2;
		color: #006442;
	}
	
	.case-study-item {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 16rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.case-study-item:last-child {
		border-bottom: none;
		padding-bottom: 0;
	}
	
	.case-study-client {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
	}
	
	.case-study-details {
		font-size: 26rpx;
		color: #666;
	}
	
	.category-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 0; /* 在父级.recommend-section中控制 */
	}
	
	.category-item {
		width: calc(50% - 12rpx);
		margin-bottom: 24rpx;
		background-color: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
		overflow: hidden;
		position: relative;
	}
	
	.img {
		width: 100%;
		height: 250rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;
	}
	
	.img .icon {
		height: 100%;
		width: 100%;
		display: block;
		object-fit: contain;
	}
	
	.banner {
		padding: 10rpx;
	}
	
	.banner-left {
		width: 100%;
	}
	
	.top-blue {
		height: 60rpx;
		width: 100%;
		border-radius: 15rpx;
		background-color: #6d92cc;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.english-name {
		color: #ffffff;
		font-size: 16rpx;
		text-align: center;
	}
	
	.more-text {
		margin-left: 12rpx;
	}
	
	.bottom-white {
		color: #6d92cc;
		min-height: 80rpx;
		width: 100%;
		background-color: #fff;
		display: flex;
		align-items: center;
		padding: 10rpx;
		box-sizing: border-box;
	}
	
	.bottom-white-text {
		flex: 1;
		min-width: 0;
		font-size: 22rpx;
		text-align: center;
		white-space: normal;
		line-height: 1.4;
		margin: 0 8rpx;
	}
	
	.logo {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		width: 70rpx;
		height: 70rpx;
	}
	
	.logo .icon {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
		padding: 0;
	}
	
	.like-section {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 10rpx;
		flex-shrink: 0;
	}
	
	.like-icon {
		width: 40rpx;
		height: 40rpx;
	}

	.pop-animation {
		position: absolute;
		bottom: 100%; /* 从按钮上方开始 */
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		flex-direction: column;
		animation: pop-up 0.8s ease-out forwards;
		pointer-events: none;
	}
	
	.pop-heart {
		font-size: 30rpx;
	}
	
	.pop-count {
		font-size: 24rpx;
		color: #ff6a6a;
		font-weight: bold;
	}

	@keyframes pop-up {
		0% {
			transform: translateX(-50%) scale(0.5);
			opacity: 0;
			bottom: 100%;
		}
		50% {
			transform: translateX(-50%) scale(1.2);
			opacity: 1;
		}
		100% {
			transform: translateX(-50%) scale(1);
			opacity: 0;
			bottom: 150%; /* 向上飘动 */
		}
	}

	.recommend-section {
		padding: 24rpx;
		background-color: #f7f8fa;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 24rpx;
		text-align: center;
		color: #333;
	}

	.fixed-customer-service {
		position: fixed;
		bottom: 40rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 90%;
		z-index: 10;
	}

	.service-btn {
		display: flex;
		align-items: center;
		padding: 10rpx;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		border-radius: 24rpx;
		box-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
	}

	.text-container {
		flex: 1;
	}

	.btn-text {
		color: #fff;
		font-size: 38rpx;
		font-weight: 600;
		text-align: center;
		display: block;
		margin-bottom: 0;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
	}

	.btn-subtext {
		color: rgba(255, 255, 255, 0.9);
		font-size: 24rpx;
		line-height: 1.6;
		text-align: center;
		display: block;
		opacity: 0.9;
		margin-bottom: 16rpx;
	}

	.service-btn:active {
		transform: scale(0.98) translateY(2rpx);
		box-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);
	}

	@keyframes float {
		0% {
			transform: translateY(0);
		}

		50% {
			transform: translateY(-10rpx);
		}

		100% {
			transform: translateY(0);
		}
	}

	.service-btn {
		animation: float 3s ease-in-out infinite;
	}

	.service-btn::after {
		content: '';
		position: absolute;
		top: -1rpx;
		left: -1rpx;
		right: -1rpx;
		bottom: -1rpx;
		background: linear-gradient(45deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.3) 50%,
				rgba(255, 255, 255, 0) 100%);
		border-radius: 24rpx;
		animation: shine 3s infinite;
		z-index: -1;
	}

	@keyframes shine {
		0% {
			opacity: 0;
			left: -50%;
		}

		50% {
			opacity: 0.4;
		}

		100% {
			opacity: 0;
			left: 150%;
		}
	}

	/* 介绍资料样式 */
	.materials-section {
		margin-bottom: 20rpx;
	}

	.materials-button {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-radius: 12rpx;
		background-color: #f8f9fa;
		padding: 20rpx;
	}

	.materials-text {
		flex: 1;
		margin-left: 12rpx;
		color: #2979FF;
		font-weight: 500;
	}

	/* 弹窗样式 */
	.materials-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
	}

	.materials-modal-content {
		width: 90%;
		max-height: 70%;
		background-color: white;
		border-radius: 16rpx;
		overflow: hidden;
	}

	.materials-modal-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 1px solid #eee;
	}

	.materials-modal-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
	}

	.materials-modal-close {
		padding: 10rpx;
	}

	.materials-modal-body {
		max-height: 60vh;
		overflow-y: auto;
		padding: 20rpx;
	}

	.material-item {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-radius: 12rpx;
		background-color: #f8f9fa;
		margin-bottom: 16rpx;
	}

	.material-item:last-child {
		margin-bottom: 0;
	}

	.material-icon {
		margin-right: 20rpx;
	}

	.material-info {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.material-name {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 8rpx;
		word-break: break-all;
	}

	.material-size {
		font-size: 24rpx;
		color: #999;
	}

	.material-action {
		margin-left: 20rpx;
	}
</style>