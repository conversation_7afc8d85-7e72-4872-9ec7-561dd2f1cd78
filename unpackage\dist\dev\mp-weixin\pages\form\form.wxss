
.container {
		background-color: #f7f8fa;
		padding-bottom: 200rpx;
		/* 增加底部内边距 */
}
.info-card {
		background-color: #ffffff;
		border-radius: 16rpx;
		margin: 24rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;
}
.card-header {
		padding: 24rpx;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		border-bottom: 1rpx solid #f0f0f0;
}
.card-body {
		padding: 24rpx;
}
.form-item {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		font-size: 28rpx;
		color: #555;
}
.form-item:last-child {
		margin-bottom: 0;
}
.text {
		word-break: break-all;
}
.full-url {
		display: block;
		background-color: #f5f5f5;
		padding: 10rpx 15rpx;
		border-radius: 8rpx;
		font-size: 24rpx;
		color: #007aff;
		word-break: break-all;
		margin-top: -10rpx;
		margin-bottom: 20rpx;
}
.phone-link {
		color: #007aff;
		text-decoration: underline;
}
.link-item {
		justify-content: space-between;
		padding: 10rpx 0;
}
.link-text {
		color: #007aff;
		font-weight: 500;
}
.image,
	.brand-logo {
		width: 100%;
		height: 400rpx;
		border-radius: 12rpx;
		margin-top: 20rpx;
}
.brand-image {
		flex-direction: column;
		align-items: flex-start;
}
.brand-logo {
		height: 150rpx;
		width: 200rpx;
		max-width: 100%;
		align-self: flex-start;
		background-color: #f5f5f5;
		border: 1px solid #eee;
}
.debug-text {
		font-size: 24rpx;
		color: #999;
		margin: 5rpx 0;
}
.agent-section .card-header {
		background-color: #eef5ff;
		color: #0052cc;
}
.case-studies-section .card-header {
		background-color: #e6f7f2;
		color: #006442;
}
.case-study-item {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 16rpx 0;
		border-bottom: 1rpx solid #f0f0f0;
}
.case-study-item:last-child {
		border-bottom: none;
		padding-bottom: 0;
}
.case-study-client {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
}
.case-study-details {
		font-size: 26rpx;
		color: #666;
}
.category-list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		padding: 0; /* 在父级.recommend-section中控制 */
}
.category-item {
		width: calc(50% - 12rpx);
		margin-bottom: 24rpx;
		background-color: #fff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
		overflow: hidden;
		position: relative;
}
.img {
		width: 100%;
		height: 250rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		overflow: hidden;
}
.img .icon {
		height: 100%;
		width: 100%;
		display: block;
		object-fit: contain;
}
.banner {
		padding: 10rpx;
}
.banner-left {
		width: 100%;
}
.top-blue {
		height: 60rpx;
		width: 100%;
		border-radius: 15rpx;
		background-color: #6d92cc;
		display: flex;
		align-items: center;
		justify-content: center;
}
.english-name {
		color: #ffffff;
		font-size: 16rpx;
		text-align: center;
}
.more-text {
		margin-left: 12rpx;
}
.bottom-white {
		color: #6d92cc;
		min-height: 80rpx;
		width: 100%;
		background-color: #fff;
		display: flex;
		align-items: center;
		padding: 10rpx;
		box-sizing: border-box;
}
.bottom-white-text {
		flex: 1;
		min-width: 0;
		font-size: 22rpx;
		text-align: center;
		white-space: normal;
		line-height: 1.4;
		margin: 0 8rpx;
}
.logo {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
		width: 70rpx;
		height: 70rpx;
}
.logo .icon {
		max-width: 100%;
		max-height: 100%;
		object-fit: contain;
		padding: 0;
}
.like-section {
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 10rpx;
		flex-shrink: 0;
}
.like-icon {
		width: 40rpx;
		height: 40rpx;
}
.pop-animation {
		position: absolute;
		bottom: 100%; /* 从按钮上方开始 */
		left: 50%;
		transform: translateX(-50%);
		display: flex;
		align-items: center;
		flex-direction: column;
		animation: pop-up 0.8s ease-out forwards;
		pointer-events: none;
}
.pop-heart {
		font-size: 30rpx;
}
.pop-count {
		font-size: 24rpx;
		color: #ff6a6a;
		font-weight: bold;
}
@keyframes pop-up {
0% {
			transform: translateX(-50%) scale(0.5);
			opacity: 0;
			bottom: 100%;
}
50% {
			transform: translateX(-50%) scale(1.2);
			opacity: 1;
}
100% {
			transform: translateX(-50%) scale(1);
			opacity: 0;
			bottom: 150%; /* 向上飘动 */
}
}
.recommend-section {
		padding: 24rpx;
		background-color: #f7f8fa;
}
.section-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 24rpx;
		text-align: center;
		color: #333;
}
.fixed-customer-service {
		position: fixed;
		bottom: 40rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 90%;
		z-index: 10;
}
.service-btn {
		display: flex;
		align-items: center;
		padding: 10rpx;
		background: linear-gradient(135deg, #2979FF, #00B4FF);
		border-radius: 24rpx;
		box-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);
		transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
		-webkit-backdrop-filter: blur(10px);
		        backdrop-filter: blur(10px);
		border: 1rpx solid rgba(255, 255, 255, 0.1);
}
.text-container {
		flex: 1;
}
.btn-text {
		color: #fff;
		font-size: 38rpx;
		font-weight: 600;
		text-align: center;
		display: block;
		margin-bottom: 0;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}
.btn-subtext {
		color: rgba(255, 255, 255, 0.9);
		font-size: 24rpx;
		line-height: 1.6;
		text-align: center;
		display: block;
		opacity: 0.9;
		margin-bottom: 16rpx;
}
.service-btn:active {
		transform: scale(0.98) translateY(2rpx);
		box-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);
}
@keyframes float {
0% {
			transform: translateY(0);
}
50% {
			transform: translateY(-10rpx);
}
100% {
			transform: translateY(0);
}
}
.service-btn {
		animation: float 3s ease-in-out infinite;
}
.service-btn::after {
		content: '';
		position: absolute;
		top: -1rpx;
		left: -1rpx;
		right: -1rpx;
		bottom: -1rpx;
		background: linear-gradient(45deg,
				rgba(255, 255, 255, 0) 0%,
				rgba(255, 255, 255, 0.3) 50%,
				rgba(255, 255, 255, 0) 100%);
		border-radius: 24rpx;
		animation: shine 3s infinite;
		z-index: -1;
}
@keyframes shine {
0% {
			opacity: 0;
			left: -50%;
}
50% {
			opacity: 0.4;
}
100% {
			opacity: 0;
			left: 150%;
}
}

	/* 介绍资料样式 */
.materials-section {
		margin-bottom: 20rpx;
}
.materials-button {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-radius: 12rpx;
		background-color: #f8f9fa;
		padding: 20rpx;
}
.materials-text {
		flex: 1;
		margin-left: 12rpx;
		color: #2979FF;
		font-weight: 500;
}

	/* 弹窗样式 */
.materials-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 1000;
}
.materials-modal-content {
		width: 90%;
		max-height: 70%;
		background-color: white;
		border-radius: 16rpx;
		overflow: hidden;
}
.materials-modal-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 1px solid #eee;
}
.materials-modal-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #333;
}
.materials-modal-close {
		padding: 10rpx;
}
.materials-modal-body {
		max-height: 60vh;
		overflow-y: auto;
		padding: 20rpx;
}
.material-item {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-radius: 12rpx;
		background-color: #f8f9fa;
		margin-bottom: 16rpx;
}
.material-item:last-child {
		margin-bottom: 0;
}
.material-icon {
		margin-right: 20rpx;
}
.material-info {
		flex: 1;
		display: flex;
		flex-direction: column;
}
.material-name {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 8rpx;
		word-break: break-all;
}
.material-size {
		font-size: 24rpx;
		color: #999;
}
.material-action {
		margin-left: 20rpx;
}
