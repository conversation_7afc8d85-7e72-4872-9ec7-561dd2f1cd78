
.container {
		width: 100%;
		height: 100vh;
		position: relative;
}
.loading-container {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		z-index: 1000;
}
.loading-spinner {
		width: 40rpx;
		height: 40rpx;
		border: 3rpx solid #f3f3f3;
		border-top: 3rpx solid #2979FF;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
}
.loading-text {
		font-size: 28rpx;
		color: #666;
}
@keyframes spin {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
