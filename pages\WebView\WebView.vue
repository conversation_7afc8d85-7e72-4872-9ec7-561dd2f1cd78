<template>
	<view class="container">
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-spinner"></view>
			<text class="loading-text">正在加载...</text>
		</view>

		<!-- Web-view 组件 -->
		<web-view
			:src="url"
			@message="getMessage"
			@load="onWebViewLoad"
			@error="onWebViewError"
		></web-view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				url: '',
				loading: true,
				title: '相关链接'
			}
		},
		onLoad(options) {
			console.log('WebView页面参数:', options);

			// 获取URL参数
			this.url = decodeURIComponent(options.url || '');
			this.title = decodeURIComponent(options.title || '相关链接');

			if (!this.url) {
				uni.showModal({
					title: '错误',
					content: '缺少链接地址',
					showCancel: false,
					confirmText: '返回',
					success: () => {
						uni.navigateBack();
					}
				});
				return;
			}

			// 设置导航栏标题
			uni.setNavigationBarTitle({
				title: this.title
			});

			console.log('即将加载链接:', this.url);
		},
		methods: {
			// Web-view 加载完成
			onWebViewLoad(e) {
				console.log('Web-view 加载完成:', e);
				this.loading = false;
			},

			// Web-view 加载错误
			onWebViewError(e) {
				console.error('Web-view 加载错误:', e);
				this.loading = false;

				uni.showModal({
					title: '加载失败',
					content: '页面加载失败，是否复制链接到剪贴板？',
					confirmText: '复制链接',
					cancelText: '返回',
					success: (res) => {
						if (res.confirm) {
							uni.setClipboardData({
								data: this.url,
								success: () => {
									uni.showToast({
										title: '链接已复制',
										icon: 'success'
									});
									setTimeout(() => {
										uni.navigateBack();
									}, 1500);
								}
							});
						} else {
							uni.navigateBack();
						}
					}
				});
			},

			// 接收 Web-view 消息
			getMessage(e) {
				console.log('收到 Web-view 消息:', e);
			}
		}
	}
</script>

<style>
	.container {
		width: 100%;
		height: 100vh;
		position: relative;
	}

	.loading-container {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #fff;
		z-index: 1000;
	}

	.loading-spinner {
		width: 40rpx;
		height: 40rpx;
		border: 3rpx solid #f3f3f3;
		border-top: 3rpx solid #2979FF;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-bottom: 20rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #666;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
</style>
