{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\r\n\timport ShareUtils from '@/utils/share.js';\r\n\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch');\r\n\r\n\t\t\t// 设置全局分享配置\r\n\t\t\tthis.globalData.shareConfig = {\r\n\t\t\t\ttitle: '熙迈科技服务有限公司 - 专业工业服务',\r\n\t\t\t\tpath: 'pages/home/<USER>',\r\n\t\t\t\timageUrl: '/static/熙迈LOGO.png'\r\n\t\t\t};\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\r\n\t\tglobalData: {\r\n\t\t\tshareConfig: {}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.fixed-customer-service {\r\n\t\tposition: fixed;\r\n\t\tbottom: 40rpx;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tz-index: 999;\r\n\t\twidth: 700rpx;\r\n\t}\r\n\t\r\n\t.service-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 10rpx;\r\n\t\tbackground: linear-gradient(135deg, #2979FF, #00B4FF);\r\n\t\tborder-radius: 24rpx;\r\n\t\tbox-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);\r\n\t\ttransition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.1);\r\n\t}\r\n\t\r\n\t.text-container {\r\n\t\tflex: 1;\r\n\t}\r\n\t\r\n\t.btn-text {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 38rpx;\r\n\t\tfont-weight: 600;\r\n\t\ttext-align: center;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 0;\r\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\t\r\n\t.btn-subtext {\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 1.6;\r\n\t\ttext-align: center;\r\n\t\tdisplay: block;\r\n\t\topacity: 0.9;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\t\r\n\t/* 交互动画 */\r\n\t.service-btn:active {\r\n\t\ttransform: scale(0.98) translateY(2rpx);\r\n\t\tbox-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);\r\n\t}\r\n\t\r\n\t\r\n\t/* 悬浮呼吸动画 */\r\n\t@keyframes float {\r\n\t\t0% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t\r\n\t\t50% {\r\n\t\t\ttransform: translateY(-10rpx);\r\n\t\t}\r\n\t\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.service-btn {\r\n\t\tanimation: float 3s ease-in-out infinite;\r\n\t}\r\n\t\r\n\t/* 流光边框效果 */\r\n\t.service-btn::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: -1rpx;\r\n\t\tleft: -1rpx;\r\n\t\tright: -1rpx;\r\n\t\tbottom: -1rpx;\r\n\t\tbackground: linear-gradient(45deg,\r\n\t\t\t\trgba(255, 255, 255, 0) 0%,\r\n\t\t\t\trgba(255, 255, 255, 0.3) 50%,\r\n\t\t\t\trgba(255, 255, 255, 0) 100%);\r\n\t\tborder-radius: 24rpx;\r\n\t\tanimation: shine 3s infinite;\r\n\t\tz-index: -1;\r\n\t}\r\n\t\r\n\t@keyframes shine {\r\n\t\t0% {\r\n\t\t\topacity: 0;\r\n\t\t\tleft: -50%;\r\n\t\t}\r\n\t\r\n\t\t50% {\r\n\t\t\topacity: 0.4;\r\n\t\t}\r\n\t\r\n\t\t100% {\r\n\t\t\topacity: 0;\r\n\t\t\tleft: 150%;\r\n\t\t}\r\n\t}\r\n\t.service-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 10rpx;\r\n\t\tbackground: linear-gradient(135deg, #2979FF, #00B4FF);\r\n\t\tborder-radius: 24rpx;\r\n\t\tbox-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);\r\n\t\ttransition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.1);\r\n\t}\r\n\t\r\n\t.text-container {\r\n\t\tflex: 1;\r\n\t}\r\n\t\r\n\t.btn-text {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 38rpx;\r\n\t\tfont-weight: 600;\r\n\t\ttext-align: center;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 0;\r\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\t\r\n\t.btn-subtext {\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 1.6;\r\n\t\ttext-align: center;\r\n\t\tdisplay: block;\r\n\t\topacity: 0.9;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\t\r\n\t/* 交互动画 */\r\n\t.service-btn:active {\r\n\t\ttransform: scale(0.98) translateY(2rpx);\r\n\t\tbox-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);\r\n\t}\r\n\t\r\n\t\r\n\t/* 悬浮呼吸动画 */\r\n\t@keyframes float {\r\n\t\t0% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t\r\n\t\t50% {\r\n\t\t\ttransform: translateY(-10rpx);\r\n\t\t}\r\n\t\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\t\r\n\t.service-btn {\r\n\t\tanimation: float 3s ease-in-out infinite;\r\n\t}\r\n\t\r\n\t/* 流光边框效果 */\r\n\t.service-btn::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: -1rpx;\r\n\t\tleft: -1rpx;\r\n\t\tright: -1rpx;\r\n\t\tbottom: -1rpx;\r\n\t\tbackground: linear-gradient(45deg,\r\n\t\t\t\trgba(255, 255, 255, 0) 0%,\r\n\t\t\t\trgba(255, 255, 255, 0.3) 50%,\r\n\t\t\t\trgba(255, 255, 255, 0) 100%);\r\n\t\tborder-radius: 24rpx;\r\n\t\tanimation: shine 3s infinite;\r\n\t\tz-index: -1;\r\n\t}\r\n\t\r\n\t@keyframes shine {\r\n\t\t0% {\r\n\t\t\topacity: 0;\r\n\t\t\tleft: -50%;\r\n\t\t}\r\n\t\r\n\t\t50% {\r\n\t\t\topacity: 0.4;\r\n\t\t}\r\n\t\r\n\t\t100% {\r\n\t\t\topacity: 0;\r\n\t\t\tleft: 150%;\r\n\t\t}\r\n\t}\r\n\r\n</style>\r\n", "import App from './App'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\nexport function createApp() {\n  const app = createSSRApp(App)\n  return {\n    app\n  }\n}\n// #endif"], "names": ["uni", "createSSRApp", "App"], "mappings": ";;;;;;;;;;;AAGC,MAAK,YAAU;AAAA,EACd,UAAU,WAAW;AACpBA,kBAAAA,MAAA,MAAA,OAAA,gBAAY,YAAY;AAGxB,SAAK,WAAW,cAAc;AAAA,MAC7B,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA;EAEX;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACtB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACtB;AAAA,EAED,YAAY;AAAA,IACX,aAAa,CAAC;AAAA,EACf;AACD;ACTM,SAAS,YAAY;AAC1B,QAAM,MAAMC,cAAY,aAACC,SAAG;AAC5B,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}