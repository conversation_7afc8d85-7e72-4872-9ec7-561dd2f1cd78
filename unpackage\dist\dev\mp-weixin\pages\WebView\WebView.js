"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  data() {
    return {
      url: "",
      loading: true,
      title: "相关链接"
    };
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/WebView/WebView.vue:29", "WebView页面参数:", options);
    this.url = decodeURIComponent(options.url || "");
    this.title = decodeURIComponent(options.title || "相关链接");
    if (!this.url) {
      common_vendor.index.showModal({
        title: "错误",
        content: "缺少链接地址",
        showCancel: false,
        confirmText: "返回",
        success: () => {
          common_vendor.index.navigateBack();
        }
      });
      return;
    }
    common_vendor.index.setNavigationBarTitle({
      title: this.title
    });
    common_vendor.index.__f__("log", "at pages/WebView/WebView.vue:53", "即将加载链接:", this.url);
  },
  methods: {
    // Web-view 加载完成
    onWebViewLoad(e) {
      common_vendor.index.__f__("log", "at pages/WebView/WebView.vue:58", "Web-view 加载完成:", e);
      this.loading = false;
    },
    // Web-view 加载错误
    onWebViewError(e) {
      common_vendor.index.__f__("error", "at pages/WebView/WebView.vue:64", "Web-view 加载错误:", e);
      this.loading = false;
      common_vendor.index.showModal({
        title: "加载失败",
        content: "页面加载失败，是否复制链接到剪贴板？",
        confirmText: "复制链接",
        cancelText: "返回",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.setClipboardData({
              data: this.url,
              success: () => {
                common_vendor.index.showToast({
                  title: "链接已复制",
                  icon: "success"
                });
                setTimeout(() => {
                  common_vendor.index.navigateBack();
                }, 1500);
              }
            });
          } else {
            common_vendor.index.navigateBack();
          }
        }
      });
    },
    // 接收 Web-view 消息
    getMessage(e) {
      common_vendor.index.__f__("log", "at pages/WebView/WebView.vue:95", "收到 Web-view 消息:", e);
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.loading
  }, $data.loading ? {} : {}, {
    b: $data.url,
    c: common_vendor.o((...args) => $options.getMessage && $options.getMessage(...args)),
    d: common_vendor.o((...args) => $options.onWebViewLoad && $options.onWebViewLoad(...args)),
    e: common_vendor.o((...args) => $options.onWebViewError && $options.onWebViewError(...args))
  });
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/WebView/WebView.js.map
