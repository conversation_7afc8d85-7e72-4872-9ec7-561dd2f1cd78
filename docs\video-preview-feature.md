# 视频预览功能实现文档

## 功能概述

为微信小程序的相关资料预览功能添加了视频文件（mp4等格式）的预览支持。现在用户可以直接在小程序内预览视频文件，而不需要下载或跳转到外部应用。

## 实现的功能

### 1. 文件类型检测
- 新增 `getFileType()` 方法，可以识别以下文件类型：
  - 视频文件：mp4, avi, mov, wmv, flv, webm, mkv, ogg
  - 图片文件：jpg, jpeg, png, gif, bmp, webp
  - PDF文件：pdf
  - 文档文件：doc, docx, xls, xlsx, ppt, pptx

### 2. 视频预览功能
- 新增 `previewVideo()` 方法，使用微信小程序的 video 组件
- 支持视频播放控制（播放、暂停、全屏等）
- 自动播放功能
- 响应式视频播放器

### 3. 图片预览功能
- 新增 `previewImage()` 方法，使用微信小程序原生的图片预览功能
- 支持图片缩放、滑动等操作

### 4. 视频预览弹窗
- 全屏黑色背景的视频预览弹窗
- 显示视频文件名
- 可关闭的弹窗设计
- 响应式布局，适配不同屏幕尺寸

## 修改的文件

### 1. pages/category/category.vue
- 添加视频预览相关的数据字段
- 修改 `previewMaterial()` 方法，根据文件类型选择预览方式
- 新增文件类型检测和预览方法
- 添加视频预览弹窗模板和样式

### 2. pages/form/form.vue
- 与 category 页面相同的修改
- 保持两个页面功能一致性

## 新增的方法

### getFileType(fileName)
```javascript
// 根据文件扩展名判断文件类型
getFileType(fileName) {
    const ext = fileName.split('.').pop().toLowerCase();
    
    // 视频类型
    const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'ogg'];
    if (videoTypes.includes(ext)) return 'video';
    
    // 图片类型
    const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    if (imageTypes.includes(ext)) return 'image';
    
    // PDF类型
    if (ext === 'pdf') return 'pdf';
    
    // 文档类型
    const docTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
    if (docTypes.includes(ext)) return 'document';
    
    return 'unknown';
}
```

### previewVideo(videoUrl, fileName)
```javascript
// 预览视频文件
previewVideo(videoUrl, fileName) {
    console.log('预览视频:', fileName);
    console.log('视频URL:', videoUrl);
    
    // 显示视频预览弹窗
    this.showVideoModal = true;
    this.currentVideoUrl = videoUrl;
    this.currentVideoName = fileName;
}
```

### previewImage(imageUrl, fileName)
```javascript
// 预览图片文件
previewImage(imageUrl, fileName) {
    console.log('预览图片:', fileName);
    console.log('图片URL:', imageUrl);
    
    // 使用微信小程序的图片预览功能
    uni.previewImage({
        urls: [imageUrl],
        current: imageUrl,
        success: function() {
            console.log('图片预览成功');
        },
        fail: function(err) {
            console.error('图片预览失败:', err);
            uni.showToast({
                title: '图片预览失败',
                icon: 'none'
            });
        }
    });
}
```

## 新增的数据字段

```javascript
data() {
    return {
        // ... 其他字段
        showVideoModal: false, // 控制视频预览弹窗显示
        currentVideoUrl: '', // 当前预览的视频URL
        currentVideoName: '' // 当前预览的视频文件名
    }
}
```

## 图标更新

更新了 `getMaterialIcon()` 方法，为不同文件类型添加了相应的图标：
- 视频文件：`videocam-filled`
- 图片文件：`image`
- 保持原有文档类型图标不变

## 使用方式

1. 用户点击相关资料中的视频文件
2. 系统自动检测文件类型
3. 如果是视频文件，显示视频预览弹窗
4. 用户可以在弹窗中播放视频
5. 点击关闭按钮或弹窗背景关闭预览

## 兼容性

- 支持微信小程序原生 video 组件的所有功能
- 兼容现有的文档预览功能
- 不影响原有的PDF、Word等文档预览流程

## 注意事项

1. 视频文件需要是微信小程序支持的格式
2. 视频URL需要是HTTPS协议
3. 大视频文件可能需要较长的加载时间
4. 建议视频文件大小控制在合理范围内以确保良好的用户体验
