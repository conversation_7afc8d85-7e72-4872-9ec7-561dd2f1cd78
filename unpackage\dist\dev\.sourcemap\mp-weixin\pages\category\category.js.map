{"version": 3, "file": "category.js", "sources": ["pages/category/category.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2F0ZWdvcnkvY2F0ZWdvcnkudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 父级分类介绍资料 -->\r\n\t\t<view v-if=\"currentMaterials.length > 0\" class=\"materials-card\">\r\n\t\t\t<view class=\"materials-link-button\" @tap=\"showParentMaterialsList\">\r\n\t\t\t\t<uni-icons type=\"paperplane\" size=\"18\" color=\"#007aff\"></uni-icons>\r\n\t\t\t\t<text class=\"materials-link-text\">查看相关资料 ({{currentMaterials.length}}个文件)</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 父级分类相关链接 -->\r\n\t\t<view v-if=\"currentRelatedLinks.length > 0\" class=\"links-card\">\r\n\t\t\t<view class=\"links-header\">\r\n\t\t\t\t<uni-icons type=\"link\" size=\"18\" color=\"#007aff\"></uni-icons>\r\n\t\t\t\t<text class=\"links-title\">相关视频号/公众号 ({{currentRelatedLinks.length}}个链接)</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"links-list\">\r\n\t\t\t\t<view v-for=\"(link, index) in currentRelatedLinks\" :key=\"index\"\r\n\t\t\t\t\t  class=\"link-item\" @tap=\"openRelatedLink(link)\">\r\n\t\t\t\t\t<uni-icons type=\"paperplane-filled\" size=\"14\" color=\"#007aff\"></uni-icons>\r\n\t\t\t\t\t<text class=\"link-text\">{{ getLinkDisplayText(link) }}</text>\r\n\t\t\t\t\t<uni-icons type=\"right\" size=\"10\" color=\"#999\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 产品经理和联系方式 -->\r\n\t\t<view v-if=\"parentProductManager || parentContactPhone\" class=\"contact-card\">\r\n\t\t\t<view v-if=\"parentProductManager\" class=\"contact-item\">\r\n\t\t\t\t<view class=\"contact-icon\">\r\n\t\t\t\t\t<uni-icons type=\"person\" size=\"14\" color=\"#666\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"contact-content-inline\">\r\n\t\t\t\t\t<text class=\"contact-label\">产品经理: </text>\r\n\t\t\t\t\t<text class=\"contact-text\">{{parentProductManager}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"parentContactPhone\" class=\"contact-item\">\r\n\t\t\t\t<view class=\"contact-icon\">\r\n\t\t\t\t\t<uni-icons type=\"phone\" size=\"14\" color=\"#666\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"contact-content-inline\">\r\n\t\t\t\t\t<text class=\"contact-label\">联系方式: </text>\r\n\t\t\t\t\t<text class=\"phone-link\" @click=\"callnum(parentContactPhone)\">{{parentContactPhone}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 分类列表 -->\r\n\t\t<view class=\"category-list\">\r\n\t\t\t<view v-for=\"(item, index) in categories\" :key=\"index\" class=\"category-item\"\r\n\t\t\t\t@click=\"handleCategoryClick(item)\">\r\n\t\t\t\t<view class=\"img\">\r\n\t\t\t\t\t<image class=\"icon\" :src=\"item.imageUrl || '/static/熙迈LOGO.png'\" mode=\"heightFix\"></image>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- <text class=\"name\">{{item.name}}</text> -->\r\n\t\t\t\t<view class=\"banner\">\r\n\t\t\t\t\t<view class=\"banner-left\">\r\n\t\t\t\t\t\t<view class=\"top-blue\">\r\n\t\t\t\t\t\t\t<text class=\"english-name-content\">\r\n\t\t\t\t\t\t\t\t<text v-if=\"item.englishName\">{{ item.englishName }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"more-text\">更多>></text>\r\n\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bottom-white\">\r\n\t\t\t\t\t\t\t<view class=\"logo\">\r\n\t\t\t\t\t\t\t\t<image v-if=\"item.YWLX === '代理'\" class=\"icon\"\r\n\t\t\t\t\t\t\t\t\t:src=\"'/static/代理图标.png'\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t\t<image v-else class=\"icon\"\r\n\t\t\t\t\t\t\t\t\t:src=\"'/static/熙迈LOGO.png'\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom-white-text\">{{item.productName}}</view>\r\n\t\t\t\t\t\t\t<view class=\"like-section\" @click.stop=\"handleLike(item)\">\r\n\t\t\t\t\t\t\t\t<image class=\"like-icon\" :src=\"item.isLiked ? '/static/红色小红心.svg' : '/static/灰色小红心.svg'\"></image>\r\n\t\t\t\t\t\t\t\t<view v-if=\"item.showHeart\" class=\"pop-animation\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"pop-heart\">❤️</text>\r\n\t\t\t\t\t\t\t\t\t<text class=\"pop-count\">{{ item.likeCount }}</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view v-if=\"recommendations.length > 0\" class=\"recommend-section\">\r\n\t\t  <view class=\"section-title\">您可能对以下产品感兴趣</view>\r\n\t\t  <view class=\"category-list\">\r\n\t\t\t\t<view v-for=\"(item, index) in recommendations\" :key=\"index\" class=\"category-item\"\r\n\t\t\t\t\t@click=\"handleCategoryClick(item)\">\r\n\t\t\t\t\t<view class=\"img\">\r\n\t\t\t\t\t\t<image class=\"icon\" :src=\"item.imageUrl || '/static/熙迈LOGO.png'\" mode=\"heightFix\"></image>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"banner\">\r\n\t\t\t\t\t\t<view class=\"banner-left\">\r\n\t\t\t\t\t\t\t<view class=\"top-blue\">\r\n\t\t\t\t\t\t\t\t<text class=\"english-name\">\r\n\t\t\t\t\t\t\t\t\t{{ item.englishName }}\r\n\t\t\t\t\t\t\t\t\t<span style=\"display:inline-block;width:16rpx;\"></span>\r\n\t\t\t\t\t\t\t\t\t<text class=\"more-text\" style=\"margin-left: 12rpx;\">更多&gt;&gt;</text>\r\n\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom-white\">\r\n\t\t\t\t\t\t\t\t<view class=\"logo\">\r\n\t\t\t\t\t\t\t\t\t<image v-if=\"item.YWLX === '代理'\" class=\"icon\"\r\n\t\t\t\t\t\t\t\t\t\t:src=\"'/static/代理图标.png'\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t\t\t<image v-else class=\"icon\"\r\n\t\t\t\t\t\t\t\t\t\t:src=\"'/static/熙迈LOGO.png'\" mode=\"aspectFit\"></image>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<view class=\"bottom-white-text\">{{item.productName}}</view>\r\n\t\t\t\t\t\t\t\t<view class=\"like-section\" @click.stop=\"handleLike(item)\">\r\n\t\t\t\t\t\t\t\t\t<image class=\"like-icon\" :src=\"item.isLiked ? '/static/红色小红心.svg' : '/static/灰色小红心.svg'\"></image>\r\n\t\t\t\t\t\t\t\t\t<view v-if=\"item.showHeart\" class=\"pop-animation\">\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"pop-heart\">❤️</text>\r\n\t\t\t\t\t\t\t\t\t\t<text class=\"pop-count\">{{ item.likeCount }}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t  </view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"fixed-customer-service\">\r\n\t\t\t<button class=\"service-btn\"  @click=\"handleContact(this.pm)\">\r\n\t\t\t\t<view class=\"text-container\">\r\n\t\t\t\t\t<text class=\"btn-text\">微信客服</text>\r\n\t\t\t\t\t<text class=\"btn-subtext\">如有需求，请点我联系</text>\r\n\t\t\t\t</view>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\r\n\t\t<!-- 无结果提示 -->\r\n\t\t<view v-if=\"categories.length === 0\" class=\"no-result\">\r\n\t\t\t<text class=\"no-result-icon\">&#9785;</text>\r\n\t\t\t<text class=\"no-result-text\">没有找到相关产品</text>\r\n\t\t\t<text class=\"no-result-subtext\">尝试更改搜索关键词或稍后再试</text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 介绍资料弹窗 -->\r\n\t\t<view v-if=\"showMaterialsModal\" class=\"materials-modal\" @tap=\"hideMaterialsList\">\r\n\t\t\t<view class=\"materials-modal-content\" @tap.stop>\r\n\t\t\t\t<view class=\"materials-modal-header\">\r\n\t\t\t\t\t<text class=\"materials-modal-title\">相关资料</text>\r\n\t\t\t\t\t<view class=\"materials-modal-close\" @tap=\"hideMaterialsList\">\r\n\t\t\t\t\t\t<uni-icons type=\"close\" size=\"20\" color=\"#666\"></uni-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"materials-modal-body\">\r\n\t\t\t\t\t<view v-for=\"(material, index) in currentMaterials\" :key=\"index\"\r\n\t\t\t\t\t\t  class=\"material-item\" @tap=\"previewMaterial(material)\">\r\n\t\t\t\t\t\t<view class=\"material-icon\">\r\n\t\t\t\t\t\t\t<uni-icons :type=\"getMaterialIcon(material.original_file_name)\" size=\"24\" color=\"#2979FF\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"material-info\">\r\n\t\t\t\t\t\t\t<text class=\"material-name\">{{ material.original_file_name }}</text>\r\n\t\t\t\t\t\t\t<text class=\"material-size\">{{ formatFileSize(material.file_size) }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"material-action\">\r\n\t\t\t\t\t\t\t<uni-icons type=\"eye\" size=\"16\" color=\"#999\"></uni-icons>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 视频预览弹窗 -->\r\n\t\t<view v-if=\"showVideoModal\" class=\"video-modal\" @tap=\"closeVideoModal\">\r\n\t\t\t<view class=\"video-modal-content\" @tap.stop>\r\n\t\t\t\t<view class=\"video-modal-header\">\r\n\t\t\t\t\t<text class=\"video-modal-title\">{{ currentVideoName }}</text>\r\n\t\t\t\t\t<view class=\"video-modal-close\" @tap=\"closeVideoModal\">\r\n\t\t\t\t\t\t<uni-icons type=\"close\" size=\"20\" color=\"#fff\"></uni-icons>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"video-modal-body\">\r\n\t\t\t\t\t<video\r\n\t\t\t\t\t\t:src=\"currentVideoUrl\"\r\n\t\t\t\t\t\tcontrols\r\n\t\t\t\t\t\tautoplay\r\n\t\t\t\t\t\t:show-fullscreen-btn=\"true\"\r\n\t\t\t\t\t\t:show-play-btn=\"true\"\r\n\t\t\t\t\t\t:show-center-play-btn=\"true\"\r\n\t\t\t\t\t\tclass=\"video-player\"\r\n\t\t\t\t\t></video>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t\timport {\r\n\t\t\tFIELD_MAPPING,\r\n\t\t\tPRODUCT_MANAGER_SERVICE_LINKS\r\n\t\t} from '@/config/fields.js';\r\n\timport {\r\n\t\tobjectToParams\r\n\t} from '@/utils/url.js';\r\n\timport ShareUtils from '@/utils/share.js';\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tsearchKeyword: '',\r\n\t\t\t\tcategories: [],\r\n\t\t\t\ttitle: '',\r\n\t\t\t\tpm: '',\r\n\t\t\t\tparentImage: '',\r\n\t\t\t\tchildren: [],\r\n\t\t\t\trecommendations: [],\r\n\t\t\t\tlikesLog: {},\r\n\t\t\t\tshowMaterialsModal: false, // 控制资料弹窗显示\r\n\t\t\t\tcurrentMaterials: [], // 当前查看的资料列表\r\n\t\t\t\tcurrentRelatedLinks: [], // 当前查看的相关链接列表\r\n\t\t\t\tparentProductManager: '', // 父级产品经理\r\n\t\t\t\tparentContactPhone: '', // 父级联系电话\r\n\t\t\t\tshowVideoModal: false, // 控制视频预览弹窗显示\r\n\t\t\t\tcurrentVideoUrl: '', // 当前预览的视频URL\r\n\t\t\t\tcurrentVideoName: '' // 当前预览的视频文件名\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonLoad(options) {\r\n\t\t\tconsole.log('=== Category页面 onLoad 开始 ===');\r\n\t\t\tconsole.log('接收到的URL参数:', options);\r\n\r\n\t\t\tthis.title = decodeURIComponent(options.title || '');\r\n\t\t\tthis.pm = decodeURIComponent(options.pm || '');\r\n\t\t\tthis.parentImage = decodeURIComponent(options.parentImage || '/static/熙迈LOGO.png');\r\n\r\n\t\t\t// 设置导航栏标题为上级分类名称\r\n\t\t\tif (this.title) {\r\n\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\ttitle: this.title\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\t// 解析父级产品经理和联系电话\r\n\t\t\tthis.parentProductManager = decodeURIComponent(options.pm || '');\r\n\t\t\tlet contactPhone = decodeURIComponent(options.parentContactPhone || '');\r\n\t\t\t// 去掉 +86 前缀\r\n\t\t\tif (contactPhone.startsWith('+86')) {\r\n\t\t\t\tcontactPhone = contactPhone.substring(3);\r\n\t\t\t}\r\n\t\t\tthis.parentContactPhone = contactPhone;\r\n\r\n\t\t\tconsole.log('解析后的基本信息:', {\r\n\t\t\t\ttitle: this.title,\r\n\t\t\t\tpm: this.pm,\r\n\t\t\t\tparentImage: this.parentImage,\r\n\t\t\t\tshareMode: options.shareMode,\r\n\t\t\t\tparentId: options.parentId\r\n\t\t\t});\r\n\r\n\t\t\t// 检查是否是分享模式或者有parentId参数\r\n\t\t\tif (options.shareMode === '1' || options.parentId) {\r\n\t\t\t\t// 分享模式或parentId模式：需要重新获取数据\r\n\t\t\t\tconsole.log('🔄 检测到分享模式或parentId模式，开始重新获取数据');\r\n\r\n\t\t\t\t// 如果有 parentMaterials 参数，先解析它\r\n\t\t\t\tif (options.parentMaterials) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tthis.currentMaterials = JSON.parse(decodeURIComponent(options.parentMaterials || '[]'));\r\n\t\t\t\t\t\tconsole.log('✅ 从URL参数解析父级相关资料，数量:', this.currentMaterials.length);\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.error('❌ 解析URL参数中的父级相关资料失败:', error);\r\n\t\t\t\t\t\tthis.currentMaterials = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 如果有 parentRelatedLinks 参数，先解析它\r\n\t\t\t\tif (options.parentRelatedLinks) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tconst relatedLinksStr = decodeURIComponent(options.parentRelatedLinks || '');\r\n\t\t\t\t\t\tif (relatedLinksStr) {\r\n\t\t\t\t\t\t\t// 用逗号分隔链接，并过滤空字符串\r\n\t\t\t\t\t\t\tthis.currentRelatedLinks = relatedLinksStr.split(',')\r\n\t\t\t\t\t\t\t\t.map(link => link.trim())\r\n\t\t\t\t\t\t\t\t.filter(link => link.length > 0);\r\n\t\t\t\t\t\t\tconsole.log('✅ 从URL参数解析父级相关链接，数量:', this.currentRelatedLinks.length);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tthis.currentRelatedLinks = [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.error('❌ 解析URL参数中的父级相关链接失败:', error);\r\n\t\t\t\t\t\tthis.currentRelatedLinks = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (options.parentId) {\r\n\t\t\t\t\t// 如果有parentId，直接通过parentId加载子分类\r\n\t\t\t\t\tthis.loadDataByParentId(options.parentId);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 否则通过title搜索\r\n\t\t\t\t\tthis.loadDataFromAPI();\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log('📝 正常模式，解析URL参数中的数据');\r\n\t\t\t\t// 正常模式：从URL参数解析数据\r\n\t\t\t\t// 解析 children 数据\r\n\t\t\t\ttry {\r\n\t\t\t\t\tthis.children = JSON.parse(decodeURIComponent(options.children || '[]'));\r\n\t\t\t\t\tconsole.log('✅ 成功解析 children 数据，数量:', this.children.length);\r\n\r\n\t\t\t\t\t// 将 children 数据赋值给 categories\r\n\t\t\t\t\tthis.categories = this.children;\r\n\t\t\t\t\tthis.categories.sort((a, b) => b.likeCount - a.likeCount);\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('❌ 解析 children 数据失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '数据解析失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\ttry {\r\n\t\t\t\t\tthis.recommendations = JSON.parse(decodeURIComponent(options.recommendations || '[]'))\r\n\t\t\t\t\tthis.recommendations.sort((a, b) => b.likeCount - a.likeCount);\r\n\t\t\t\t\tconsole.log('✅ 成功解析推荐数据，数量:', this.recommendations.length);\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('❌ 解析推荐数据失败:', error)\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 解析父级分类的介绍资料\r\n\t\t\t\ttry {\r\n\t\t\t\t\tthis.currentMaterials = JSON.parse(decodeURIComponent(options.parentMaterials || '[]'));\r\n\t\t\t\t\tconsole.log('✅ 成功解析父级介绍资料，数量:', this.currentMaterials.length);\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('❌ 解析父级介绍资料失败:', error);\r\n\t\t\t\t\tthis.currentMaterials = [];\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 解析父级分类的相关链接\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst relatedLinksStr = decodeURIComponent(options.parentRelatedLinks || '');\r\n\t\t\t\t\tif (relatedLinksStr) {\r\n\t\t\t\t\t\t// 用逗号分隔链接，并过滤空字符串\r\n\t\t\t\t\t\tthis.currentRelatedLinks = relatedLinksStr.split(',')\r\n\t\t\t\t\t\t\t.map(link => link.trim())\r\n\t\t\t\t\t\t\t.filter(link => link.length > 0);\r\n\t\t\t\t\t\tconsole.log('✅ 成功解析父级相关链接，数量:', this.currentRelatedLinks.length);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.currentRelatedLinks = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('❌ 解析父级相关链接失败:', error);\r\n\t\t\t\t\tthis.currentRelatedLinks = [];\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tconsole.log('=== Category页面 onLoad 结束 ===');\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.updateLikesStatus();\r\n\t\t},\r\n\r\n\t\t// 分享功能\r\n\t\tonShareAppMessage(res) {\r\n\t\t\tconsole.log('=== Category页面分享给好友 ===');\r\n\t\t\tconsole.log('分享触发参数:', res);\r\n\t\t\tconsole.log('当前页面数据:', {\r\n\t\t\t\ttitle: this.title,\r\n\t\t\t\tparentImage: this.parentImage,\r\n\t\t\t\tcategoriesCount: this.categories.length,\r\n\t\t\t\trecommendationsCount: this.recommendations.length\r\n\t\t\t});\r\n\r\n\t\t\tconst shareTitle = this.title ? `${this.title} - 熙迈科技` : '熙迈科技分类产品 - 专业工业服务';\r\n\r\n\t\t\t// 由于分享链接长度限制，只传递核心参数，页面加载时重新获取数据\r\n\t\t\tconst sharePath = `pages/category/category?title=${encodeURIComponent(this.title || '')}&pm=${encodeURIComponent(this.pm || '')}&parentImage=${encodeURIComponent(this.parentImage || '/static/熙迈LOGO.png')}&shareMode=1`;\r\n\r\n\t\t\tconsole.log('构建的分享路径:', sharePath);\r\n\t\t\tconsole.log('分享路径长度:', sharePath.length);\r\n\r\n\t\t\tconst shareConfig = ShareUtils.getDefaultShareConfig({\r\n\t\t\t\ttitle: shareTitle,\r\n\t\t\t\tpath: sharePath,\r\n\t\t\t\timageUrl: this.parentImage || '/static/熙迈LOGO.png'\r\n\t\t\t});\r\n\r\n\t\t\tconsole.log('最终分享配置:', shareConfig);\r\n\t\t\tconsole.log('=== Category页面分享配置完成 ===');\r\n\r\n\t\t\treturn shareConfig;\r\n\t\t},\r\n\r\n\t\tonShareTimeline(res) {\r\n\t\t\tconsole.log('=== Category页面分享到朋友圈 ===');\r\n\t\t\tconsole.log('分享触发参数:', res);\r\n\r\n\t\t\tconst shareTitle = this.title ? `${this.title} - 熙迈科技` : '熙迈科技分类产品 - 专业工业服务';\r\n\r\n\t\t\t// 由于分享链接长度限制，只传递核心参数，页面加载时重新获取数据\r\n\t\t\tconst sharePath = `pages/category/category?title=${encodeURIComponent(this.title || '')}&pm=${encodeURIComponent(this.pm || '')}&parentImage=${encodeURIComponent(this.parentImage || '/static/熙迈LOGO.png')}&shareMode=1`;\r\n\r\n\t\t\tconsole.log('构建的分享路径:', sharePath);\r\n\r\n\t\t\tconst shareConfig = ShareUtils.getDefaultShareConfig({\r\n\t\t\t\ttitle: shareTitle,\r\n\t\t\t\tpath: sharePath,\r\n\t\t\t\timageUrl: this.parentImage || '/static/熙迈LOGO.png'\r\n\t\t\t});\r\n\r\n\t\t\tconsole.log('最终分享配置:', shareConfig);\r\n\t\t\tconsole.log('=== Category页面朋友圈分享配置完成 ===');\r\n\r\n\t\t\treturn shareConfig;\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\t// 通过父类ID直接加载数据\r\n\t\t\tasync loadDataByParentId(parentId) {\r\n\t\t\t\tconsole.log('🔄 通过父类ID加载数据:', parentId);\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 先通过 parentId 获取父级分类的完整数据\r\n\t\t\t\t\tconsole.log('🔄 开始获取父级分类完整数据');\r\n\t\t\t\t\tconst parentDataResponse = await uni.request({\r\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',\r\n\t\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\r\n\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\r\n\t\t\t\t\t\t\tworksheetId: 'fenlei',\r\n\t\t\t\t\t\t\tpageSize: 50,\r\n\t\t\t\t\t\t\tpageIndex: 1,\r\n\t\t\t\t\t\t\tlistType: 0,\r\n\t\t\t\t\t\t\tcontrols: [],\r\n\t\t\t\t\t\t\tfilters: [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\tcontrolId: 'rowid',\r\n\t\t\t\t\t\t\t\t\tdataType: 2,\r\n\t\t\t\t\t\t\t\t\tspliceType: 1,\r\n\t\t\t\t\t\t\t\t\tfilterType: 2, // 等于（精确匹配）\r\n\t\t\t\t\t\t\t\t\tvalue: parentId\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tconsole.log('父级数据API响应:', parentDataResponse);\r\n\r\n\t\t\t\t\tif (parentDataResponse.data && parentDataResponse.data.data && parentDataResponse.data.data.rows && parentDataResponse.data.data.rows.length > 0) {\r\n\t\t\t\t\t\t// 获取到完整的父级数据\r\n\t\t\t\t\t\tconst parentItem = parentDataResponse.data.data.rows[0];\r\n\t\t\t\t\t\tconsole.log('✅ 获取到父级分类完整数据:', parentItem);\r\n\r\n\t\t\t\t\t\t// 使用完整的父级数据加载子分类\r\n\t\t\t\t\t\tawait this.loadSubCategories(parentItem);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('❌ 未找到对应的父级分类数据');\r\n\t\t\t\t\t\t// 如果找不到父级数据，创建一个基本对象继续执行\r\n\t\t\t\t\t\tconst parentItem = {\r\n\t\t\t\t\t\t\trowid: parentId\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tawait this.loadSubCategories(parentItem);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('❌ 通过父类ID加载数据失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '加载数据失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 从API重新加载数据（用于分享模式）\r\n\t\t\tasync loadDataFromAPI() {\r\n\t\t\t\tconsole.log('🔄 开始从API重新加载数据');\r\n\t\t\t\tconsole.log('搜索标题:', this.title);\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst requestData = {\r\n\t\t\t\t\t\tappKey: '984e1ff028f80150',\r\n\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\r\n\t\t\t\t\t\tworksheetId: 'fenlei',\r\n\t\t\t\t\t\tpageSize: 50,\r\n\t\t\t\t\t\tpageIndex: 1,\r\n\t\t\t\t\t\tlistType: 0,\r\n\t\t\t\t\t\tcontrols: [],\r\n\t\t\t\t\t\tfilters: [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tcontrolId: '67b2da03ef727a4cd047da1b', // 产品名称字段\r\n\t\t\t\t\t\t\t\tdataType: 2,\r\n\t\t\t\t\t\t\t\tspliceType: 1,\r\n\t\t\t\t\t\t\t\tfilterType: 2, // 等于（精确匹配）\r\n\t\t\t\t\t\t\t\tvalue: this.title\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\tconsole.log('API请求数据:', requestData);\r\n\r\n\t\t\t\t\t// 根据标题搜索对应的分类数据\r\n\t\t\t\t\tconst response = await uni.request({\r\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',\r\n\t\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\t\tdata: requestData,\r\n\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tconsole.log('API响应:', response);\r\n\r\n\t\t\t\t\tif (response.data && response.data.data && response.data.data.rows && response.data.data.rows.length > 0) {\r\n\t\t\t\t\t\tconsole.log('✅ 找到匹配的分类数据，数量:', response.data.data.rows.length);\r\n\t\t\t\t\t\t// 找到匹配的分类，获取其子分类\r\n\t\t\t\t\t\tconst parentItem = response.data.data.rows[0];\r\n\t\t\t\t\t\tconsole.log('父级分类数据:', parentItem);\r\n\t\t\t\t\t\tawait this.loadSubCategories(parentItem);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('❌ 未找到匹配的分类数据');\r\n\t\t\t\t\t\t// 如果找不到数据，显示空状态\r\n\t\t\t\t\t\tthis.categories = [];\r\n\t\t\t\t\t\tthis.recommendations = [];\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '未找到相关数据',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('❌ 重新加载数据失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '加载数据失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 加载子分类数据\r\n\t\t\tasync loadSubCategories(parentItem) {\r\n\t\t\t\tconsole.log('🔄 开始加载子分类数据');\r\n\t\t\t\tconsole.log('父级项目rowid:', parentItem.rowid);\r\n\r\n\t\t\t\t// 从父级项目中提取产品名称并设置导航栏标题\r\n\t\t\t\tconst parentProductName = parentItem['67b2da03ef727a4cd047da1b'] || '';\r\n\t\t\t\tif (parentProductName) {\r\n\t\t\t\t\tthis.title = parentProductName;\r\n\t\t\t\t\tuni.setNavigationBarTitle({\r\n\t\t\t\t\t\ttitle: parentProductName\r\n\t\t\t\t\t});\r\n\t\t\t\t\tconsole.log('✅ 设置导航栏标题为:', parentProductName);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 从父级项目中提取产品经理和联系电话信息\r\n\t\t\t\tconsole.log('🔄 处理父级联系信息');\r\n\t\t\t\tthis.parentProductManager = parentItem['67b2de31ef727a4cd047da6f'] || '';\r\n\t\t\t\tlet contactPhone = parentItem['67b400cdef727a4cd047e08c'] || '';\r\n\t\t\t\t// 去掉 +86 前缀\r\n\t\t\t\tif (contactPhone.startsWith('+86')) {\r\n\t\t\t\t\tcontactPhone = contactPhone.substring(3);\r\n\t\t\t\t}\r\n\t\t\t\tthis.parentContactPhone = contactPhone;\r\n\r\n\t\t\t\t// 处理父级相关资料（只有在没有从URL参数获取到的情况下才处理）\r\n\t\t\t\tif (this.currentMaterials.length === 0) {\r\n\t\t\t\t\tconsole.log('🔄 从API数据处理父级相关资料');\r\n\t\t\t\t\tif (parentItem.DLPP_JSZL) {\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tconst materialsData = JSON.parse(parentItem.DLPP_JSZL);\r\n\t\t\t\t\t\t\tthis.currentMaterials = materialsData.map(material => ({\r\n\t\t\t\t\t\t\t\t// 使用正确的字段名\r\n\t\t\t\t\t\t\t\tname: material.original_file_name || '未知文件',\r\n\t\t\t\t\t\t\t\turl: material.DownloadUrl || material.original_file_full_path || '',\r\n\t\t\t\t\t\t\t\tsize: material.file_size || 0,\r\n\t\t\t\t\t\t\t\t// 保留完整字段（与 form 页面一致）\r\n\t\t\t\t\t\t\t\tDownloadUrl: material.DownloadUrl || '',\r\n\t\t\t\t\t\t\t\toriginal_file_full_path: material.original_file_full_path || '',\r\n\t\t\t\t\t\t\t\toriginal_file_name: material.original_file_name || '未知文件',\r\n\t\t\t\t\t\t\t\tfile_size: material.file_size || 0\r\n\t\t\t\t\t\t\t}));\r\n\t\t\t\t\t\t\tconsole.log('✅ 从API数据成功处理父级相关资料，数量:', this.currentMaterials.length);\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error('❌ 解析API数据中的父级相关资料失败:', error);\r\n\t\t\t\t\t\t\tthis.currentMaterials = [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log('✅ 已从URL参数获取到父级相关资料，跳过API数据处理');\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 处理父级相关链接（只有在没有从URL参数获取到的情况下才处理）\r\n\t\t\t\tif (this.currentRelatedLinks.length === 0) {\r\n\t\t\t\t\tconsole.log('🔄 从API数据处理父级相关链接');\r\n\t\t\t\t\tconst relatedLinksData = parentItem[FIELD_MAPPING.relatedLinks];\r\n\t\t\t\t\tif (relatedLinksData && typeof relatedLinksData === 'string') {\r\n\t\t\t\t\t\t// 用逗号分隔链接，并过滤空字符串\r\n\t\t\t\t\t\tthis.currentRelatedLinks = relatedLinksData.split(',')\r\n\t\t\t\t\t\t\t.map(link => link.trim())\r\n\t\t\t\t\t\t\t.filter(link => link.length > 0);\r\n\t\t\t\t\t\tconsole.log('✅ 从API数据成功处理父级相关链接，数量:', this.currentRelatedLinks.length);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log('✅ 已从URL参数获取到父级相关链接，跳过API数据处理');\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconsole.log('父级联系信息:', {\r\n\t\t\t\t\tproductManager: this.parentProductManager,\r\n\t\t\t\t\tcontactPhone: this.parentContactPhone,\r\n\t\t\t\t\tmaterialsCount: this.currentMaterials.length\r\n\t\t\t\t});\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst subCategoryRequestData = {\r\n\t\t\t\t\t\tappKey: '984e1ff028f80150',\r\n\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\r\n\t\t\t\t\t\tworksheetId: 'fenlei',\r\n\t\t\t\t\t\tpageSize: 50,\r\n\t\t\t\t\t\tpageIndex: 1,\r\n\t\t\t\t\t\trowId: parentItem.rowid,\r\n\t\t\t\t\t\tcontrolId: '67b2dd3aef727a4cd047da37',\r\n\t\t\t\t\t\tgetSystemControl: false\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\tconsole.log('子分类API请求数据:', subCategoryRequestData);\r\n\r\n\t\t\t\t\tconst subCategoryResponse = await uni.request({\r\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',\r\n\t\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\t\tdata: subCategoryRequestData,\r\n\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tconsole.log('子分类API响应:', subCategoryResponse);\r\n\r\n\t\t\t\t\tif (subCategoryResponse.data && subCategoryResponse.data.data && subCategoryResponse.data.data.rows) {\r\n\t\t\t\t\t\tconsole.log('✅ 获取到子分类数据，数量:', subCategoryResponse.data.data.rows.length);\r\n\t\t\t\t\t\tthis.categories = subCategoryResponse.data.data.rows.map(row => this.formatAPIData(row));\r\n\t\t\t\t\t\tthis.categories.sort((a, b) => b.likeCount - a.likeCount);\r\n\t\t\t\t\t\tconsole.log('格式化后的子分类数据:', this.categories);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('❌ 未获取到子分类数据');\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 加载推荐数据\r\n\t\t\t\t\tawait this.loadRecommendations(parentItem);\r\n\r\n\t\t\t\t\tconsole.log('✅ 子分类和推荐数据加载完成');\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('❌ 加载子分类失败:', error);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 加载推荐数据（根据父级项目）\r\n\t\t\tasync loadRecommendations(parentItem) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconsole.log('🔄 开始获取推荐数据，父级rowid:', parentItem?.rowid);\r\n\r\n\t\t\t\t\tif (!parentItem || !parentItem.rowid) {\r\n\t\t\t\t\t\tconsole.log('❌ 没有父级项目信息，跳过推荐数据加载');\r\n\t\t\t\t\t\tthis.recommendations = [];\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconst recommendResponse = await uni.request({\r\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',\r\n\t\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\r\n\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\r\n\t\t\t\t\t\t\tworksheetId: 'fenlei',\r\n\t\t\t\t\t\t\trowId: parentItem.rowid,\r\n\t\t\t\t\t\t\tcontrolId: '67b2dd25ef727a4cd047da2b', // 推荐产品字段ID\r\n\t\t\t\t\t\t\tpageSize: 10,\r\n\t\t\t\t\t\t\tpageIndex: 1\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tif (recommendResponse.data && recommendResponse.data.data && recommendResponse.data.data.rows) {\r\n\t\t\t\t\t\tthis.recommendations = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));\r\n\t\t\t\t\t\tthis.recommendations.sort((a, b) => b.likeCount - a.likeCount);\r\n\t\t\t\t\t\tconsole.log('✅ 获取到推荐数据，数量:', this.recommendations.length);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('❌ 未获取到推荐数据');\r\n\t\t\t\t\t\tthis.recommendations = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('❌ 加载推荐数据失败:', error);\r\n\t\t\t\t\tthis.recommendations = [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\tupdateLikesStatus() {\r\n\t\t\t\tconst likesLog = uni.getStorageSync('likes_log') || {};\r\n\t\t\t\tthis.likesLog = likesLog;\r\n\t\t\t\tconst processList = (list) => {\r\n\t\t\t\t\tif (list && list.length > 0) {\r\n\t\t\t\t\t\tlist.forEach(item => {\r\n\t\t\t\t\t\t\tif (likesLog[item.rowId]) {\r\n\t\t\t\t\t\t\t\tthis.$set(item, 'isLiked', true);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.$set(item, 'isLiked', false);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t};\r\n\t\t\t\tprocessList(this.categories);\r\n\t\t\t\tprocessList(this.recommendations);\r\n\t\t\t},\r\n\t\t\thandleLike(item) {\r\n\t\t\t\t// 1. Toggle the UI state immediately (Optimistic Update)\r\n\t\t\t\titem.isLiked = !item.isLiked;\r\n\t\t\t\t\r\n\t\t\t\tlet webhookAction = '';\r\n\t\t\t\r\n\t\t\t\tif (item.isLiked) {\r\n\t\t\t\t\t// --- UI & Local State Update for LIKE ---\r\n\t\t\t\t\titem.likeCount++;\r\n\t\t\t\t\twebhookAction = 'increment';\r\n\t\t\t\t\tthis.$set(this.likesLog, item.rowId, true);\r\n\t\t\t\r\n\t\t\t\t\t// Trigger animation\r\n\t\t\t\t\tthis.$set(item, 'showHeart', true);\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.$set(item, 'showHeart', false);\r\n\t\t\t\t\t}, 600);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// --- UI & Local State Update for UNLIKE ---\r\n\t\t\t\t\titem.likeCount--;\r\n\t\t\t\t\twebhookAction = 'decrement';\r\n\t\t\t\t\tthis.$delete(this.likesLog, item.rowId);\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t\t// Update local storage for persistence across sessions\r\n\t\t\t\tuni.setStorageSync('likes_log', this.likesLog);\r\n\t\t\t\r\n\t\t\t\t// 2. Send the corresponding command to the webhook\r\n\t\t\t\tuni.request({\r\n\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/workflow/hooks/Njg2Y2ZhNDk3MDMwNzAyNDViNDMxOTcx',\r\n\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\theader: {\r\n\t\t\t\t\t\t'Content-Type': 'application/json'\r\n\t\t\t\t\t},\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\taction: webhookAction,\r\n\t\t\t\t\t\tid: item.rowId\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(`Webhook '${webhookAction}' for rowid ${item.rowId} sent successfully. Response:`, res.data);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error(`Webhook '${webhookAction}' for rowid ${item.rowId} failed:`, err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\thandleContact(pm) {\r\n\t\t\t\t// console.log(pm);\r\n\t\t\t\t// 从PRODUCT_MANAGER_SERVICE_LINKS中查找对应的serviceLink\r\n\t\t\t\tlet serviceLink = 'https://work.weixin.qq.com/kfid/kfc05b214375686d833'; // 默认链接\r\n\t\t\t\tconst managerLink = PRODUCT_MANAGER_SERVICE_LINKS.find(item => item.manager === pm);\r\n\t\t\t\tif (managerLink) {\r\n\t\t\t\t\tserviceLink = managerLink.serviceLink;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// console.log(serviceLink);\r\n\t\t\t\twx.openCustomerServiceChat({\r\n\t\t\t\t\textInfo: {\r\n\t\t\t\t\t\turl: serviceLink\r\n\t\t\t\t\t},\r\n\t\t\t\t\tcorpId: 'wwa76e36d25343b6b9',\r\n\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tformatAPIData(row) {\r\n\t\t\t\tconst formattedItem = {\r\n\t\t\t\t\trowId: row.rowid || '',\r\n\t\t\t\t\tchildren: [],\r\n\t\t\t\t\tlikeCount: parseInt(row['DZS']) || 0,\r\n\t\t\t\t\tisLiked: false,\r\n\t\t\t\t\tshowHeart: false\r\n\t\t\t\t};\r\n\t\t\t\tObject.keys(FIELD_MAPPING).forEach(key => {\r\n\t\t\t\t\tconst apiFieldId = FIELD_MAPPING[key];\r\n\t\t\t\t\tformattedItem[key] = row[apiFieldId] || '';\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// Ensure caseStudiesCount is parsed as a number\r\n\t\t\t\tformattedItem.caseStudiesCount = parseInt(row[FIELD_MAPPING.caseStudiesCount]) || 0;\r\n\r\n\t\t\t\t// 添加图片URL处理\r\n\t\t\t\tif (formattedItem.imageUrl || row.imageUrl) {\r\n\t\t\t\t\tformattedItem.imageUrl = this.getImageUrl(formattedItem.imageUrl || row.imageUrl);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 解析介绍资料\r\n\t\t\t\tif (row.DLPP_JSZL) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tconst materialsData = JSON.parse(row.DLPP_JSZL);\r\n\t\t\t\t\t\tif (Array.isArray(materialsData) && materialsData.length > 0) {\r\n\t\t\t\t\t\t\tformattedItem.introductionMaterials = materialsData.map(material => ({\r\n\t\t\t\t\t\t\t\tname: material.originalFilename || material.name || '未知文件',\r\n\t\t\t\t\t\t\t\turl: material.fileUrl || material.DownloadUrl || material.url || '',\r\n\t\t\t\t\t\t\t\tsize: material.filesize || material.size || 0,\r\n\t\t\t\t\t\t\t\t// 保留原始字段以便兼容\r\n\t\t\t\t\t\t\t\tDownloadUrl: material.fileUrl || material.DownloadUrl || material.url || '',\r\n\t\t\t\t\t\t\t\toriginal_file_full_path: material.fileUrl || material.DownloadUrl || material.url || '',\r\n\t\t\t\t\t\t\t\toriginal_file_name: material.originalFilename || material.name || '未知文件',\r\n\t\t\t\t\t\t\t\tfile_size: material.filesize || material.size || 0\r\n\t\t\t\t\t\t\t}));\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tformattedItem.introductionMaterials = [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\tconsole.error('解析介绍资料失败:', e);\r\n\t\t\t\t\t\tformattedItem.introductionMaterials = [];\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tformattedItem.introductionMaterials = [];\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// 打印解析后的数据\r\n\t\t\t\t// console.log('解析后的数据:', formattedItem);\r\n\r\n\t\t\t\treturn formattedItem;\r\n\t\t\t},\r\n\t\t\tasync handleSearch() {\r\n\t\t\t\tif (!this.searchKeyword.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入搜索关键词',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst requestData = {\r\n\t\t\t\t\t\tappKey: '984e1ff028f80150',\r\n\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\r\n\t\t\t\t\t\tworksheetId: 'fenlei',\r\n\t\t\t\t\t\tviewId: '',\r\n\t\t\t\t\t\tpageSize: 50,\r\n\t\t\t\t\t\tpageIndex: 1,\r\n\t\t\t\t\t\tlistType: 0,\r\n\t\t\t\t\t\tcontrols: [],\r\n\t\t\t\t\t\tfilters: [{\r\n\t\t\t\t\t\t\t\tcontrolId: '67b2da03ef727a4cd047da1b',\r\n\t\t\t\t\t\t\t\tdataType: 2,\r\n\t\t\t\t\t\t\t\tspliceType: 2,\r\n\t\t\t\t\t\t\t\tfilterType: 1,\r\n\t\t\t\t\t\t\t\tvalue: this.searchKeyword\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tcontrolId: '67b2dd25ef727a4cd047da2a',\r\n\t\t\t\t\t\t\t\tdataType: 2,\r\n\t\t\t\t\t\t\t\tspliceType: 2,\r\n\t\t\t\t\t\t\t\tfilterType: 1,\r\n\t\t\t\t\t\t\t\tvalue: this.searchKeyword\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\tconst response = await uni.request({\r\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',\r\n\t\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\t\tdata: requestData,\r\n\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tif (response.statusCode !== 200) {\r\n\t\t\t\t\t\tconsole.error('请求失败:', response);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请求失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (response.data && response.data.data) {\r\n\t\t\t\t\t\tif (response.data.data.rows && response.data.data.rows.length > 0) {\r\n\t\t\t\t\t\t\tthis.categories = response.data.data.rows.map(row => ({\r\n\t\t\t\t\t\t\t\tname: row['67b2da03ef727a4cd047da1b'] || '未命名分类',\r\n\t\t\t\t\t\t\t\tid: row['67b2dd25ef727a4cd047da2a'] || '',\r\n\t\t\t\t\t\t\t\ticon: this.getImageUrl(row['67b2dd25ef727a4cd047da2b']) ||\r\n\t\t\t\t\t\t\t\t\t'/static/熙迈LOGO.png',\r\n\t\t\t\t\t\t\t\trowId: row.rowid || '',\r\n\t\t\t\t\t\t\t\tchildren: [],\r\n\t\t\t\t\t\t\t\tlikeCount: parseInt(row['DZS']) || 0,\r\n\t\t\t\t\t\t\t\tisLiked: false\r\n\t\t\t\t\t\t\t}));\r\n\t\t\t\t\t\t\tconsole.log(agentBrandImage)\r\n\t\t\t\t\t\t} else if (response.data.data.total === 0 || response.data.error_code === 1) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '没有找到相关产品',\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tthis.categories = [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('接口返回数据格式异常:', response.data);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '数据格式异常',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取分类失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取分类失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetImageUrl(imageData) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 支持直接传入数组或JSON字符串\r\n\t\t\t\t\tconst parsedData = Array.isArray(imageData) ? imageData : JSON.parse(imageData);\r\n\r\n\t\t\t\t\tif (!Array.isArray(parsedData)) {\r\n\t\t\t\t\t\tconsole.warn(\"图片数据格式错误\");\r\n\t\t\t\t\t\treturn '/static/熙迈LOGO.png'; // 返回默认图\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 查找第一个有效URL（优先fileUrl，其次thumbnail_full_path）\r\n\t\t\t\t\tconst targetItem = parsedData.find(item =>\r\n\t\t\t\t\t\titem.fileUrl?.startsWith('http') ||\r\n\t\t\t\t\t\titem.thumbnail_full_path?.startsWith('http')\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\t// 返回优先级：fileUrl > thumbnail_full_path > 默认图\r\n\t\t\t\t\treturn targetItem?.fileUrl ||\r\n\t\t\t\t\t\ttargetItem?.thumbnail_full_path ||\r\n\t\t\t\t\t\t'/static/熙迈LOGO.png';\r\n\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error(\"解析失败:\", error.message);\r\n\t\t\t\t\treturn '/static/熙迈LOGO.png'; // 始终返回字符串\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcleanImageUrl(url) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 解码URL中的特殊字符\r\n\t\t\t\t\tlet cleanedUrl = decodeURIComponent(url);\r\n\t\t\t\t\t// 移除URL中的多余空格\r\n\t\t\t\t\tcleanedUrl = cleanedUrl.trim();\r\n\t\t\t\t\t// 确保URL以http或https开头\r\n\t\t\t\t\tif (!cleanedUrl.startsWith('http')) {\r\n\t\t\t\t\t\tcleanedUrl = `https://${cleanedUrl}`;\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn cleanedUrl;\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('清理图片URL失败:', error);\r\n\t\t\t\t\treturn null;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync handleCategoryClick(item) {\r\n\t\t\t\tlet recommendations = [];\r\n\t\t\t\tlet parentMaterialsWithDownloadUrl = [];\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 检查是否有相关资料需要获取完整信息\r\n\t\t\t\t\tconst hasIntroductionMaterials = item.introductionMaterials && item.introductionMaterials.length > 0;\r\n\r\n\t\t\t\t\t// 如果有相关资料，先调用 getFilterRows 获取包含 DownloadUrl 的完整信息\r\n\t\t\t\t\tif (hasIntroductionMaterials) {\r\n\t\t\t\t\t\tconsole.log('检测到相关资料，获取完整附件信息...');\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tconst materialResponse = await uni.request({\r\n\t\t\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows',\r\n\t\t\t\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\r\n\t\t\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\r\n\t\t\t\t\t\t\t\t\tworksheetId: 'fenlei',\r\n\t\t\t\t\t\t\t\t\tpageSize: 1,\r\n\t\t\t\t\t\t\t\t\tpageIndex: 1,\r\n\t\t\t\t\t\t\t\t\tlistType: 0,\r\n\t\t\t\t\t\t\t\t\tcontrols: [],\r\n\t\t\t\t\t\t\t\t\tfilters: [{\r\n\t\t\t\t\t\t\t\t\t\tcontrolId: 'rowid',\r\n\t\t\t\t\t\t\t\t\t\tdataType: 2,\r\n\t\t\t\t\t\t\t\t\t\tspliceType: 1,\r\n\t\t\t\t\t\t\t\t\t\tfilterType: 2,\r\n\t\t\t\t\t\t\t\t\t\tvalue: item.rowId\r\n\t\t\t\t\t\t\t\t\t}]\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t\t\t'Content-Type': 'application/json'\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\tif (materialResponse.data && materialResponse.data.data && materialResponse.data.data.rows && materialResponse.data.data.rows.length > 0) {\r\n\t\t\t\t\t\t\t\tconst productData = materialResponse.data.data.rows[0];\r\n\t\t\t\t\t\t\t\tif (productData.DLPP_JSZL) {\r\n\t\t\t\t\t\t\t\t\tconst materialsData = JSON.parse(productData.DLPP_JSZL);\r\n\t\t\t\t\t\t\t\t\tconsole.log('原始附件数据:', materialsData);\r\n\t\t\t\t\t\t\t\t\tparentMaterialsWithDownloadUrl = materialsData.map(material => {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('处理单个附件:', material);\r\n\t\t\t\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t\t\t\t// 使用正确的字段名\r\n\t\t\t\t\t\t\t\t\t\t\tname: material.original_file_name || '未知文件',\r\n\t\t\t\t\t\t\t\t\t\t\turl: material.DownloadUrl || material.original_file_full_path || '',\r\n\t\t\t\t\t\t\t\t\t\t\tsize: material.file_size || 0,\r\n\t\t\t\t\t\t\t\t\t\t\t// 保留完整的字段信息（与 form 页面一致）\r\n\t\t\t\t\t\t\t\t\t\t\tDownloadUrl: material.DownloadUrl || '',\r\n\t\t\t\t\t\t\t\t\t\t\toriginal_file_full_path: material.original_file_full_path || '',\r\n\t\t\t\t\t\t\t\t\t\t\toriginal_file_name: material.original_file_name || '未知文件',\r\n\t\t\t\t\t\t\t\t\t\t\tfile_size: material.file_size || 0\r\n\t\t\t\t\t\t\t\t\t\t};\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\tconsole.log('映射后的附件信息:', parentMaterialsWithDownloadUrl);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error('获取完整附件信息失败:', error);\r\n\t\t\t\t\t\t\t// 如果获取失败，使用原有的资料信息\r\n\t\t\t\t\t\t\tparentMaterialsWithDownloadUrl = item.introductionMaterials || [];\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconst subCategoryResponse = await uni.request({\r\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',\r\n\t\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\r\n\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\r\n\t\t\t\t\t\t\tworksheetId: 'fenlei',\r\n\t\t\t\t\t\t\tpageSize: 50,\r\n\t\t\t\t\t\t\tpageIndex: 1,\r\n\t\t\t\t\t\t\trowId: item.rowId,\r\n\t\t\t\t\t\t\tcontrolId: '67b2dd3aef727a4cd047da37',\r\n\t\t\t\t\t\t\tgetSystemControl: false\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\tconst recommendResponse = await uni.request({\r\n\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',\r\n\t\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\r\n\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\r\n\t\t\t\t\t\t\tworksheetId: 'fenlei',\r\n\t\t\t\t\t\t\tpageSize: 50,\r\n\t\t\t\t\t\t\tpageIndex: 1,\r\n\t\t\t\t\t\t\trowId: item.rowId,\r\n\t\t\t\t\t\t\tcontrolId: 'GLCP',\r\n\t\t\t\t\t\t\tgetSystemControl: false\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t'Content-Type': 'application/json'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tif (subCategoryResponse.statusCode !== 200) {\r\n\t\t\t\t\t\tconsole.error('请求子分类失败:', response);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '请求子分类失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\tif (!subCategoryResponse.data || !subCategoryResponse.data.data) {\r\n\t\t\t\t\t\tconsole.error('接口返回数据格式异常:', subCategoryResponse.data);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '数据格式异常',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tif (!subCategoryResponse.data.data.rows || subCategoryResponse.data.data.rows.length === 0) {\r\n\t\t\t\t\t\trecommendations = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));\r\n\r\n\t\t\t\t\t\tlet caseStudies = [];\r\n\t\t\t\t\t\tif (item.caseStudiesCount > 0) {\r\n\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\tconst caseStudiesResponse = await uni.request({\r\n\t\t\t\t\t\t\t\t\turl: 'https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations',\r\n\t\t\t\t\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\t\t\t\tappKey: '984e1ff028f80150',\r\n\t\t\t\t\t\t\t\t\t\tsign: 'NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==',\r\n\t\t\t\t\t\t\t\t\t\tworksheetId: 'fenlei',\r\n\t\t\t\t\t\t\t\t\t\tpageSize: 50,\r\n\t\t\t\t\t\t\t\t\t\tpageIndex: 1,\r\n\t\t\t\t\t\t\t\t\t\trowId: item.rowId,\r\n\t\t\t\t\t\t\t\t\t\tcontrolId: 'ALQK',\r\n\t\t\t\t\t\t\t\t\t\tgetSystemControl: false\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\t\t\t\t'Content-Type': 'application/json'\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tif (caseStudiesResponse.data && caseStudiesResponse.data.data && caseStudiesResponse.data.data.rows) {\r\n\t\t\t\t\t\t\t\t\tcaseStudies = caseStudiesResponse.data.data.rows.map(row => ({\r\n\t\t\t\t\t\t\t\t\t\tclientName: row[FIELD_MAPPING.caseClientName] || '',\r\n\t\t\t\t\t\t\t\t\t\tdetails: row[FIELD_MAPPING.caseDetails] || ''\r\n\t\t\t\t\t\t\t\t\t}));\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\t\t\tconsole.error('获取案例情况失败:', e);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 正常页面跳转：传递基本数据，但不包含推荐产品（避免URL过长）\r\n\t\t\t\t\t\tconst formUrl = `/pages/form/form?${objectToParams(item)}&caseStudies=${encodeURIComponent(JSON.stringify(caseStudies))}`;\r\n\r\n\t\t\t\t\t\tconsole.log('=== Category页面跳转到Form页面 ===');\r\n\t\t\t\t\t\tconsole.log('跳转的产品信息:', {\r\n\t\t\t\t\t\t\tproductName: item.productName,\r\n\t\t\t\t\t\t\tproductCode: item.productCode,\r\n\t\t\t\t\t\t\tcaseStudiesCount: caseStudies.length\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tconsole.log('跳转URL长度:', formUrl.length);\r\n\r\n\t\t\t\t\t\t// 检查URL长度，如果仍然过长则使用简化模式\r\n\t\t\t\t\t\tif (formUrl.length > 1000) {\r\n\t\t\t\t\t\t\tconsole.log('⚠️ URL仍然过长，使用简化模式');\r\n\t\t\t\t\t\t\tconst simpleUrl = item.productCode ?\r\n\t\t\t\t\t\t\t\t`/pages/form/form?productCode=${encodeURIComponent(item.productCode)}&rowid=${encodeURIComponent(item.rowId)}&shareMode=1` :\r\n\t\t\t\t\t\t\t\t`/pages/form/form?productName=${encodeURIComponent(item.productName)}&rowid=${encodeURIComponent(item.rowId)}&shareMode=1`;\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: simpleUrl\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: formUrl\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconst formattedData = subCategoryResponse.data.data.rows.map(row => this.formatAPIData(row));\r\n\t\t\t\t\trecommendations = recommendResponse.data.data.rows.map(row => this.formatAPIData(row));\r\n\r\n\t\t\t\t\t// 传递父级分类的介绍资料（使用包含 DownloadUrl 的完整信息）\r\n\t\t\t\t\tconst parentMaterials = parentMaterialsWithDownloadUrl.length > 0 ? parentMaterialsWithDownloadUrl : (item.introductionMaterials || []);\r\n\t\t\t\t\tconsole.log('传递父级分类介绍资料:', parentMaterials);\r\n\r\n\t\t\t\t\t// 传递父级分类的相关链接\r\n\t\t\t\t\tconst parentRelatedLinks = item.relatedLinks || [];\r\n\t\t\t\t\tconsole.log('传递父级分类相关链接:', parentRelatedLinks);\r\n\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/pages/category/category?title=${encodeURIComponent(item.productName)}&pm=${encodeURIComponent(item.productManager)}&parentImage=${encodeURIComponent(item.imageUrl || '/static/熙迈LOGO.png')}&parentContactPhone=${encodeURIComponent(item.contactPhone || '')}&children=${encodeURIComponent(JSON.stringify(formattedData))}&recommendations=${encodeURIComponent(JSON.stringify(recommendations))}&parentMaterials=${encodeURIComponent(JSON.stringify(parentMaterials))}&parentRelatedLinks=${encodeURIComponent(parentRelatedLinks.join(','))}`\r\n\t\t\t\t\t});\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取子分类失败:', error);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '获取子分类失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 显示父级分类资料列表\r\n\t\t\tshowParentMaterialsList() {\r\n\t\t\t\t// currentMaterials 已经在 onLoad 中设置为父级资料\r\n\t\t\t\tthis.showMaterialsModal = true;\r\n\t\t\t},\r\n\t\t\t// 隐藏资料列表\r\n\t\t\thideMaterialsList() {\r\n\t\t\t\tthis.showMaterialsModal = false;\r\n\t\t\t\t// 不要清空 currentMaterials，保持按钮显示\r\n\t\t\t},\r\n\t\t\t// 预览资料文件\r\n\t\t\tpreviewMaterial(material) {\r\n\t\t\t\tconsole.log('预览文件:', material.original_file_name);\r\n\t\t\t\tconsole.log('文件信息:', material);\r\n\r\n\t\t\t\t// 优先使用 DownloadUrl，如果没有则使用其他URL字段\r\n\t\t\t\tconst downloadUrl = material.DownloadUrl;\r\n\t\t\t\tconst originalUrl = material.original_file_full_path;\r\n\t\t\t\tconst fileName = material.original_file_name;\r\n\r\n\t\t\t\tconsole.log('使用 DownloadUrl:', downloadUrl);\r\n\t\t\t\tconsole.log('备用 URL:', originalUrl);\r\n\t\t\t\tconsole.log('文件名:', fileName);\r\n\r\n\t\t\t\t// 获取文件类型\r\n\t\t\t\tconst fileType = this.getFileType(fileName);\r\n\t\t\t\tconsole.log('文件类型:', fileType);\r\n\r\n\t\t\t\t// 根据文件类型选择预览方式\r\n\t\t\t\tif (fileType === 'video') {\r\n\t\t\t\t\t// 视频文件预览\r\n\t\t\t\t\tthis.previewVideo(downloadUrl || originalUrl, fileName);\r\n\t\t\t\t} else if (fileType === 'image') {\r\n\t\t\t\t\t// 图片文件预览\r\n\t\t\t\t\tthis.previewImage(downloadUrl || originalUrl, fileName);\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 其他文件类型使用原有的文档预览方式\r\n\t\t\t\t\tthis.previewWechatWithFallback(downloadUrl, originalUrl, fileName);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 拨打电话\r\n\t\t\tcallnum(num) {\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: num\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 带回退机制的预览方法\r\n\t\t\tpreviewWechatWithFallback(primaryUrl, fallbackUrl, fileName) {\r\n\t\t\t\tconsole.log('尝试预览文档:', fileName);\r\n\t\t\t\tconsole.log('主要URL:', primaryUrl);\r\n\t\t\t\tconsole.log('备用URL:', fallbackUrl);\r\n\r\n\t\t\t\t// 先尝试主要URL\r\n\t\t\t\tthis.previewWechatSingle(primaryUrl, (success) => {\r\n\t\t\t\t\tif (!success && fallbackUrl && fallbackUrl !== primaryUrl) {\r\n\t\t\t\t\t\tconsole.log('主要URL失败，尝试备用URL');\r\n\t\t\t\t\t\tthis.previewWechatSingle(fallbackUrl, (success) => {\r\n\t\t\t\t\t\t\tif (!success) {\r\n\t\t\t\t\t\t\t\t// 两个URL都失败，显示最终错误\r\n\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\ttitle: '预览失败',\r\n\t\t\t\t\t\t\t\t\tcontent: '文件无法预览，可能是文件格式不支持或网络问题。\\n\\n是否复制文件链接？',\r\n\t\t\t\t\t\t\t\t\tconfirmText: '复制链接',\r\n\t\t\t\t\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\t\t\t\t\tsuccess: function(modalRes) {\r\n\t\t\t\t\t\t\t\t\t\tif (modalRes.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\t\t\t\t\tdata: primaryUrl,\r\n\t\t\t\t\t\t\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '链接已复制',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 单次预览尝试\r\n\t\t\tpreviewWechatSingle(urlPdf, callback) {\r\n\t\t\t\tif (!urlPdf || typeof urlPdf !== 'string') {\r\n\t\t\t\t\tconsole.error('无效的文档URL:', urlPdf);\r\n\t\t\t\t\tcallback(false);\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在加载中..'\r\n\t\t\t\t});\r\n\r\n\t\t\t\tconsole.log('开始下载文件:', urlPdf);\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: urlPdf,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tconsole.log('文件下载成功:', res);\r\n\t\t\t\t\t\tvar filePath = res.tempFilePath;\r\n\r\n\t\t\t\t\t\tif (!filePath) {\r\n\t\t\t\t\t\t\tconsole.error('下载成功但临时文件路径为空');\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tcallback(false);\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tconsole.log('准备打开文档:', filePath);\r\n\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\tfilePath: filePath,\r\n\t\t\t\t\t\t\tshowMenu: false,\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tconsole.log('打开文档成功:', res);\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tcallback(true);\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\tconsole.error('打开文档失败:', err);\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tcallback(false);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\tconsole.error('下载文件失败:', err);\r\n\t\t\t\t\t\tconsole.error('失败的URL:', urlPdf);\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tcallback(false);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 微信小程序预览文档\r\n\t\t\tpreviewWechat(urlPdf) {\r\n\t\t\t\tconsole.log('准备预览文档，URL:', urlPdf);\r\n\r\n\t\t\t\t// 检查URL是否有效\r\n\t\t\t\tif (!urlPdf || typeof urlPdf !== 'string') {\r\n\t\t\t\t\tconsole.error('无效的文档URL:', urlPdf);\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '文档链接无效',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在加载中..'\r\n\t\t\t\t});\r\n\r\n\t\t\t\tconsole.log('开始下载文件:', urlPdf);\r\n\t\t\t\tuni.downloadFile({\r\n\t\t\t\t\turl: urlPdf,\r\n\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\tconsole.log('文件下载成功:', res);\r\n\t\t\t\t\t\tvar filePath = res.tempFilePath;\r\n\r\n\t\t\t\t\t\tif (!filePath) {\r\n\t\t\t\t\t\t\tconsole.error('下载成功但临时文件路径为空');\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '文件路径错误',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tconsole.log('准备打开文档:', filePath);\r\n\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\tfilePath: filePath,\r\n\t\t\t\t\t\t\tshowMenu: false, // 禁用菜单，防止下载\r\n\t\t\t\t\t\t\tsuccess: function(res) {\r\n\t\t\t\t\t\t\t\tconsole.log('打开文档成功:', res);\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\tconsole.error('打开文档失败:', err);\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '文档格式不支持或文件损坏',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\tconsole.error('下载文件失败:', err);\r\n\t\t\t\t\t\tconsole.error('失败的URL:', urlPdf);\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\r\n\t\t\t\t\t\t// 根据错误类型给出不同提示\r\n\t\t\t\t\t\tlet errorMsg = '文件加载失败';\r\n\t\t\t\t\t\tif (err.errMsg && err.errMsg.includes('ENOENT')) {\r\n\t\t\t\t\t\t\terrorMsg = '文件不存在或已被删除';\r\n\t\t\t\t\t\t} else if (err.errMsg && err.errMsg.includes('network')) {\r\n\t\t\t\t\t\t\terrorMsg = '网络连接失败，请检查网络';\r\n\t\t\t\t\t\t} else if (err.errMsg && err.errMsg.includes('timeout')) {\r\n\t\t\t\t\t\t\terrorMsg = '下载超时，请重试';\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '预览失败',\r\n\t\t\t\t\t\t\tcontent: errorMsg + '\\n\\n是否尝试在浏览器中打开？',\r\n\t\t\t\t\t\t\tconfirmText: '打开',\r\n\t\t\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\t\t\tsuccess: function(modalRes) {\r\n\t\t\t\t\t\t\t\tif (modalRes.confirm) {\r\n\t\t\t\t\t\t\t\t\t// 尝试用浏览器打开\r\n\t\t\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\t\t\tdata: urlPdf,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '链接已复制到剪贴板',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 获取文件类型\r\n\t\t\tgetFileType(fileName) {\r\n\t\t\t\tconst ext = fileName.split('.').pop().toLowerCase();\r\n\r\n\t\t\t\t// 视频类型\r\n\t\t\t\tconst videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'ogg'];\r\n\t\t\t\tif (videoTypes.includes(ext)) return 'video';\r\n\r\n\t\t\t\t// 图片类型\r\n\t\t\t\tconst imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];\r\n\t\t\t\tif (imageTypes.includes(ext)) return 'image';\r\n\r\n\t\t\t\t// PDF类型\r\n\t\t\t\tif (ext === 'pdf') return 'pdf';\r\n\r\n\t\t\t\t// 文档类型\r\n\t\t\t\tconst docTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];\r\n\t\t\t\tif (docTypes.includes(ext)) return 'document';\r\n\r\n\t\t\t\treturn 'unknown';\r\n\t\t\t},\r\n\t\t\t// 获取文件图标\r\n\t\t\tgetMaterialIcon(fileName) {\r\n\t\t\t\tconst ext = fileName.split('.').pop().toLowerCase();\r\n\t\t\t\tswitch (ext) {\r\n\t\t\t\t\tcase 'pdf':\r\n\t\t\t\t\t\treturn 'paperplane';\r\n\t\t\t\t\tcase 'doc':\r\n\t\t\t\t\tcase 'docx':\r\n\t\t\t\t\t\treturn 'compose';\r\n\t\t\t\t\tcase 'xls':\r\n\t\t\t\t\tcase 'xlsx':\r\n\t\t\t\t\t\treturn 'bars';\r\n\t\t\t\t\tcase 'ppt':\r\n\t\t\t\t\tcase 'pptx':\r\n\t\t\t\t\t\treturn 'videocam';\r\n\t\t\t\t\tcase 'mp4':\r\n\t\t\t\t\tcase 'avi':\r\n\t\t\t\t\tcase 'mov':\r\n\t\t\t\t\tcase 'wmv':\r\n\t\t\t\t\tcase 'flv':\r\n\t\t\t\t\tcase 'webm':\r\n\t\t\t\t\tcase 'mkv':\r\n\t\t\t\t\t\treturn 'videocam-filled';\r\n\t\t\t\t\tcase 'jpg':\r\n\t\t\t\t\tcase 'jpeg':\r\n\t\t\t\t\tcase 'png':\r\n\t\t\t\t\tcase 'gif':\r\n\t\t\t\t\tcase 'bmp':\r\n\t\t\t\t\tcase 'webp':\r\n\t\t\t\t\t\treturn 'image';\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\treturn 'folder';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 预览视频文件\r\n\t\t\tpreviewVideo(videoUrl, fileName) {\r\n\t\t\t\tconsole.log('预览视频:', fileName);\r\n\t\t\t\tconsole.log('视频URL:', videoUrl);\r\n\r\n\t\t\t\t// 在微信小程序中，我们可以使用 video 组件来播放视频\r\n\t\t\t\t// 这里我们创建一个简单的视频预览弹窗\r\n\t\t\t\tthis.showVideoModal = true;\r\n\t\t\t\tthis.currentVideoUrl = videoUrl;\r\n\t\t\t\tthis.currentVideoName = fileName;\r\n\t\t\t},\r\n\t\t\t// 预览图片文件\r\n\t\t\tpreviewImage(imageUrl, fileName) {\r\n\t\t\t\tconsole.log('预览图片:', fileName);\r\n\t\t\t\tconsole.log('图片URL:', imageUrl);\r\n\r\n\t\t\t\t// 使用微信小程序的图片预览功能\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\turls: [imageUrl],\r\n\t\t\t\t\tcurrent: imageUrl,\r\n\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\tconsole.log('图片预览成功');\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\tconsole.error('图片预览失败:', err);\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '图片预览失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 关闭视频预览\r\n\t\t\tcloseVideoModal() {\r\n\t\t\t\tthis.showVideoModal = false;\r\n\t\t\t\tthis.currentVideoUrl = '';\r\n\t\t\t\tthis.currentVideoName = '';\r\n\t\t\t},\r\n\t\t\t// 获取链接显示文本\r\n\t\t\tgetLinkDisplayText(link) {\r\n\t\t\t\t// 如果是微信公众号文章链接，显示\"公众号文章\"\r\n\t\t\t\tif (link.includes('mp.weixin.qq.com')) {\r\n\t\t\t\t\treturn '公众号文章';\r\n\t\t\t\t}\r\n\t\t\t\t// 如果是视频号链接，显示\"视频号\"\r\n\t\t\t\tif (link.includes('channels.weixin.qq.com') || link.includes('video.weixin.qq.com')) {\r\n\t\t\t\t\treturn '视频号';\r\n\t\t\t\t}\r\n\t\t\t\t// 其他链接显示域名\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst url = new URL(link);\r\n\t\t\t\t\treturn url.hostname;\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\treturn '相关链接';\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 打开相关链接\r\n\t\t\topenRelatedLink(link) {\r\n\t\t\t\tconsole.log('打开相关链接:', link);\r\n\r\n\t\t\t\t// 获取链接显示名称作为页面标题\r\n\t\t\t\tconst linkTitle = this.getLinkDisplayText(link);\r\n\r\n\t\t\t\t// 跳转到 webview 页面\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/pages/WebView/WebView?url=${encodeURIComponent(link)}&title=${encodeURIComponent(linkTitle)}`,\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.error('跳转到 webview 页面失败:', err);\r\n\t\t\t\t\t\t// 如果跳转失败，回退到复制链接的方式\r\n\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\ttitle: '打开链接',\r\n\t\t\t\t\t\t\tcontent: '无法直接打开链接，是否复制到剪贴板？',\r\n\t\t\t\t\t\t\tconfirmText: '复制链接',\r\n\t\t\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\t\t\t\t\t\tdata: link,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '链接已复制',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '复制失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 格式化文件大小\r\n\t\t\tformatFileSize(bytes) {\r\n\t\t\t\tif (bytes === 0) return '0 B';\r\n\t\t\t\tconst k = 1024;\r\n\t\t\t\tconst sizes = ['B', 'KB', 'MB', 'GB'];\r\n\t\t\t\tconst i = Math.floor(Math.log(bytes) / Math.log(k));\r\n\t\t\t\treturn parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.container {\r\n\t\tposition: relative;\r\n\t\tbackground: #42f3f933;\r\n\t\tanimation: bg-flow 25s linear infinite;\r\n\t\tmin-height: 100vh;\r\n\t\tpadding: 0;\r\n\t\tpadding-bottom: calc(200rpx + env(safe-area-inset-bottom));\r\n\t}\r\n\r\n\t/* 提升内容层级 */\r\n\t.category-list,\r\n\t.no-result,\r\n\t.recommend-section {\r\n\t\tposition: relative;\r\n\t\tz-index: 1;\r\n\t}\r\n\r\n\t.category-list {\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 24rpx;\r\n\t}\r\n\r\n\t.category-item {\r\n\t\twidth: calc(50% - 12rpx);\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 16rpx;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);\r\n\t\toverflow: hidden;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.img {\r\n\t\twidth: 100%;\r\n\t\theight: 250rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.img .icon {\r\n\t\theight: 100%;\r\n\t\twidth: 100%;\r\n\t\tdisplay: block;\r\n\t\tobject-fit: contain;\r\n\t}\r\n\r\n\t.banner {\r\n\t\tpadding: 10rpx;\r\n\t}\r\n\r\n\t.banner-left {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.top-blue {\r\n\t\theight: 60rpx;\r\n\t\twidth: 100%;\r\n\t\tborder-radius: 15rpx;\r\n\t\tbackground-color: #6d92cc;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.english-name, .english-name-content {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-size: 16rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 1.4;\r\n\t\tword-wrap: break-word;\r\n\t\tword-break: break-all;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.more-text {\r\n\t\tmargin-left: 12rpx;\r\n\t}\r\n\r\n\t.english-name-content .more-text {\r\n\t\tmargin-top: 4rpx;\r\n\t\tfont-size: 14rpx;\r\n\t\topacity: 0.9;\r\n\t}\r\n\r\n\t.bottom-white {\r\n\t\tcolor: #6d92cc;\r\n\t\tmin-height: 80rpx;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #fff;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.bottom-white-text {\r\n\t\tflex: 1;\r\n\t\tmin-width: 0;\r\n\t\tfont-size: 22rpx;\r\n\t\ttext-align: center;\r\n\t\twhite-space: normal;\r\n\t\tline-height: 1.4;\r\n\t\tmargin: 0 8rpx;\r\n\t}\r\n\r\n\t.logo {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tflex-shrink: 0;\r\n\t\twidth: 70rpx;\r\n\t\theight: 70rpx;\r\n\t}\r\n\t\r\n\t.logo .icon {\r\n\t\tmax-width: 100%;\r\n\t\tmax-height: 100%;\r\n\t\tobject-fit: contain;\r\n\t\tpadding: 0;\r\n\t}\r\n\t\r\n\t.like-section {\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 0 10rpx;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\t\r\n\t.like-icon {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n\t\r\n\t.pop-animation {\r\n\t\tposition: absolute;\r\n\t\tbottom: 100%; /* 从按钮上方开始 */\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tflex-direction: column;\r\n\t\tanimation: pop-up 0.8s ease-out forwards;\r\n\t\tpointer-events: none;\r\n\t}\r\n\t\r\n\t.pop-heart {\r\n\t\tfont-size: 30rpx;\r\n\t}\r\n\t\r\n\t.pop-count {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ff6a6a;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t@keyframes pop-up {\r\n\t\t0% {\r\n\t\t\ttransform: translateX(-50%) scale(0.5);\r\n\t\t\topacity: 0;\r\n\t\t\tbottom: 100%;\r\n\t\t}\r\n\t\t50% {\r\n\t\t\ttransform: translateX(-50%) scale(1.2);\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\t\t100% {\r\n\t\t\ttransform: translateX(-50%) scale(1);\r\n\t\t\topacity: 0;\r\n\t\t\tbottom: 150%; /* 向上飘动 */\r\n\t\t}\r\n\t}\r\n\t\r\n\t.recommend-section {\r\n\t\tpadding: 24rpx;\r\n\t\tbackground-color: #f7f8fa;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\ttext-align: center;\r\n\t\tcolor: #333;\r\n\t}\r\n\t\r\n\t.fixed-customer-service {\r\n\t\tposition: fixed;\r\n\t\tbottom: 40rpx;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t\twidth: 90%;\r\n\t\tz-index: 10;\r\n\t}\r\n\r\n\t.no-result {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 40rpx;\r\n\t\tmargin: 40rpx;\r\n\t\tbackground-color: #f9f9f9;\r\n\t\tborder-radius: 20rpx;\r\n\t\ttext-align: center;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.no-result-icon {\r\n\t\tfont-size: 100rpx;\r\n\t\tcolor: #e0e0e0;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.no-result-text {\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\r\n\t.no-result-subtext {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\t\r\n\t.service-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 10rpx;\r\n\t\tbackground: linear-gradient(135deg, #2979FF, #00B4FF);\r\n\t\tborder-radius: 24rpx;\r\n\t\tbox-shadow: 0 12rpx 36rpx rgba(41, 121, 255, 0.3);\r\n\t\ttransition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);\r\n\t\tbackdrop-filter: blur(10px);\r\n\t\tborder: 1rpx solid rgba(255, 255, 255, 0.1);\r\n\t}\r\n\r\n\t.text-container {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.btn-text {\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 38rpx;\r\n\t\tfont-weight: 600;\r\n\t\ttext-align: center;\r\n\t\tdisplay: block;\r\n\t\tmargin-bottom: 0;\r\n\t\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.btn-subtext {\r\n\t\tcolor: rgba(255, 255, 255, 0.9);\r\n\t\tfont-size: 24rpx;\r\n\t\tline-height: 1.6;\r\n\t\ttext-align: center;\r\n\t\tdisplay: block;\r\n\t\topacity: 0.9;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\r\n\t/* 交互动画 */\r\n\t.service-btn:active {\r\n\t\ttransform: scale(0.98) translateY(2rpx);\r\n\t\tbox-shadow: 0 6rpx 18rpx rgba(41, 121, 255, 0.25);\r\n\t}\r\n\r\n\r\n\t/* 悬浮呼吸动画 */\r\n\t@keyframes float {\r\n\t\t0% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\ttransform: translateY(-10rpx);\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: translateY(0);\r\n\t\t}\r\n\t}\r\n\r\n\t.service-btn {\r\n\t\tanimation: float 3s ease-in-out infinite;\r\n\t}\r\n\r\n\t/* 流光边框效果 */\r\n\t.service-btn::after {\r\n\t\tcontent: '';\r\n\t\tposition: absolute;\r\n\t\ttop: -1rpx;\r\n\t\tleft: -1rpx;\r\n\t\tright: -1rpx;\r\n\t\tbottom: -1rpx;\r\n\t\tbackground: linear-gradient(45deg,\r\n\t\t\t\trgba(255, 255, 255, 0) 0%,\r\n\t\t\t\trgba(255, 255, 255, 0.3) 50%,\r\n\t\t\t\trgba(255, 255, 255, 0) 100%);\r\n\t\tborder-radius: 24rpx;\r\n\t\tanimation: shine 3s infinite;\r\n\t\tz-index: -1;\r\n\t}\r\n\r\n\t@keyframes shine {\r\n\t\t0% {\r\n\t\t\topacity: 0;\r\n\t\t\tleft: -50%;\r\n\t\t}\r\n\r\n\t\t50% {\r\n\t\t\topacity: 0.4;\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\topacity: 0;\r\n\t\t\tleft: 150%;\r\n\t\t}\r\n\t}\r\n\r\n\t/* 介绍资料卡片样式 */\r\n\t.materials-card {\r\n\t\tmargin: 20rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tbackground: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 12rpx 16rpx;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.1);\r\n\t\tborder: 1px solid rgba(0, 122, 255, 0.1);\r\n\t}\r\n\r\n\t.materials-link-button {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.materials-link-text {\r\n\t\tmargin-left: 10rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #007aff;\r\n\t\ttext-decoration: underline;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\r\n\t/* 相关链接卡片样式 */\r\n\t.links-card {\r\n\t\tmargin: 20rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tbackground: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 16rpx;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);\r\n\t\tborder: 1px solid rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.links-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 12rpx;\r\n\t}\r\n\r\n\t.links-title {\r\n\t\tmargin-left: 10rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\r\n\t.links-list {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 8rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.link-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 16rpx 12rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.link-item:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n\t.link-item:active {\r\n\t\tbackground-color: #f5f5f5;\r\n\t}\r\n\r\n\t.link-text {\r\n\t\tflex: 1;\r\n\t\tmargin-left: 8rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t/* 联系信息卡片样式 */\r\n\t.contact-card {\r\n\t\tmargin: 20rpx;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tbackground: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 8rpx 12rpx;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);\r\n\t\tborder: 1px solid rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.contact-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 4rpx 0;\r\n\t}\r\n\r\n\t.contact-item:not(:last-child) {\r\n\t\tborder-bottom: 1px solid rgba(0, 0, 0, 0.06);\r\n\t\tmargin-bottom: 2rpx;\r\n\t\tpadding-bottom: 6rpx;\r\n\t}\r\n\r\n\t.contact-icon {\r\n\t\twidth: 28rpx;\r\n\t\theight: 28rpx;\r\n\t\tbackground-color: rgba(102, 102, 102, 0.1);\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t.contact-content-inline {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.contact-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.contact-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.phone-link {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #007aff;\r\n\t\ttext-decoration: underline;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t/* 介绍资料样式 */\r\n\t.action-section {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tgap: 16rpx;\r\n\t}\r\n\r\n\t.materials-btn {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground-color: rgba(41, 121, 255, 0.1);\r\n\t\tborder-radius: 20rpx;\r\n\t\tpadding: 8rpx 12rpx;\r\n\t\tgap: 6rpx;\r\n\t}\r\n\r\n\t.materials-count {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #2979FF;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t/* 弹窗样式 */\r\n\t.materials-modal {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.materials-modal-content {\r\n\t\twidth: 90%;\r\n\t\tmax-height: 70%;\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 16rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.materials-modal-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-bottom: 1px solid #eee;\r\n\t}\r\n\r\n\t.materials-modal-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: 600;\r\n\t\tcolor: #333;\r\n\t}\r\n\r\n\t.materials-modal-close {\r\n\t\tpadding: 10rpx;\r\n\t}\r\n\r\n\t.materials-modal-body {\r\n\t\tmax-height: 60vh;\r\n\t\toverflow-y: auto;\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t.material-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\r\n\t.material-item:last-child {\r\n\t\tmargin-bottom: 0;\r\n\t}\r\n\r\n\t.material-icon {\r\n\t\tmargin-right: 20rpx;\r\n\t}\r\n\r\n\t.material-info {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.material-name {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 8rpx;\r\n\t\tword-break: break-all;\r\n\t}\r\n\r\n\t.material-size {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.material-action {\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t/* 视频预览弹窗样式 */\r\n\t.video-modal {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.9);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 2000;\r\n\t}\r\n\r\n\t.video-modal-content {\r\n\t\twidth: 95%;\r\n\t\tmax-width: 800rpx;\r\n\t\tbackground-color: #000;\r\n\t\tborder-radius: 16rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.video-modal-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 20rpx 30rpx;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.8);\r\n\t}\r\n\r\n\t.video-modal-title {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #fff;\r\n\t\tflex: 1;\r\n\t\tmargin-right: 20rpx;\r\n\t\tword-break: break-all;\r\n\t}\r\n\r\n\t.video-modal-close {\r\n\t\tpadding: 10rpx;\r\n\t}\r\n\r\n\t.video-modal-body {\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.video-player {\r\n\t\twidth: 100%;\r\n\t\theight: 400rpx;\r\n\t\tbackground-color: #000;\r\n\t}\r\n</style>", "import MiniProgramPage from 'D:/小程序项目/熙迈门户/XMMH/pages/category/category.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "response", "PRODUCT_MANAGER_SERVICE_LINKS", "FIELD_MAPPING", "res"], "mappings": ";;;;;;EA0ME,OAAA;AACC,WAAA;AAAA,MACC,eAAA;AAAA;MAEA,OAAA;AAAA;MAEA,aAAA;AAAA;MAEA,iBAAA,CAAA;AAAA;MAEA,oBAAA;AAAA;AAAA,MACA,kBAAA,CAAA;AAAA;AAAA,MACA,qBAAA,CAAA;AAAA;AAAA,MACA,sBAAA;AAAA;AAAA;;MAEA,gBAAA;AAAA;AAAA,MACA,iBAAA;AAAA;AAAA,MACA,kBAAA;AAAA;AAAA,IACD;AAAA;EAGD,OAAA,SAAA;AACCA,kBAAAA,MAAA,MAAA,OAAA,sCAAA,8BAAA;AACAA,kBAAA,MAAA,MAAA,OAAA,sCAAA,cAAA,OAAA;;AAGA,SAAA,KAAA,mBAAA,QAAA,MAAA,EAAA;AACA,SAAA,cAAA,mBAAA,QAAA,eAAA,oBAAA;AAGA,QAAA,KAAA,OAAA;;QAEE,OAAA,KAAA;AAAA,MACD,CAAA;AAAA,IACD;;AAIA,QAAA,eAAA,mBAAA,QAAA,sBAAA,EAAA;AAEA,QAAA,aAAA,WAAA,KAAA,GAAA;AACC,qBAAA,aAAA,UAAA,CAAA;AAAA,IACD;AACA,SAAA,qBAAA;AAEAA,kBAAAA,MAAA,MAAA,OAAA,sCAAA,aAAA;AAAA,MACC,OAAA,KAAA;AAAA;;;;IAKD,CAAA;AAGA,QAAA,QAAA,cAAA,OAAA,QAAA,UAAA;AAECA,oBAAAA,MAAA,MAAA,OAAA,sCAAA,gCAAA;;AAIC,YAAA;AACC,eAAA,mBAAA,KAAA,MAAA,mBAAA,QAAA,mBAAA,IAAA,CAAA;;;AAGAA,wBAAA,MAAA,MAAA,SAAA,sCAAA,wBAAA,KAAA;;QAED;AAAA,MACD;AAGA,UAAA,QAAA,oBAAA;AACC,YAAA;AACC,gBAAA,kBAAA,mBAAA,QAAA,sBAAA,EAAA;AACA,cAAA,iBAAA;;;;;UAQA;AAAA;AAEAA,wBAAA,MAAA,MAAA,SAAA,sCAAA,wBAAA,KAAA;;QAED;AAAA,MACD;AAEA,UAAA,QAAA,UAAA;AAEC,aAAA,mBAAA,QAAA,QAAA;AAAA;;MAID;AAAA;AAEAA,oBAAAA,MAAA,MAAA,OAAA,sCAAA,qBAAA;AAGA,UAAA;AACC,aAAA,WAAA,KAAA,MAAA,mBAAA,QAAA,YAAA,IAAA,CAAA;;;;;AAOAA,sBAAA,MAAA,MAAA,SAAA,sCAAA,uBAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AAAA,MACD;AACA,UAAA;AACC,aAAA,kBAAA,KAAA,MAAA,mBAAA,QAAA,mBAAA,IAAA,CAAA;;;;AAIAA,sBAAAA,MAAA,MAAA,SAAA,sCAAA,eAAA,KAAA;AAAA,MACD;AAGA,UAAA;AACC,aAAA,mBAAA,KAAA,MAAA,mBAAA,QAAA,mBAAA,IAAA,CAAA;;;AAGAA,sBAAA,MAAA,MAAA,SAAA,sCAAA,iBAAA,KAAA;;MAED;AAGA,UAAA;AACC,cAAA,kBAAA,mBAAA,QAAA,sBAAA,EAAA;AACA,YAAA,iBAAA;;;;;QAQA;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,sCAAA,iBAAA,KAAA;;MAED;AAAA,IACD;AACAA,kBAAAA,MAAA,MAAA,OAAA,sCAAA,8BAAA;AAAA;EAED,SAAA;;;;;AAMCA,kBAAAA,MAAA,MAAA,OAAA,sCAAA,yBAAA;AACAA,kBAAA,MAAA,MAAA,OAAA,sCAAA,WAAA,GAAA;AACAA,kBAAAA,MAAA,MAAA,OAAA,sCAAA,WAAA;AAAA,MACC,OAAA,KAAA;AAAA;MAEA,iBAAA,KAAA,WAAA;AAAA;IAED,CAAA;AAEA,UAAA,aAAA,KAAA,QAAA,GAAA,KAAA,KAAA,YAAA;;AAKAA,kBAAA,MAAA,MAAA,OAAA,sCAAA,YAAA,SAAA;AACAA,kBAAA,MAAA,MAAA,OAAA,sCAAA,WAAA,UAAA,MAAA;;MAGC,OAAA;AAAA,MACA,MAAA;AAAA;IAED,CAAA;AAEAA,kBAAA,MAAA,MAAA,OAAA,sCAAA,WAAA,WAAA;AACAA,kBAAAA,MAAA,MAAA,OAAA,sCAAA,0BAAA;AAEA,WAAA;AAAA;EAGD,gBAAA,KAAA;AACCA,kBAAAA,MAAA,MAAA,OAAA,sCAAA,0BAAA;AACAA,kBAAA,MAAA,MAAA,OAAA,sCAAA,WAAA,GAAA;AAEA,UAAA,aAAA,KAAA,QAAA,GAAA,KAAA,KAAA,YAAA;;AAKAA,kBAAA,MAAA,MAAA,OAAA,sCAAA,YAAA,SAAA;;MAGC,OAAA;AAAA,MACA,MAAA;AAAA;IAED,CAAA;AAEAA,kBAAA,MAAA,MAAA,OAAA,sCAAA,WAAA,WAAA;AACAA,kBAAAA,MAAA,MAAA,OAAA,sCAAA,6BAAA;AAEA,WAAA;AAAA;EAGD,SAAA;AAAA;AAAA,IAEC,MAAA,mBAAA,UAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,sCAAA,kBAAA,QAAA;AAEA,UAAA;AAECA,sBAAAA,MAAA,MAAA,OAAA,sCAAA,iBAAA;AACA,cAAA,qBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;;;;YAKC,aAAA;AAAA;;;;YAKA,SAAA;AAAA,cACC;AAAA,gBACC,WAAA;AAAA;;;;;cAKD;AAAA,YACD;AAAA;UAED,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;AAEAA,sBAAA,MAAA,MAAA,OAAA,sCAAA,cAAA,kBAAA;AAEA,YAAA,mBAAA,QAAA,mBAAA,KAAA,QAAA,mBAAA,KAAA,KAAA,QAAA,mBAAA,KAAA,KAAA,KAAA,SAAA,GAAA;;AAGCA,wBAAA,MAAA,MAAA,OAAA,sCAAA,kBAAA,UAAA;AAGA,gBAAA,KAAA,kBAAA,UAAA;AAAA;AAEAA,wBAAAA,MAAA,MAAA,OAAA,sCAAA,gBAAA;AAEA,gBAAA,aAAA;AAAA;;AAGA,gBAAA,KAAA,kBAAA,UAAA;AAAA,QACD;AAAA;AAGAA,sBAAA,MAAA,MAAA,SAAA,sCAAA,mBAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AAAA,MACD;AAAA;;;AAKAA,oBAAAA,MAAA,MAAA,OAAA,sCAAA,iBAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,sCAAA,SAAA,KAAA,KAAA;AAEA,UAAA;AACC,cAAA,cAAA;AAAA;;UAGC,aAAA;AAAA;;;;UAKA,SAAA;AAAA,YACC;AAAA,cACC,WAAA;AAAA;AAAA;;;;cAIA,OAAA,KAAA;AAAA,YACD;AAAA,UACD;AAAA;AAGDA,sBAAA,MAAA,MAAA,OAAA,sCAAA,YAAA,WAAA;AAGA,cAAAC,YAAA,MAAAD,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;UAEA,MAAA;AAAA,UACA,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;AAEAA,sBAAA,MAAA,MAAA,OAAA,sCAAA,UAAAC,SAAA;;;AAKC,gBAAA,aAAAA,UAAA,KAAA,KAAA,KAAA,CAAA;AACAD,wBAAA,MAAA,MAAA,OAAA,sCAAA,WAAA,UAAA;AACA,gBAAA,KAAA,kBAAA,UAAA;AAAA;AAEAA,wBAAAA,MAAA,MAAA,OAAA,sCAAA,cAAA;AAEA,eAAA,aAAA;;AAEAA,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AAAA,QACD;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,sCAAA,eAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AAAA,MACD;AAAA;;IAID,MAAA,kBAAA,YAAA;AACCA,oBAAAA,MAAA,MAAA,OAAA,sCAAA,cAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,sCAAA,cAAA,WAAA,KAAA;AAGA,YAAA,oBAAA,WAAA,0BAAA,KAAA;AACA,UAAA,mBAAA;;;;QAIC,CAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,sCAAA,eAAA,iBAAA;AAAA,MACD;AAGAA,oBAAAA,MAAA,MAAA,OAAA,sCAAA,aAAA;AACA,WAAA,uBAAA,WAAA,0BAAA,KAAA;;AAGA,UAAA,aAAA,WAAA,KAAA,GAAA;AACC,uBAAA,aAAA,UAAA,CAAA;AAAA,MACD;AACA,WAAA,qBAAA;AAGA,UAAA,KAAA,iBAAA,WAAA,GAAA;AACCA,sBAAAA,MAAA,MAAA,OAAA,sCAAA,mBAAA;;AAEC,cAAA;;;;cAIE,MAAA,SAAA,sBAAA;AAAA,cACA,KAAA,SAAA,eAAA,SAAA,2BAAA;AAAA;;cAGA,aAAA,SAAA,eAAA;AAAA;;cAGA,WAAA,SAAA,aAAA;AAAA,YACD,EAAA;;;AAGAA,0BAAA,MAAA,MAAA,SAAA,sCAAA,wBAAA,KAAA;;UAED;AAAA,QACD;AAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,sCAAA,8BAAA;AAAA,MACD;AAGA,UAAA,KAAA,oBAAA,WAAA,GAAA;AACCA,sBAAAA,MAAA,MAAA,OAAA,sCAAA,mBAAA;;;;;QAQA;AAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,sCAAA,8BAAA;AAAA,MACD;AAEAA,oBAAAA,MAAA,MAAA,OAAA,sCAAA,WAAA;AAAA,QACC,gBAAA,KAAA;AAAA,QACA,cAAA,KAAA;AAAA,QACA,gBAAA,KAAA,iBAAA;AAAA,MACD,CAAA;AAEA,UAAA;;;;UAIE,aAAA;AAAA;;;UAIA,WAAA;AAAA,UACA,kBAAA;AAAA;AAGDA,sBAAA,MAAA,MAAA,OAAA,sCAAA,eAAA,sBAAA;AAEA,cAAA,sBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;;UAGA,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;AAEAA,sBAAA,MAAA,MAAA,OAAA,sCAAA,aAAA,mBAAA;;;AAIC,eAAA,aAAA,oBAAA,KAAA,KAAA,KAAA,IAAA,SAAA,KAAA,cAAA,GAAA,CAAA;;AAEAA,wBAAA,MAAA,MAAA,OAAA,sCAAA,eAAA,KAAA,UAAA;AAAA;AAEAA,wBAAAA,MAAA,MAAA,OAAA,sCAAA,aAAA;AAAA,QACD;AAGA,cAAA,KAAA,oBAAA,UAAA;AAEAA,sBAAAA,MAAA,MAAA,OAAA,sCAAA,gBAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,sCAAA,cAAA,KAAA;AAAA,MACD;AAAA;;IAID,MAAA,oBAAA,YAAA;AACC,UAAA;AACCA,sBAAA,MAAA,MAAA,OAAA,sCAAA,wBAAA,yCAAA,KAAA;AAEA,YAAA,CAAA,cAAA,CAAA,WAAA,OAAA;AACCA,wBAAAA,MAAA,MAAA,OAAA,sCAAA,qBAAA;;AAEA;AAAA,QACD;AAEA,cAAA,oBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;;;;YAKC,aAAA;AAAA;;;;;;UAMD,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;AAEA,YAAA,kBAAA,QAAA,kBAAA,KAAA,QAAA,kBAAA,KAAA,KAAA,MAAA;AACC,eAAA,kBAAA,kBAAA,KAAA,KAAA,KAAA,IAAA,SAAA,KAAA,cAAA,GAAA,CAAA;;;;AAIAA,wBAAAA,MAAA,MAAA,OAAA,sCAAA,YAAA;;QAED;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,sCAAA,eAAA,KAAA;;MAED;AAAA;IAGD,oBAAA;;;;;AAKG,eAAA,QAAA,UAAA;;AAEE,mBAAA,KAAA,MAAA,WAAA,IAAA;AAAA;AAEA,mBAAA,KAAA,MAAA,WAAA,KAAA;AAAA,YACD;AAAA,UACD,CAAA;AAAA,QACD;AAAA;;AAGD,kBAAA,KAAA,eAAA;AAAA;IAED,WAAA,MAAA;;AAIC,UAAA,gBAAA;AAEA,UAAA,KAAA,SAAA;AAEC,aAAA;;AAEA,aAAA,KAAA,KAAA,UAAA,KAAA,OAAA,IAAA;AAGA,aAAA,KAAA,MAAA,aAAA,IAAA;AACA,mBAAA,MAAA;AACC,eAAA,KAAA,MAAA,aAAA,KAAA;AAAA,QACD,GAAA,GAAA;AAAA;AAGA,aAAA;;AAEA,aAAA,QAAA,KAAA,UAAA,KAAA,KAAA;AAAA,MACD;AAGAA,oBAAAA,MAAA,eAAA,aAAA,KAAA,QAAA;;QAIC,KAAA;AAAA;QAEA,QAAA;AAAA,UACC,gBAAA;AAAA;;UAGA,QAAA;AAAA;;QAGD,SAAA,CAAA,QAAA;AACCA,wBAAAA,MAAA,MAAA,OAAA,sCAAA,YAAA,aAAA,eAAA,KAAA,KAAA,iCAAA,IAAA,IAAA;AAAA;;;QAID;AAAA,MACD,CAAA;AAAA;IAED,cAAA,IAAA;AAGC,UAAA,cAAA;AACA,YAAA,cAAAE,cAAAA,8BAAA,KAAA,UAAA,KAAA,YAAA,EAAA;AACA,UAAA,aAAA;AACC,sBAAA,YAAA;AAAA,MACD;;QAIC,SAAA;AAAA,UACC,KAAA;AAAA;;;QAID;AAAA;;IAGF,cAAA,KAAA;AACC,YAAA,gBAAA;AAAA,QACC,OAAA,IAAA,SAAA;AAAA;QAEA,WAAA,SAAA,IAAA,KAAA,CAAA,KAAA;AAAA;QAEA,WAAA;AAAA;AAED,aAAA,KAAAC,cAAAA,aAAA,EAAA,QAAA,SAAA;AACC,cAAA,aAAAA,4BAAA,GAAA;AACA,sBAAA,GAAA,IAAA,IAAA,UAAA,KAAA;AAAA,MACD,CAAA;AAGA,oBAAA,mBAAA,SAAA,IAAAA,cAAAA,cAAA,gBAAA,CAAA,KAAA;AAGA,UAAA,cAAA,YAAA,IAAA,UAAA;AACC,sBAAA,WAAA,KAAA,YAAA,cAAA,YAAA,IAAA,QAAA;AAAA,MACD;AAGA,UAAA,IAAA,WAAA;AACC,YAAA;AACC,gBAAA,gBAAA,KAAA,MAAA,IAAA,SAAA;;AAEC,0BAAA,wBAAA,cAAA,IAAA,eAAA;AAAA;;cAGC,MAAA,SAAA,YAAA,SAAA,QAAA;AAAA;AAAA,cAEA,aAAA,SAAA,WAAA,SAAA,eAAA,SAAA,OAAA;AAAA,cACA,yBAAA,SAAA,WAAA,SAAA,eAAA,SAAA,OAAA;AAAA,cACA,oBAAA,SAAA,oBAAA,SAAA,QAAA;AAAA,cACA,WAAA,SAAA,YAAA,SAAA,QAAA;AAAA,YACD,EAAA;AAAA;AAEA,0BAAA,wBAAA;UACD;AAAA,QACD,SAAA,GAAA;AACCH,wBAAA,MAAA,MAAA,SAAA,sCAAA,aAAA,CAAA;AACA,wBAAA,wBAAA;QACD;AAAA;AAEA,sBAAA,wBAAA;MACD;AAKA,aAAA;AAAA;IAED,MAAA,eAAA;AACC,UAAA,CAAA,KAAA,cAAA,QAAA;AACCA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AACA;AAAA,MACD;AAEA,UAAA;AACC,cAAA,cAAA;AAAA;;UAGC,aAAA;AAAA,UACA,QAAA;AAAA;;;;UAKA,SAAA;AAAA,YAAA;AAAA,cACE,WAAA;AAAA;;;;;YAMD;AAAA,cACC,WAAA;AAAA;;;;YAKD;AAAA,UACD;AAAA;AAGD,cAAAC,YAAA,MAAAD,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;UAEA,MAAA;AAAA,UACA,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;;AAGCA,wBAAA,MAAA,MAAA,SAAA,sCAAA,SAAAC,SAAA;AACAD,wBAAAA,MAAA,UAAA;AAAA;;UAGA,CAAA;AACA;AAAA,QACD;AAEA,YAAAC,UAAA,QAAAA,UAAA,KAAA,MAAA;;;cAGG,MAAA,IAAA,0BAAA,KAAA;AAAA,cACA,IAAA,IAAA,0BAAA,KAAA;AAAA,yEAEC;AAAA,cACD,OAAA,IAAA,SAAA;AAAA;cAEA,WAAA,SAAA,IAAA,KAAA,CAAA,KAAA;AAAA;YAED,EAAA;AACAD,0BAAAA,MAAA,MAAA,OAAA,sCAAA,eAAA;AAAA,UACD,WAAAC,UAAA,KAAA,KAAA,UAAA,KAAAA,UAAA,KAAA,eAAA,GAAA;AACCD,0BAAAA,MAAA,UAAA;AAAA,cACC,OAAA;AAAA;;YAGD,CAAA;AACA,iBAAA,aAAA;UACD;AAAA;AAEAA,wBAAA,MAAA,MAAA,SAAA,sCAAA,eAAAC,UAAA,IAAA;AACAD,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AAAA,QACD;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,sCAAA,WAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AAAA,MACD;AAAA;;AAGA,UAAA;AAEC,cAAA,aAAA,MAAA,QAAA,SAAA,IAAA,YAAA,KAAA,MAAA,SAAA;AAEA,YAAA,CAAA,MAAA,QAAA,UAAA,GAAA;AACCA,wBAAAA,MAAA,MAAA,QAAA,sCAAA,UAAA;AACA,iBAAA;AAAA,QACD;AAGA,cAAA,aAAA,WAAA;AAAA,UAAA,UAAA;;AACC,+BAAA,YAAA,mBAAA,WAAA,cACA,UAAA,wBAAA,mBAAA,WAAA;AAAA;AAAA;sEAKA,yCAAA,wBACA;AAAA;AAGDA,sBAAA,MAAA,MAAA,SAAA,sCAAA,SAAA,MAAA,OAAA;AACA,eAAA;AAAA,MACD;AAAA;IAED,cAAA,KAAA;AACC,UAAA;AAEC,YAAA,aAAA,mBAAA,GAAA;;AAIA,YAAA,CAAA,WAAA,WAAA,MAAA,GAAA;AACC,uBAAA,WAAA,UAAA;AAAA,QACD;AACA,eAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,sCAAA,cAAA,KAAA;;MAED;AAAA;IAED,MAAA,oBAAA,MAAA;AACC,UAAA,kBAAA,CAAA;AACA,UAAA,iCAAA,CAAA;AAEA,UAAA;AAEC,cAAA,2BAAA,KAAA,yBAAA,KAAA,sBAAA,SAAA;;AAICA,wBAAAA,MAAA,MAAA,OAAA,sCAAA,qBAAA;AACA,cAAA;AACC,kBAAA,mBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,cACC,KAAA;AAAA;;;;gBAKC,aAAA;AAAA;;;;gBAKA,SAAA,CAAA;AAAA,kBACC,WAAA;AAAA;;;kBAIA,OAAA,KAAA;AAAA;;cAGF,QAAA;AAAA,gBACC,gBAAA;AAAA,cACD;AAAA,YACD,CAAA;AAEA,gBAAA,iBAAA,QAAA,iBAAA,KAAA,QAAA,iBAAA,KAAA,KAAA,QAAA,iBAAA,KAAA,KAAA,KAAA,SAAA,GAAA;;;;AAIEA,8BAAA,MAAA,MAAA,OAAA,sCAAA,WAAA,aAAA;;AAECA,gCAAA,MAAA,MAAA,OAAA,uCAAA,WAAA,QAAA;AACA,yBAAA;AAAA;AAAA,oBAEC,MAAA,SAAA,sBAAA;AAAA,oBACA,KAAA,SAAA,eAAA,SAAA,2BAAA;AAAA;;oBAGA,aAAA,SAAA,eAAA;AAAA;;oBAGA,WAAA,SAAA,aAAA;AAAA;gBAEF,CAAA;;cAED;AAAA,YACD;AAAA;AAEAA,0BAAA,MAAA,MAAA,SAAA,uCAAA,eAAA,KAAA;;UAGD;AAAA,QACD;AAEA,cAAA,sBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;;;;YAKC,aAAA;AAAA;;YAGA,OAAA,KAAA;AAAA,YACA,WAAA;AAAA,YACA,kBAAA;AAAA;UAED,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;AACA,cAAA,oBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,UACC,KAAA;AAAA;;;;YAKC,aAAA;AAAA;;YAGA,OAAA,KAAA;AAAA,YACA,WAAA;AAAA,YACA,kBAAA;AAAA;UAED,QAAA;AAAA,YACC,gBAAA;AAAA,UACD;AAAA,QACD,CAAA;AAEA,YAAA,oBAAA,eAAA,KAAA;AACCA,wBAAA,MAAA,MAAA,SAAA,uCAAA,YAAA,QAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AACA;AAAA,QACD;;;AAKCA,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AACA;AAAA,QACD;AAEA,YAAA,CAAA,oBAAA,KAAA,KAAA,QAAA,oBAAA,KAAA,KAAA,KAAA,WAAA,GAAA;AACC,4BAAA,kBAAA,KAAA,KAAA,KAAA,IAAA,SAAA,KAAA,cAAA,GAAA,CAAA;AAEA,cAAA,cAAA,CAAA;;AAEC,gBAAA;AACC,oBAAA,sBAAA,MAAAA,cAAA,MAAA,QAAA;AAAA,gBACC,KAAA;AAAA;;;;kBAKC,aAAA;AAAA;;kBAGA,OAAA,KAAA;AAAA,kBACA,WAAA;AAAA,kBACA,kBAAA;AAAA;gBAED,QAAA;AAAA,kBACC,gBAAA;AAAA,gBACD;AAAA,cACD,CAAA;;;;kBAIE,SAAA,IAAAG,4BAAA,WAAA,KAAA;AAAA,gBACD,EAAA;AAAA,cACD;AAAA,YACD,SAAA,GAAA;AACCH,4BAAA,MAAA,MAAA,SAAA,uCAAA,aAAA,CAAA;AAAA,YACD;AAAA,UACD;;AAKAA,wBAAAA,MAAA,MAAA,OAAA,uCAAA,6BAAA;AACAA,wBAAAA,MAAA,MAAA,OAAA,uCAAA,YAAA;AAAA;;YAGC,kBAAA,YAAA;AAAA,UACD,CAAA;AACAA,wBAAA,MAAA,MAAA,OAAA,uCAAA,YAAA,QAAA,MAAA;;AAICA,0BAAAA,MAAA,MAAA,OAAA,uCAAA,mBAAA;AACA,kBAAA,YAAA,KAAA;AAGAA,0BAAAA,MAAA,WAAA;AAAA;YAEA,CAAA;AAAA;AAEAA,0BAAAA,MAAA,WAAA;AAAA;YAEA,CAAA;AAAA,UACD;AACA;AAAA,QACD;AAEA,cAAA,gBAAA,oBAAA,KAAA,KAAA,KAAA,IAAA,SAAA,KAAA,cAAA,GAAA,CAAA;AACA,0BAAA,kBAAA,KAAA,KAAA,KAAA,IAAA,SAAA,KAAA,cAAA,GAAA,CAAA;;AAIAA,sBAAA,MAAA,MAAA,OAAA,uCAAA,eAAA,eAAA;AAGA,cAAA,qBAAA,KAAA,gBAAA;AACAA,sBAAA,MAAA,MAAA,OAAA,uCAAA,eAAA,kBAAA;AAEAA,sBAAAA,MAAA,WAAA;AAAA,UACC,KAAA,kCAAA,mBAAA,KAAA,WAAA,CAAA,OAAA,mBAAA,KAAA,cAAA,CAAA,gBAAA,mBAAA,KAAA,YAAA,oBAAA,CAAA,uBAAA,mBAAA,KAAA,gBAAA,EAAA,CAAA,aAAA,mBAAA,KAAA,UAAA,aAAA,CAAA,CAAA,oBAAA,mBAAA,KAAA,UAAA,eAAA,CAAA,CAAA,oBAAA,mBAAA,KAAA,UAAA,eAAA,CAAA,CAAA,uBAAA,mBAAA,mBAAA,KAAA,GAAA,CAAA,CAAA;AAAA,QACD,CAAA;AAAA;AAEAA,sBAAA,MAAA,MAAA,SAAA,uCAAA,YAAA,KAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AAAA,MACD;AAAA;;;;;;IAQD,oBAAA;;;;;AAMCA,oBAAA,MAAA,MAAA,OAAA,uCAAA,SAAA,SAAA,kBAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,uCAAA,SAAA,QAAA;AAGA,YAAA,cAAA,SAAA;;AAEA,YAAA,WAAA,SAAA;AAEAA,oBAAA,MAAA,MAAA,OAAA,uCAAA,mBAAA,WAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,uCAAA,WAAA,WAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,uCAAA,QAAA,QAAA;AAGA,YAAA,WAAA,KAAA,YAAA,QAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,uCAAA,SAAA,QAAA;;;;;;AAWC,aAAA,0BAAA,aAAA,aAAA,QAAA;AAAA,MACD;AAAA;;;AAIAA,oBAAAA,MAAA,cAAA;AAAA,QACC,aAAA;AAAA,MACD,CAAA;AAAA;;;AAIAA,oBAAA,MAAA,MAAA,OAAA,uCAAA,WAAA,QAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,uCAAA,UAAA,UAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,uCAAA,UAAA,WAAA;;;AAKEA,wBAAAA,MAAA,MAAA,OAAA,uCAAA,iBAAA;;;AAIEA,4BAAAA,MAAA,UAAA;AAAA;;gBAGC,aAAA;AAAA,gBACA,YAAA;AAAA;AAEC,sBAAA,SAAA,SAAA;AACCA,kCAAAA,MAAA,iBAAA;AAAA,sBACC,MAAA;AAAA,sBACA,SAAA,WAAA;AACCA,sCAAAA,MAAA,UAAA;AAAA;;wBAGA,CAAA;AAAA,sBACD;AAAA,oBACD,CAAA;AAAA,kBACD;AAAA,gBACD;AAAA,cACD,CAAA;AAAA,YACD;AAAA,UACD,CAAA;AAAA,QACD;AAAA,MACD,CAAA;AAAA;;IAGD,oBAAA,QAAA,UAAA;AACC,UAAA,CAAA,UAAA,OAAA,WAAA,UAAA;AACCA,sBAAA,MAAA,MAAA,SAAA,uCAAA,aAAA,MAAA;AACA,iBAAA,KAAA;AACA;AAAA,MACD;AAEAA,oBAAAA,MAAA,YAAA;AAAA,QACC,OAAA;AAAA,MACD,CAAA;AAEAA,oBAAA,MAAA,MAAA,OAAA,uCAAA,WAAA,MAAA;AACAA,oBAAAA,MAAA,aAAA;AAAA;QAEC,SAAA,SAAA,KAAA;AACCA,wBAAA,MAAA,MAAA,OAAA,uCAAA,WAAA,GAAA;;;AAICA,0BAAAA,MAAA,MAAA,SAAA,uCAAA,eAAA;AACAA,0BAAA,MAAA,YAAA;AACA,qBAAA,KAAA;AACA;AAAA,UACD;AAEAA,wBAAA,MAAA,MAAA,OAAA,uCAAA,WAAA,QAAA;AACAA,wBAAAA,MAAA,aAAA;AAAA,YACC;AAAA,YACA,UAAA;AAAA,YACA,SAAA,SAAAI,MAAA;AACCJ,4BAAA,MAAA,MAAA,OAAA,uCAAA,WAAAI,IAAA;AACAJ,4BAAA,MAAA,YAAA;AACA,uBAAA,IAAA;AAAA;YAED,MAAA,SAAA,KAAA;AACCA,4BAAA,MAAA,MAAA,SAAA,uCAAA,WAAA,GAAA;AACAA,4BAAA,MAAA,YAAA;AACA,uBAAA,KAAA;AAAA,YACD;AAAA,UACD,CAAA;AAAA;QAED,MAAA,SAAA,KAAA;AACCA,wBAAA,MAAA,MAAA,SAAA,uCAAA,WAAA,GAAA;AACAA,wBAAA,MAAA,MAAA,SAAA,uCAAA,WAAA,MAAA;AACAA,wBAAA,MAAA,YAAA;AACA,mBAAA,KAAA;AAAA,QACD;AAAA,MACD,CAAA;AAAA;;IAGD,cAAA,QAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,uCAAA,eAAA,MAAA;AAGA,UAAA,CAAA,UAAA,OAAA,WAAA,UAAA;AACCA,sBAAA,MAAA,MAAA,SAAA,uCAAA,aAAA,MAAA;AACAA,sBAAAA,MAAA,UAAA;AAAA,UACC,OAAA;AAAA;QAED,CAAA;AACA;AAAA,MACD;AAEAA,oBAAAA,MAAA,YAAA;AAAA,QACC,OAAA;AAAA,MACD,CAAA;AAEAA,oBAAA,MAAA,MAAA,OAAA,uCAAA,WAAA,MAAA;AACAA,oBAAAA,MAAA,aAAA;AAAA;QAEC,SAAA,SAAA,KAAA;AACCA,wBAAA,MAAA,MAAA,OAAA,uCAAA,WAAA,GAAA;;;AAICA,0BAAAA,MAAA,MAAA,SAAA,uCAAA,eAAA;AACAA,0BAAA,MAAA,YAAA;AACAA,0BAAAA,MAAA,UAAA;AAAA,cACC,OAAA;AAAA;YAED,CAAA;AACA;AAAA,UACD;AAEAA,wBAAA,MAAA,MAAA,OAAA,uCAAA,WAAA,QAAA;AACAA,wBAAAA,MAAA,aAAA;AAAA,YACC;AAAA;;YAEA,SAAA,SAAAI,MAAA;AACCJ,4BAAA,MAAA,MAAA,OAAA,uCAAA,WAAAI,IAAA;AACAJ,4BAAA,MAAA,YAAA;AAAA;YAED,MAAA,SAAA,KAAA;AACCA,4BAAA,MAAA,MAAA,SAAA,uCAAA,WAAA,GAAA;AACAA,4BAAA,MAAA,YAAA;AACAA,4BAAAA,MAAA,UAAA;AAAA,gBACC,OAAA;AAAA;cAED,CAAA;AAAA,YACD;AAAA,UACD,CAAA;AAAA;QAED,MAAA,SAAA,KAAA;AACCA,wBAAA,MAAA,MAAA,SAAA,uCAAA,WAAA,GAAA;AACAA,wBAAA,MAAA,MAAA,SAAA,uCAAA,WAAA,MAAA;AACAA,wBAAA,MAAA,YAAA;AAGA,cAAA,WAAA;AACA,cAAA,IAAA,UAAA,IAAA,OAAA,SAAA,QAAA,GAAA;AACC,uBAAA;AAAA;;;AAIA,uBAAA;AAAA,UACD;AAEAA,wBAAAA,MAAA,UAAA;AAAA;YAEC,SAAA,WAAA;AAAA,YACA,aAAA;AAAA,YACA,YAAA;AAAA;AAEC,kBAAA,SAAA,SAAA;AAECA,8BAAAA,MAAA,iBAAA;AAAA;kBAEC,SAAA,WAAA;AACCA,kCAAAA,MAAA,UAAA;AAAA,sBACC,OAAA;AAAA;oBAED,CAAA;AAAA,kBACD;AAAA,gBACD,CAAA;AAAA,cACD;AAAA,YACD;AAAA,UACD,CAAA;AAAA,QACD;AAAA,MACD,CAAA;AAAA;;IAGD,YAAA,UAAA;;AAIC,YAAA,aAAA,CAAA,OAAA,OAAA,OAAA,OAAA,OAAA,QAAA,OAAA,KAAA;AACA,UAAA,WAAA,SAAA,GAAA;AAAA,eAAA;;AAIA,UAAA,WAAA,SAAA,GAAA;AAAA,eAAA;;;;AAOA,UAAA,SAAA,SAAA,GAAA;AAAA,eAAA;AAEA,aAAA;AAAA;;;;;QAMC,KAAA;AACC,iBAAA;AAAA,QACD,KAAA;AAAA;AAEC,iBAAA;AAAA,QACD,KAAA;AAAA;;QAGA,KAAA;AAAA;AAEC,iBAAA;AAAA,QACD,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA;QAEA,KAAA;;QAEA,KAAA;AAAA;QAEA,KAAA;AAAA,QACA,KAAA;AAAA,QACA,KAAA;AAAA;;QAGA;AACC,iBAAA;AAAA,MACF;AAAA;;IAGD,aAAA,UAAA,UAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,uCAAA,SAAA,QAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,uCAAA,UAAA,QAAA;;;AAMA,WAAA,mBAAA;AAAA;;IAGD,aAAA,UAAA,UAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,uCAAA,SAAA,QAAA;AACAA,oBAAA,MAAA,MAAA,OAAA,uCAAA,UAAA,QAAA;AAGAA,oBAAAA,MAAA,aAAA;AAAA,QACC,MAAA,CAAA,QAAA;AAAA,QACA,SAAA;AAAA,QACA,SAAA,WAAA;AACCA,wBAAAA,MAAA,MAAA,OAAA,uCAAA,QAAA;AAAA;QAED,MAAA,SAAA,KAAA;AACCA,wBAAA,MAAA,MAAA,SAAA,uCAAA,WAAA,GAAA;AACAA,wBAAAA,MAAA,UAAA;AAAA,YACC,OAAA;AAAA;UAED,CAAA;AAAA,QACD;AAAA,MACD,CAAA;AAAA;;IAGD,kBAAA;;;;;;;AAQC,UAAA,KAAA,SAAA,kBAAA,GAAA;;MAEA;AAEA,UAAA,KAAA,SAAA,wBAAA,KAAA,KAAA,SAAA,qBAAA,GAAA;;MAEA;AAEA,UAAA;AACC,cAAA,MAAA,IAAA,IAAA,IAAA;AACA,eAAA,IAAA;AAAA;;MAGD;AAAA;;IAGD,gBAAA,MAAA;AACCA,oBAAA,MAAA,MAAA,OAAA,uCAAA,WAAA,IAAA;AAGA,YAAA,YAAA,KAAA,mBAAA,IAAA;AAGAA,oBAAAA,MAAA,WAAA;AAAA;;AAGEA,wBAAA,MAAA,MAAA,SAAA,uCAAA,qBAAA,GAAA;AAEAA,wBAAAA,MAAA,UAAA;AAAA;;YAGC,aAAA;AAAA,YACA,YAAA;AAAA,YACA,SAAA,CAAA,QAAA;AACC,kBAAA,IAAA,SAAA;AACCA,8BAAAA,MAAA,iBAAA;AAAA,kBACC,MAAA;AAAA;AAECA,kCAAAA,MAAA,UAAA;AAAA;;oBAGA,CAAA;AAAA;kBAED,MAAA,MAAA;AACCA,kCAAAA,MAAA,UAAA;AAAA;;oBAGA,CAAA;AAAA,kBACD;AAAA,gBACD,CAAA;AAAA,cACD;AAAA,YACD;AAAA,UACD,CAAA;AAAA,QACD;AAAA,MACD,CAAA;AAAA;;IAGD,eAAA,OAAA;;;;AAGC,YAAA,QAAA,CAAA,KAAA,MAAA,MAAA,IAAA;;AAEA,aAAA,YAAA,QAAA,KAAA,IAAA,GAAA,CAAA,GAAA,QAAA,CAAA,CAAA,IAAA,MAAA,MAAA,CAAA;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzgDD,GAAG,WAAW,eAAe;"}