"use strict";
const common_vendor = require("../../common/vendor.js");
const config_fields = require("../../config/fields.js");
const utils_url = require("../../utils/url.js");
const utils_share = require("../../utils/share.js");
const _sfc_main = {
  data() {
    return {
      searchKeyword: "",
      categories: [],
      title: "",
      pm: "",
      parentImage: "",
      children: [],
      recommendations: [],
      likesLog: {},
      showMaterialsModal: false,
      // 控制资料弹窗显示
      currentMaterials: [],
      // 当前查看的资料列表
      currentRelatedLinks: [],
      // 当前查看的相关链接列表
      parentProductManager: "",
      // 父级产品经理
      parentContactPhone: "",
      // 父级联系电话
      showVideoModal: false,
      // 控制视频预览弹窗显示
      currentVideoUrl: "",
      // 当前预览的视频URL
      currentVideoName: ""
      // 当前预览的视频文件名
    };
  },
  onLoad(options) {
    common_vendor.index.__f__("log", "at pages/category/category.vue:225", "=== Category页面 onLoad 开始 ===");
    common_vendor.index.__f__("log", "at pages/category/category.vue:226", "接收到的URL参数:", options);
    this.title = decodeURIComponent(options.title || "");
    this.pm = decodeURIComponent(options.pm || "");
    this.parentImage = decodeURIComponent(options.parentImage || "/static/熙迈LOGO.png");
    if (this.title) {
      common_vendor.index.setNavigationBarTitle({
        title: this.title
      });
    }
    this.parentProductManager = decodeURIComponent(options.pm || "");
    let contactPhone = decodeURIComponent(options.parentContactPhone || "");
    if (contactPhone.startsWith("+86")) {
      contactPhone = contactPhone.substring(3);
    }
    this.parentContactPhone = contactPhone;
    common_vendor.index.__f__("log", "at pages/category/category.vue:248", "解析后的基本信息:", {
      title: this.title,
      pm: this.pm,
      parentImage: this.parentImage,
      shareMode: options.shareMode,
      parentId: options.parentId
    });
    if (options.shareMode === "1" || options.parentId) {
      common_vendor.index.__f__("log", "at pages/category/category.vue:259", "🔄 检测到分享模式或parentId模式，开始重新获取数据");
      if (options.parentMaterials) {
        try {
          this.currentMaterials = JSON.parse(decodeURIComponent(options.parentMaterials || "[]"));
          common_vendor.index.__f__("log", "at pages/category/category.vue:265", "✅ 从URL参数解析父级相关资料，数量:", this.currentMaterials.length);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/category/category.vue:267", "❌ 解析URL参数中的父级相关资料失败:", error);
          this.currentMaterials = [];
        }
      }
      if (options.parentRelatedLinks) {
        try {
          const relatedLinksStr = decodeURIComponent(options.parentRelatedLinks || "");
          if (relatedLinksStr) {
            this.currentRelatedLinks = relatedLinksStr.split(",").map((link) => link.trim()).filter((link) => link.length > 0);
            common_vendor.index.__f__("log", "at pages/category/category.vue:281", "✅ 从URL参数解析父级相关链接，数量:", this.currentRelatedLinks.length);
          } else {
            this.currentRelatedLinks = [];
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/category/category.vue:286", "❌ 解析URL参数中的父级相关链接失败:", error);
          this.currentRelatedLinks = [];
        }
      }
      if (options.parentId) {
        this.loadDataByParentId(options.parentId);
      } else {
        this.loadDataFromAPI();
      }
    } else {
      common_vendor.index.__f__("log", "at pages/category/category.vue:299", "📝 正常模式，解析URL参数中的数据");
      try {
        this.children = JSON.parse(decodeURIComponent(options.children || "[]"));
        common_vendor.index.__f__("log", "at pages/category/category.vue:304", "✅ 成功解析 children 数据，数量:", this.children.length);
        this.categories = this.children;
        this.categories.sort((a, b) => b.likeCount - a.likeCount);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/category/category.vue:310", "❌ 解析 children 数据失败:", error);
        common_vendor.index.showToast({
          title: "数据解析失败",
          icon: "none"
        });
      }
      try {
        this.recommendations = JSON.parse(decodeURIComponent(options.recommendations || "[]"));
        this.recommendations.sort((a, b) => b.likeCount - a.likeCount);
        common_vendor.index.__f__("log", "at pages/category/category.vue:319", "✅ 成功解析推荐数据，数量:", this.recommendations.length);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/category/category.vue:321", "❌ 解析推荐数据失败:", error);
      }
      try {
        this.currentMaterials = JSON.parse(decodeURIComponent(options.parentMaterials || "[]"));
        common_vendor.index.__f__("log", "at pages/category/category.vue:327", "✅ 成功解析父级介绍资料，数量:", this.currentMaterials.length);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/category/category.vue:329", "❌ 解析父级介绍资料失败:", error);
        this.currentMaterials = [];
      }
      try {
        const relatedLinksStr = decodeURIComponent(options.parentRelatedLinks || "");
        if (relatedLinksStr) {
          this.currentRelatedLinks = relatedLinksStr.split(",").map((link) => link.trim()).filter((link) => link.length > 0);
          common_vendor.index.__f__("log", "at pages/category/category.vue:341", "✅ 成功解析父级相关链接，数量:", this.currentRelatedLinks.length);
        } else {
          this.currentRelatedLinks = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/category/category.vue:346", "❌ 解析父级相关链接失败:", error);
        this.currentRelatedLinks = [];
      }
    }
    common_vendor.index.__f__("log", "at pages/category/category.vue:350", "=== Category页面 onLoad 结束 ===");
  },
  onShow() {
    this.updateLikesStatus();
  },
  // 分享功能
  onShareAppMessage(res) {
    common_vendor.index.__f__("log", "at pages/category/category.vue:358", "=== Category页面分享给好友 ===");
    common_vendor.index.__f__("log", "at pages/category/category.vue:359", "分享触发参数:", res);
    common_vendor.index.__f__("log", "at pages/category/category.vue:360", "当前页面数据:", {
      title: this.title,
      parentImage: this.parentImage,
      categoriesCount: this.categories.length,
      recommendationsCount: this.recommendations.length
    });
    const shareTitle = this.title ? `${this.title} - 熙迈科技` : "熙迈科技分类产品 - 专业工业服务";
    const sharePath = `pages/category/category?title=${encodeURIComponent(this.title || "")}&pm=${encodeURIComponent(this.pm || "")}&parentImage=${encodeURIComponent(this.parentImage || "/static/熙迈LOGO.png")}&shareMode=1`;
    common_vendor.index.__f__("log", "at pages/category/category.vue:372", "构建的分享路径:", sharePath);
    common_vendor.index.__f__("log", "at pages/category/category.vue:373", "分享路径长度:", sharePath.length);
    const shareConfig = utils_share.ShareUtils.getDefaultShareConfig({
      title: shareTitle,
      path: sharePath,
      imageUrl: this.parentImage || "/static/熙迈LOGO.png"
    });
    common_vendor.index.__f__("log", "at pages/category/category.vue:381", "最终分享配置:", shareConfig);
    common_vendor.index.__f__("log", "at pages/category/category.vue:382", "=== Category页面分享配置完成 ===");
    return shareConfig;
  },
  onShareTimeline(res) {
    common_vendor.index.__f__("log", "at pages/category/category.vue:388", "=== Category页面分享到朋友圈 ===");
    common_vendor.index.__f__("log", "at pages/category/category.vue:389", "分享触发参数:", res);
    const shareTitle = this.title ? `${this.title} - 熙迈科技` : "熙迈科技分类产品 - 专业工业服务";
    const sharePath = `pages/category/category?title=${encodeURIComponent(this.title || "")}&pm=${encodeURIComponent(this.pm || "")}&parentImage=${encodeURIComponent(this.parentImage || "/static/熙迈LOGO.png")}&shareMode=1`;
    common_vendor.index.__f__("log", "at pages/category/category.vue:396", "构建的分享路径:", sharePath);
    const shareConfig = utils_share.ShareUtils.getDefaultShareConfig({
      title: shareTitle,
      path: sharePath,
      imageUrl: this.parentImage || "/static/熙迈LOGO.png"
    });
    common_vendor.index.__f__("log", "at pages/category/category.vue:404", "最终分享配置:", shareConfig);
    common_vendor.index.__f__("log", "at pages/category/category.vue:405", "=== Category页面朋友圈分享配置完成 ===");
    return shareConfig;
  },
  methods: {
    // 通过父类ID直接加载数据
    async loadDataByParentId(parentId) {
      common_vendor.index.__f__("log", "at pages/category/category.vue:413", "🔄 通过父类ID加载数据:", parentId);
      try {
        common_vendor.index.__f__("log", "at pages/category/category.vue:417", "🔄 开始获取父级分类完整数据");
        const parentDataResponse = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows",
          method: "POST",
          data: {
            appKey: "984e1ff028f80150",
            sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
            worksheetId: "fenlei",
            pageSize: 50,
            pageIndex: 1,
            listType: 0,
            controls: [],
            filters: [
              {
                controlId: "rowid",
                dataType: 2,
                spliceType: 1,
                filterType: 2,
                // 等于（精确匹配）
                value: parentId
              }
            ]
          },
          header: {
            "Content-Type": "application/json"
          }
        });
        common_vendor.index.__f__("log", "at pages/category/category.vue:444", "父级数据API响应:", parentDataResponse);
        if (parentDataResponse.data && parentDataResponse.data.data && parentDataResponse.data.data.rows && parentDataResponse.data.data.rows.length > 0) {
          const parentItem = parentDataResponse.data.data.rows[0];
          common_vendor.index.__f__("log", "at pages/category/category.vue:449", "✅ 获取到父级分类完整数据:", parentItem);
          await this.loadSubCategories(parentItem);
        } else {
          common_vendor.index.__f__("log", "at pages/category/category.vue:454", "❌ 未找到对应的父级分类数据");
          const parentItem = {
            rowid: parentId
          };
          await this.loadSubCategories(parentItem);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/category/category.vue:463", "❌ 通过父类ID加载数据失败:", error);
        common_vendor.index.showToast({
          title: "加载数据失败",
          icon: "none"
        });
      }
    },
    // 从API重新加载数据（用于分享模式）
    async loadDataFromAPI() {
      common_vendor.index.__f__("log", "at pages/category/category.vue:473", "🔄 开始从API重新加载数据");
      common_vendor.index.__f__("log", "at pages/category/category.vue:474", "搜索标题:", this.title);
      try {
        const requestData = {
          appKey: "984e1ff028f80150",
          sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
          worksheetId: "fenlei",
          pageSize: 50,
          pageIndex: 1,
          listType: 0,
          controls: [],
          filters: [
            {
              controlId: "67b2da03ef727a4cd047da1b",
              // 产品名称字段
              dataType: 2,
              spliceType: 1,
              filterType: 2,
              // 等于（精确匹配）
              value: this.title
            }
          ]
        };
        common_vendor.index.__f__("log", "at pages/category/category.vue:496", "API请求数据:", requestData);
        const response2 = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows",
          method: "POST",
          data: requestData,
          header: {
            "Content-Type": "application/json"
          }
        });
        common_vendor.index.__f__("log", "at pages/category/category.vue:508", "API响应:", response2);
        if (response2.data && response2.data.data && response2.data.data.rows && response2.data.data.rows.length > 0) {
          common_vendor.index.__f__("log", "at pages/category/category.vue:511", "✅ 找到匹配的分类数据，数量:", response2.data.data.rows.length);
          const parentItem = response2.data.data.rows[0];
          common_vendor.index.__f__("log", "at pages/category/category.vue:514", "父级分类数据:", parentItem);
          await this.loadSubCategories(parentItem);
        } else {
          common_vendor.index.__f__("log", "at pages/category/category.vue:517", "❌ 未找到匹配的分类数据");
          this.categories = [];
          this.recommendations = [];
          common_vendor.index.showToast({
            title: "未找到相关数据",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/category/category.vue:527", "❌ 重新加载数据失败:", error);
        common_vendor.index.showToast({
          title: "加载数据失败",
          icon: "none"
        });
      }
    },
    // 加载子分类数据
    async loadSubCategories(parentItem) {
      common_vendor.index.__f__("log", "at pages/category/category.vue:537", "🔄 开始加载子分类数据");
      common_vendor.index.__f__("log", "at pages/category/category.vue:538", "父级项目rowid:", parentItem.rowid);
      const parentProductName = parentItem["67b2da03ef727a4cd047da1b"] || "";
      if (parentProductName) {
        this.title = parentProductName;
        common_vendor.index.setNavigationBarTitle({
          title: parentProductName
        });
        common_vendor.index.__f__("log", "at pages/category/category.vue:547", "✅ 设置导航栏标题为:", parentProductName);
      }
      common_vendor.index.__f__("log", "at pages/category/category.vue:551", "🔄 处理父级联系信息");
      this.parentProductManager = parentItem["67b2de31ef727a4cd047da6f"] || "";
      let contactPhone = parentItem["67b400cdef727a4cd047e08c"] || "";
      if (contactPhone.startsWith("+86")) {
        contactPhone = contactPhone.substring(3);
      }
      this.parentContactPhone = contactPhone;
      if (this.currentMaterials.length === 0) {
        common_vendor.index.__f__("log", "at pages/category/category.vue:562", "🔄 从API数据处理父级相关资料");
        if (parentItem.DLPP_JSZL) {
          try {
            const materialsData = JSON.parse(parentItem.DLPP_JSZL);
            this.currentMaterials = materialsData.map((material) => ({
              // 使用正确的字段名
              name: material.original_file_name || "未知文件",
              url: material.DownloadUrl || material.original_file_full_path || "",
              size: material.file_size || 0,
              // 保留完整字段（与 form 页面一致）
              DownloadUrl: material.DownloadUrl || "",
              original_file_full_path: material.original_file_full_path || "",
              original_file_name: material.original_file_name || "未知文件",
              file_size: material.file_size || 0
            }));
            common_vendor.index.__f__("log", "at pages/category/category.vue:577", "✅ 从API数据成功处理父级相关资料，数量:", this.currentMaterials.length);
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/category/category.vue:579", "❌ 解析API数据中的父级相关资料失败:", error);
            this.currentMaterials = [];
          }
        }
      } else {
        common_vendor.index.__f__("log", "at pages/category/category.vue:584", "✅ 已从URL参数获取到父级相关资料，跳过API数据处理");
      }
      if (this.currentRelatedLinks.length === 0) {
        common_vendor.index.__f__("log", "at pages/category/category.vue:589", "🔄 从API数据处理父级相关链接");
        const relatedLinksData = parentItem[config_fields.FIELD_MAPPING.relatedLinks];
        if (relatedLinksData && typeof relatedLinksData === "string") {
          this.currentRelatedLinks = relatedLinksData.split(",").map((link) => link.trim()).filter((link) => link.length > 0);
          common_vendor.index.__f__("log", "at pages/category/category.vue:596", "✅ 从API数据成功处理父级相关链接，数量:", this.currentRelatedLinks.length);
        }
      } else {
        common_vendor.index.__f__("log", "at pages/category/category.vue:599", "✅ 已从URL参数获取到父级相关链接，跳过API数据处理");
      }
      common_vendor.index.__f__("log", "at pages/category/category.vue:602", "父级联系信息:", {
        productManager: this.parentProductManager,
        contactPhone: this.parentContactPhone,
        materialsCount: this.currentMaterials.length
      });
      try {
        const subCategoryRequestData = {
          appKey: "984e1ff028f80150",
          sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
          worksheetId: "fenlei",
          pageSize: 50,
          pageIndex: 1,
          rowId: parentItem.rowid,
          controlId: "67b2dd3aef727a4cd047da37",
          getSystemControl: false
        };
        common_vendor.index.__f__("log", "at pages/category/category.vue:620", "子分类API请求数据:", subCategoryRequestData);
        const subCategoryResponse = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations",
          method: "POST",
          data: subCategoryRequestData,
          header: {
            "Content-Type": "application/json"
          }
        });
        common_vendor.index.__f__("log", "at pages/category/category.vue:631", "子分类API响应:", subCategoryResponse);
        if (subCategoryResponse.data && subCategoryResponse.data.data && subCategoryResponse.data.data.rows) {
          common_vendor.index.__f__("log", "at pages/category/category.vue:634", "✅ 获取到子分类数据，数量:", subCategoryResponse.data.data.rows.length);
          this.categories = subCategoryResponse.data.data.rows.map((row) => this.formatAPIData(row));
          this.categories.sort((a, b) => b.likeCount - a.likeCount);
          common_vendor.index.__f__("log", "at pages/category/category.vue:637", "格式化后的子分类数据:", this.categories);
        } else {
          common_vendor.index.__f__("log", "at pages/category/category.vue:639", "❌ 未获取到子分类数据");
        }
        await this.loadRecommendations(parentItem);
        common_vendor.index.__f__("log", "at pages/category/category.vue:645", "✅ 子分类和推荐数据加载完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/category/category.vue:647", "❌ 加载子分类失败:", error);
      }
    },
    // 加载推荐数据（根据父级项目）
    async loadRecommendations(parentItem) {
      try {
        common_vendor.index.__f__("log", "at pages/category/category.vue:654", "🔄 开始获取推荐数据，父级rowid:", parentItem == null ? void 0 : parentItem.rowid);
        if (!parentItem || !parentItem.rowid) {
          common_vendor.index.__f__("log", "at pages/category/category.vue:657", "❌ 没有父级项目信息，跳过推荐数据加载");
          this.recommendations = [];
          return;
        }
        const recommendResponse = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations",
          method: "POST",
          data: {
            appKey: "984e1ff028f80150",
            sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
            worksheetId: "fenlei",
            rowId: parentItem.rowid,
            controlId: "67b2dd25ef727a4cd047da2b",
            // 推荐产品字段ID
            pageSize: 10,
            pageIndex: 1
          },
          header: {
            "Content-Type": "application/json"
          }
        });
        if (recommendResponse.data && recommendResponse.data.data && recommendResponse.data.data.rows) {
          this.recommendations = recommendResponse.data.data.rows.map((row) => this.formatAPIData(row));
          this.recommendations.sort((a, b) => b.likeCount - a.likeCount);
          common_vendor.index.__f__("log", "at pages/category/category.vue:682", "✅ 获取到推荐数据，数量:", this.recommendations.length);
        } else {
          common_vendor.index.__f__("log", "at pages/category/category.vue:684", "❌ 未获取到推荐数据");
          this.recommendations = [];
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/category/category.vue:688", "❌ 加载推荐数据失败:", error);
        this.recommendations = [];
      }
    },
    updateLikesStatus() {
      const likesLog = common_vendor.index.getStorageSync("likes_log") || {};
      this.likesLog = likesLog;
      const processList = (list) => {
        if (list && list.length > 0) {
          list.forEach((item) => {
            if (likesLog[item.rowId]) {
              this.$set(item, "isLiked", true);
            } else {
              this.$set(item, "isLiked", false);
            }
          });
        }
      };
      processList(this.categories);
      processList(this.recommendations);
    },
    handleLike(item) {
      item.isLiked = !item.isLiked;
      let webhookAction = "";
      if (item.isLiked) {
        item.likeCount++;
        webhookAction = "increment";
        this.$set(this.likesLog, item.rowId, true);
        this.$set(item, "showHeart", true);
        setTimeout(() => {
          this.$set(item, "showHeart", false);
        }, 600);
      } else {
        item.likeCount--;
        webhookAction = "decrement";
        this.$delete(this.likesLog, item.rowId);
      }
      common_vendor.index.setStorageSync("likes_log", this.likesLog);
      common_vendor.index.request({
        url: "https://dmit.duoningbio.com/api/workflow/hooks/Njg2Y2ZhNDk3MDMwNzAyNDViNDMxOTcx",
        method: "POST",
        header: {
          "Content-Type": "application/json"
        },
        data: {
          action: webhookAction,
          id: item.rowId
        },
        success: (res) => {
          common_vendor.index.__f__("log", "at pages/category/category.vue:749", `Webhook '${webhookAction}' for rowid ${item.rowId} sent successfully. Response:`, res.data);
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/category/category.vue:752", `Webhook '${webhookAction}' for rowid ${item.rowId} failed:`, err);
        }
      });
    },
    handleContact(pm) {
      let serviceLink = "https://work.weixin.qq.com/kfid/kfc05b214375686d833";
      const managerLink = config_fields.PRODUCT_MANAGER_SERVICE_LINKS.find((item) => item.manager === pm);
      if (managerLink) {
        serviceLink = managerLink.serviceLink;
      }
      common_vendor.wx$1.openCustomerServiceChat({
        extInfo: {
          url: serviceLink
        },
        corpId: "wwa76e36d25343b6b9",
        success(res) {
        }
      });
    },
    formatAPIData(row) {
      const formattedItem = {
        rowId: row.rowid || "",
        children: [],
        likeCount: parseInt(row["DZS"]) || 0,
        isLiked: false,
        showHeart: false
      };
      Object.keys(config_fields.FIELD_MAPPING).forEach((key) => {
        const apiFieldId = config_fields.FIELD_MAPPING[key];
        formattedItem[key] = row[apiFieldId] || "";
      });
      formattedItem.caseStudiesCount = parseInt(row[config_fields.FIELD_MAPPING.caseStudiesCount]) || 0;
      if (formattedItem.imageUrl || row.imageUrl) {
        formattedItem.imageUrl = this.getImageUrl(formattedItem.imageUrl || row.imageUrl);
      }
      if (row.DLPP_JSZL) {
        try {
          const materialsData = JSON.parse(row.DLPP_JSZL);
          if (Array.isArray(materialsData) && materialsData.length > 0) {
            formattedItem.introductionMaterials = materialsData.map((material) => ({
              name: material.originalFilename || material.name || "未知文件",
              url: material.fileUrl || material.DownloadUrl || material.url || "",
              size: material.filesize || material.size || 0,
              // 保留原始字段以便兼容
              DownloadUrl: material.fileUrl || material.DownloadUrl || material.url || "",
              original_file_full_path: material.fileUrl || material.DownloadUrl || material.url || "",
              original_file_name: material.originalFilename || material.name || "未知文件",
              file_size: material.filesize || material.size || 0
            }));
          } else {
            formattedItem.introductionMaterials = [];
          }
        } catch (e) {
          common_vendor.index.__f__("error", "at pages/category/category.vue:815", "解析介绍资料失败:", e);
          formattedItem.introductionMaterials = [];
        }
      } else {
        formattedItem.introductionMaterials = [];
      }
      return formattedItem;
    },
    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        common_vendor.index.showToast({
          title: "请输入搜索关键词",
          icon: "none"
        });
        return;
      }
      try {
        const requestData = {
          appKey: "984e1ff028f80150",
          sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
          worksheetId: "fenlei",
          viewId: "",
          pageSize: 50,
          pageIndex: 1,
          listType: 0,
          controls: [],
          filters: [
            {
              controlId: "67b2da03ef727a4cd047da1b",
              dataType: 2,
              spliceType: 2,
              filterType: 1,
              value: this.searchKeyword
            },
            {
              controlId: "67b2dd25ef727a4cd047da2a",
              dataType: 2,
              spliceType: 2,
              filterType: 1,
              value: this.searchKeyword
            }
          ]
        };
        const response2 = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows",
          method: "POST",
          data: requestData,
          header: {
            "Content-Type": "application/json"
          }
        });
        if (response2.statusCode !== 200) {
          common_vendor.index.__f__("error", "at pages/category/category.vue:873", "请求失败:", response2);
          common_vendor.index.showToast({
            title: "请求失败",
            icon: "none"
          });
          return;
        }
        if (response2.data && response2.data.data) {
          if (response2.data.data.rows && response2.data.data.rows.length > 0) {
            this.categories = response2.data.data.rows.map((row) => ({
              name: row["67b2da03ef727a4cd047da1b"] || "未命名分类",
              id: row["67b2dd25ef727a4cd047da2a"] || "",
              icon: this.getImageUrl(row["67b2dd25ef727a4cd047da2b"]) || "/static/熙迈LOGO.png",
              rowId: row.rowid || "",
              children: [],
              likeCount: parseInt(row["DZS"]) || 0,
              isLiked: false
            }));
            common_vendor.index.__f__("log", "at pages/category/category.vue:893", agentBrandImage);
          } else if (response2.data.data.total === 0 || response2.data.error_code === 1) {
            common_vendor.index.showToast({
              title: "没有找到相关产品",
              icon: "none",
              duration: 2e3
            });
            this.categories = [];
          }
        } else {
          common_vendor.index.__f__("error", "at pages/category/category.vue:903", "接口返回数据格式异常:", response2.data);
          common_vendor.index.showToast({
            title: "数据格式异常",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/category/category.vue:910", "获取分类失败:", error);
        common_vendor.index.showToast({
          title: "获取分类失败",
          icon: "none"
        });
      }
    },
    getImageUrl(imageData) {
      try {
        const parsedData = Array.isArray(imageData) ? imageData : JSON.parse(imageData);
        if (!Array.isArray(parsedData)) {
          common_vendor.index.__f__("warn", "at pages/category/category.vue:923", "图片数据格式错误");
          return "/static/熙迈LOGO.png";
        }
        const targetItem = parsedData.find(
          (item) => {
            var _a, _b;
            return ((_a = item.fileUrl) == null ? void 0 : _a.startsWith("http")) || ((_b = item.thumbnail_full_path) == null ? void 0 : _b.startsWith("http"));
          }
        );
        return (targetItem == null ? void 0 : targetItem.fileUrl) || (targetItem == null ? void 0 : targetItem.thumbnail_full_path) || "/static/熙迈LOGO.png";
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/category/category.vue:939", "解析失败:", error.message);
        return "/static/熙迈LOGO.png";
      }
    },
    cleanImageUrl(url) {
      try {
        let cleanedUrl = decodeURIComponent(url);
        cleanedUrl = cleanedUrl.trim();
        if (!cleanedUrl.startsWith("http")) {
          cleanedUrl = `https://${cleanedUrl}`;
        }
        return cleanedUrl;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/category/category.vue:955", "清理图片URL失败:", error);
        return null;
      }
    },
    async handleCategoryClick(item) {
      let recommendations = [];
      let parentMaterialsWithDownloadUrl = [];
      try {
        const hasIntroductionMaterials = item.introductionMaterials && item.introductionMaterials.length > 0;
        if (hasIntroductionMaterials) {
          common_vendor.index.__f__("log", "at pages/category/category.vue:969", "检测到相关资料，获取完整附件信息...");
          try {
            const materialResponse = await common_vendor.index.request({
              url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getFilterRows",
              method: "POST",
              data: {
                appKey: "984e1ff028f80150",
                sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
                worksheetId: "fenlei",
                pageSize: 1,
                pageIndex: 1,
                listType: 0,
                controls: [],
                filters: [{
                  controlId: "rowid",
                  dataType: 2,
                  spliceType: 1,
                  filterType: 2,
                  value: item.rowId
                }]
              },
              header: {
                "Content-Type": "application/json"
              }
            });
            if (materialResponse.data && materialResponse.data.data && materialResponse.data.data.rows && materialResponse.data.data.rows.length > 0) {
              const productData = materialResponse.data.data.rows[0];
              if (productData.DLPP_JSZL) {
                const materialsData = JSON.parse(productData.DLPP_JSZL);
                common_vendor.index.__f__("log", "at pages/category/category.vue:999", "原始附件数据:", materialsData);
                parentMaterialsWithDownloadUrl = materialsData.map((material) => {
                  common_vendor.index.__f__("log", "at pages/category/category.vue:1001", "处理单个附件:", material);
                  return {
                    // 使用正确的字段名
                    name: material.original_file_name || "未知文件",
                    url: material.DownloadUrl || material.original_file_full_path || "",
                    size: material.file_size || 0,
                    // 保留完整的字段信息（与 form 页面一致）
                    DownloadUrl: material.DownloadUrl || "",
                    original_file_full_path: material.original_file_full_path || "",
                    original_file_name: material.original_file_name || "未知文件",
                    file_size: material.file_size || 0
                  };
                });
                common_vendor.index.__f__("log", "at pages/category/category.vue:1014", "映射后的附件信息:", parentMaterialsWithDownloadUrl);
              }
            }
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/category/category.vue:1018", "获取完整附件信息失败:", error);
            parentMaterialsWithDownloadUrl = item.introductionMaterials || [];
          }
        }
        const subCategoryResponse = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations",
          method: "POST",
          data: {
            appKey: "984e1ff028f80150",
            sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
            worksheetId: "fenlei",
            pageSize: 50,
            pageIndex: 1,
            rowId: item.rowId,
            controlId: "67b2dd3aef727a4cd047da37",
            getSystemControl: false
          },
          header: {
            "Content-Type": "application/json"
          }
        });
        const recommendResponse = await common_vendor.index.request({
          url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations",
          method: "POST",
          data: {
            appKey: "984e1ff028f80150",
            sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
            worksheetId: "fenlei",
            pageSize: 50,
            pageIndex: 1,
            rowId: item.rowId,
            controlId: "GLCP",
            getSystemControl: false
          },
          header: {
            "Content-Type": "application/json"
          }
        });
        if (subCategoryResponse.statusCode !== 200) {
          common_vendor.index.__f__("error", "at pages/category/category.vue:1060", "请求子分类失败:", response);
          common_vendor.index.showToast({
            title: "请求子分类失败",
            icon: "none"
          });
          return;
        }
        if (!subCategoryResponse.data || !subCategoryResponse.data.data) {
          common_vendor.index.__f__("error", "at pages/category/category.vue:1070", "接口返回数据格式异常:", subCategoryResponse.data);
          common_vendor.index.showToast({
            title: "数据格式异常",
            icon: "none"
          });
          return;
        }
        if (!subCategoryResponse.data.data.rows || subCategoryResponse.data.data.rows.length === 0) {
          recommendations = recommendResponse.data.data.rows.map((row) => this.formatAPIData(row));
          let caseStudies = [];
          if (item.caseStudiesCount > 0) {
            try {
              const caseStudiesResponse = await common_vendor.index.request({
                url: "https://dmit.duoningbio.com/api/v2/open/worksheet/getRowRelations",
                method: "POST",
                data: {
                  appKey: "984e1ff028f80150",
                  sign: "NzMwNjI3ZDgxZDEyMDc5YWJkZjVlMzA3ZDU5YTA1MDY5MDVhNTUwNWFmMDZjNTUwNjU5ZDk1YjkxMDQ4ZTU5Mw==",
                  worksheetId: "fenlei",
                  pageSize: 50,
                  pageIndex: 1,
                  rowId: item.rowId,
                  controlId: "ALQK",
                  getSystemControl: false
                },
                header: {
                  "Content-Type": "application/json"
                }
              });
              if (caseStudiesResponse.data && caseStudiesResponse.data.data && caseStudiesResponse.data.data.rows) {
                caseStudies = caseStudiesResponse.data.data.rows.map((row) => ({
                  clientName: row[config_fields.FIELD_MAPPING.caseClientName] || "",
                  details: row[config_fields.FIELD_MAPPING.caseDetails] || ""
                }));
              }
            } catch (e) {
              common_vendor.index.__f__("error", "at pages/category/category.vue:1108", "获取案例情况失败:", e);
            }
          }
          const formUrl = `/pages/form/form?${utils_url.objectToParams(item)}&caseStudies=${encodeURIComponent(JSON.stringify(caseStudies))}`;
          common_vendor.index.__f__("log", "at pages/category/category.vue:1115", "=== Category页面跳转到Form页面 ===");
          common_vendor.index.__f__("log", "at pages/category/category.vue:1116", "跳转的产品信息:", {
            productName: item.productName,
            productCode: item.productCode,
            caseStudiesCount: caseStudies.length
          });
          common_vendor.index.__f__("log", "at pages/category/category.vue:1121", "跳转URL长度:", formUrl.length);
          if (formUrl.length > 1e3) {
            common_vendor.index.__f__("log", "at pages/category/category.vue:1125", "⚠️ URL仍然过长，使用简化模式");
            const simpleUrl = item.productCode ? `/pages/form/form?productCode=${encodeURIComponent(item.productCode)}&rowid=${encodeURIComponent(item.rowId)}&shareMode=1` : `/pages/form/form?productName=${encodeURIComponent(item.productName)}&rowid=${encodeURIComponent(item.rowId)}&shareMode=1`;
            common_vendor.index.navigateTo({
              url: simpleUrl
            });
          } else {
            common_vendor.index.navigateTo({
              url: formUrl
            });
          }
          return;
        }
        const formattedData = subCategoryResponse.data.data.rows.map((row) => this.formatAPIData(row));
        recommendations = recommendResponse.data.data.rows.map((row) => this.formatAPIData(row));
        const parentMaterials = parentMaterialsWithDownloadUrl.length > 0 ? parentMaterialsWithDownloadUrl : item.introductionMaterials || [];
        common_vendor.index.__f__("log", "at pages/category/category.vue:1145", "传递父级分类介绍资料:", parentMaterials);
        const parentRelatedLinks = item.relatedLinks || [];
        common_vendor.index.__f__("log", "at pages/category/category.vue:1149", "传递父级分类相关链接:", parentRelatedLinks);
        common_vendor.index.navigateTo({
          url: `/pages/category/category?title=${encodeURIComponent(item.productName)}&pm=${encodeURIComponent(item.productManager)}&parentImage=${encodeURIComponent(item.imageUrl || "/static/熙迈LOGO.png")}&parentContactPhone=${encodeURIComponent(item.contactPhone || "")}&children=${encodeURIComponent(JSON.stringify(formattedData))}&recommendations=${encodeURIComponent(JSON.stringify(recommendations))}&parentMaterials=${encodeURIComponent(JSON.stringify(parentMaterials))}&parentRelatedLinks=${encodeURIComponent(parentRelatedLinks.join(","))}`
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/category/category.vue:1155", "获取子分类失败:", error);
        common_vendor.index.showToast({
          title: "获取子分类失败",
          icon: "none"
        });
      }
    },
    // 显示父级分类资料列表
    showParentMaterialsList() {
      this.showMaterialsModal = true;
    },
    // 隐藏资料列表
    hideMaterialsList() {
      this.showMaterialsModal = false;
    },
    // 预览资料文件
    previewMaterial(material) {
      common_vendor.index.__f__("log", "at pages/category/category.vue:1174", "预览文件:", material.original_file_name);
      common_vendor.index.__f__("log", "at pages/category/category.vue:1175", "文件信息:", material);
      const downloadUrl = material.DownloadUrl;
      const originalUrl = material.original_file_full_path;
      const fileName = material.original_file_name;
      common_vendor.index.__f__("log", "at pages/category/category.vue:1182", "使用 DownloadUrl:", downloadUrl);
      common_vendor.index.__f__("log", "at pages/category/category.vue:1183", "备用 URL:", originalUrl);
      common_vendor.index.__f__("log", "at pages/category/category.vue:1184", "文件名:", fileName);
      const fileType = this.getFileType(fileName);
      common_vendor.index.__f__("log", "at pages/category/category.vue:1188", "文件类型:", fileType);
      if (fileType === "video") {
        this.previewVideo(downloadUrl || originalUrl, fileName);
      } else if (fileType === "image") {
        this.previewImage(downloadUrl || originalUrl, fileName);
      } else {
        this.previewWechatWithFallback(downloadUrl, originalUrl, fileName);
      }
    },
    // 拨打电话
    callnum(num) {
      common_vendor.index.makePhoneCall({
        phoneNumber: num
      });
    },
    // 带回退机制的预览方法
    previewWechatWithFallback(primaryUrl, fallbackUrl, fileName) {
      common_vendor.index.__f__("log", "at pages/category/category.vue:1210", "尝试预览文档:", fileName);
      common_vendor.index.__f__("log", "at pages/category/category.vue:1211", "主要URL:", primaryUrl);
      common_vendor.index.__f__("log", "at pages/category/category.vue:1212", "备用URL:", fallbackUrl);
      this.previewWechatSingle(primaryUrl, (success) => {
        if (!success && fallbackUrl && fallbackUrl !== primaryUrl) {
          common_vendor.index.__f__("log", "at pages/category/category.vue:1217", "主要URL失败，尝试备用URL");
          this.previewWechatSingle(fallbackUrl, (success2) => {
            if (!success2) {
              common_vendor.index.showModal({
                title: "预览失败",
                content: "文件无法预览，可能是文件格式不支持或网络问题。\n\n是否复制文件链接？",
                confirmText: "复制链接",
                cancelText: "取消",
                success: function(modalRes) {
                  if (modalRes.confirm) {
                    common_vendor.index.setClipboardData({
                      data: primaryUrl,
                      success: function() {
                        common_vendor.index.showToast({
                          title: "链接已复制",
                          icon: "success"
                        });
                      }
                    });
                  }
                }
              });
            }
          });
        }
      });
    },
    // 单次预览尝试
    previewWechatSingle(urlPdf, callback) {
      if (!urlPdf || typeof urlPdf !== "string") {
        common_vendor.index.__f__("error", "at pages/category/category.vue:1248", "无效的文档URL:", urlPdf);
        callback(false);
        return;
      }
      common_vendor.index.showLoading({
        title: "正在加载中.."
      });
      common_vendor.index.__f__("log", "at pages/category/category.vue:1257", "开始下载文件:", urlPdf);
      common_vendor.index.downloadFile({
        url: urlPdf,
        success: function(res) {
          common_vendor.index.__f__("log", "at pages/category/category.vue:1261", "文件下载成功:", res);
          var filePath = res.tempFilePath;
          if (!filePath) {
            common_vendor.index.__f__("error", "at pages/category/category.vue:1265", "下载成功但临时文件路径为空");
            common_vendor.index.hideLoading();
            callback(false);
            return;
          }
          common_vendor.index.__f__("log", "at pages/category/category.vue:1271", "准备打开文档:", filePath);
          common_vendor.index.openDocument({
            filePath,
            showMenu: false,
            success: function(res2) {
              common_vendor.index.__f__("log", "at pages/category/category.vue:1276", "打开文档成功:", res2);
              common_vendor.index.hideLoading();
              callback(true);
            },
            fail: function(err) {
              common_vendor.index.__f__("error", "at pages/category/category.vue:1281", "打开文档失败:", err);
              common_vendor.index.hideLoading();
              callback(false);
            }
          });
        },
        fail: function(err) {
          common_vendor.index.__f__("error", "at pages/category/category.vue:1288", "下载文件失败:", err);
          common_vendor.index.__f__("error", "at pages/category/category.vue:1289", "失败的URL:", urlPdf);
          common_vendor.index.hideLoading();
          callback(false);
        }
      });
    },
    // 微信小程序预览文档
    previewWechat(urlPdf) {
      common_vendor.index.__f__("log", "at pages/category/category.vue:1297", "准备预览文档，URL:", urlPdf);
      if (!urlPdf || typeof urlPdf !== "string") {
        common_vendor.index.__f__("error", "at pages/category/category.vue:1301", "无效的文档URL:", urlPdf);
        common_vendor.index.showToast({
          title: "文档链接无效",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showLoading({
        title: "正在加载中.."
      });
      common_vendor.index.__f__("log", "at pages/category/category.vue:1313", "开始下载文件:", urlPdf);
      common_vendor.index.downloadFile({
        url: urlPdf,
        success: function(res) {
          common_vendor.index.__f__("log", "at pages/category/category.vue:1317", "文件下载成功:", res);
          var filePath = res.tempFilePath;
          if (!filePath) {
            common_vendor.index.__f__("error", "at pages/category/category.vue:1321", "下载成功但临时文件路径为空");
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "文件路径错误",
              icon: "none"
            });
            return;
          }
          common_vendor.index.__f__("log", "at pages/category/category.vue:1330", "准备打开文档:", filePath);
          common_vendor.index.openDocument({
            filePath,
            showMenu: false,
            // 禁用菜单，防止下载
            success: function(res2) {
              common_vendor.index.__f__("log", "at pages/category/category.vue:1335", "打开文档成功:", res2);
              common_vendor.index.hideLoading();
            },
            fail: function(err) {
              common_vendor.index.__f__("error", "at pages/category/category.vue:1339", "打开文档失败:", err);
              common_vendor.index.hideLoading();
              common_vendor.index.showToast({
                title: "文档格式不支持或文件损坏",
                icon: "none"
              });
            }
          });
        },
        fail: function(err) {
          common_vendor.index.__f__("error", "at pages/category/category.vue:1349", "下载文件失败:", err);
          common_vendor.index.__f__("error", "at pages/category/category.vue:1350", "失败的URL:", urlPdf);
          common_vendor.index.hideLoading();
          let errorMsg = "文件加载失败";
          if (err.errMsg && err.errMsg.includes("ENOENT")) {
            errorMsg = "文件不存在或已被删除";
          } else if (err.errMsg && err.errMsg.includes("network")) {
            errorMsg = "网络连接失败，请检查网络";
          } else if (err.errMsg && err.errMsg.includes("timeout")) {
            errorMsg = "下载超时，请重试";
          }
          common_vendor.index.showModal({
            title: "预览失败",
            content: errorMsg + "\n\n是否尝试在浏览器中打开？",
            confirmText: "打开",
            cancelText: "取消",
            success: function(modalRes) {
              if (modalRes.confirm) {
                common_vendor.index.setClipboardData({
                  data: urlPdf,
                  success: function() {
                    common_vendor.index.showToast({
                      title: "链接已复制到剪贴板",
                      icon: "success"
                    });
                  }
                });
              }
            }
          });
        }
      });
    },
    // 获取文件类型
    getFileType(fileName) {
      const ext = fileName.split(".").pop().toLowerCase();
      const videoTypes = ["mp4", "avi", "mov", "wmv", "flv", "webm", "mkv", "ogg"];
      if (videoTypes.includes(ext))
        return "video";
      const imageTypes = ["jpg", "jpeg", "png", "gif", "bmp", "webp"];
      if (imageTypes.includes(ext))
        return "image";
      if (ext === "pdf")
        return "pdf";
      const docTypes = ["doc", "docx", "xls", "xlsx", "ppt", "pptx"];
      if (docTypes.includes(ext))
        return "document";
      return "unknown";
    },
    // 获取文件图标
    getMaterialIcon(fileName) {
      const ext = fileName.split(".").pop().toLowerCase();
      switch (ext) {
        case "pdf":
          return "paperplane";
        case "doc":
        case "docx":
          return "compose";
        case "xls":
        case "xlsx":
          return "bars";
        case "ppt":
        case "pptx":
          return "videocam";
        case "mp4":
        case "avi":
        case "mov":
        case "wmv":
        case "flv":
        case "webm":
        case "mkv":
          return "videocam-filled";
        case "jpg":
        case "jpeg":
        case "png":
        case "gif":
        case "bmp":
        case "webp":
          return "image";
        default:
          return "folder";
      }
    },
    // 预览视频文件
    previewVideo(videoUrl, fileName) {
      common_vendor.index.__f__("log", "at pages/category/category.vue:1443", "预览视频:", fileName);
      common_vendor.index.__f__("log", "at pages/category/category.vue:1444", "视频URL:", videoUrl);
      this.showVideoModal = true;
      this.currentVideoUrl = videoUrl;
      this.currentVideoName = fileName;
    },
    // 预览图片文件
    previewImage(imageUrl, fileName) {
      common_vendor.index.__f__("log", "at pages/category/category.vue:1454", "预览图片:", fileName);
      common_vendor.index.__f__("log", "at pages/category/category.vue:1455", "图片URL:", imageUrl);
      common_vendor.index.previewImage({
        urls: [imageUrl],
        current: imageUrl,
        success: function() {
          common_vendor.index.__f__("log", "at pages/category/category.vue:1462", "图片预览成功");
        },
        fail: function(err) {
          common_vendor.index.__f__("error", "at pages/category/category.vue:1465", "图片预览失败:", err);
          common_vendor.index.showToast({
            title: "图片预览失败",
            icon: "none"
          });
        }
      });
    },
    // 关闭视频预览
    closeVideoModal() {
      this.showVideoModal = false;
      this.currentVideoUrl = "";
      this.currentVideoName = "";
    },
    // 获取链接显示文本
    getLinkDisplayText(link) {
      if (link.includes("mp.weixin.qq.com")) {
        return "公众号文章";
      }
      if (link.includes("channels.weixin.qq.com") || link.includes("video.weixin.qq.com")) {
        return "视频号";
      }
      try {
        const url = new URL(link);
        return url.hostname;
      } catch (error) {
        return "相关链接";
      }
    },
    // 打开相关链接
    openRelatedLink(link) {
      common_vendor.index.__f__("log", "at pages/category/category.vue:1499", "打开相关链接:", link);
      common_vendor.index.showModal({
        title: "打开链接",
        content: "即将复制链接到剪贴板，请在浏览器中打开",
        confirmText: "复制链接",
        cancelText: "取消",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.setClipboardData({
              data: link,
              success: () => {
                common_vendor.index.showToast({
                  title: "链接已复制",
                  icon: "success"
                });
              },
              fail: () => {
                common_vendor.index.showToast({
                  title: "复制失败",
                  icon: "none"
                });
              }
            });
          }
        }
      });
    },
    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0)
        return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
    }
  }
};
if (!Array) {
  const _component_uni_icons = common_vendor.resolveComponent("uni-icons");
  _component_uni_icons();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.currentMaterials.length > 0
  }, $data.currentMaterials.length > 0 ? {
    b: common_vendor.p({
      type: "paperplane",
      size: "18",
      color: "#007aff"
    }),
    c: common_vendor.t($data.currentMaterials.length),
    d: common_vendor.o((...args) => $options.showParentMaterialsList && $options.showParentMaterialsList(...args))
  } : {}, {
    e: $data.currentRelatedLinks.length > 0
  }, $data.currentRelatedLinks.length > 0 ? {
    f: common_vendor.p({
      type: "link",
      size: "18",
      color: "#007aff"
    }),
    g: common_vendor.t($data.currentRelatedLinks.length),
    h: common_vendor.f($data.currentRelatedLinks, (link, index, i0) => {
      return {
        a: "268fc464-2-" + i0,
        b: common_vendor.t($options.getLinkDisplayText(link)),
        c: "268fc464-3-" + i0,
        d: index,
        e: common_vendor.o(($event) => $options.openRelatedLink(link), index)
      };
    }),
    i: common_vendor.p({
      type: "paperplane-filled",
      size: "14",
      color: "#007aff"
    }),
    j: common_vendor.p({
      type: "right",
      size: "10",
      color: "#999"
    })
  } : {}, {
    k: $data.parentProductManager || $data.parentContactPhone
  }, $data.parentProductManager || $data.parentContactPhone ? common_vendor.e({
    l: $data.parentProductManager
  }, $data.parentProductManager ? {
    m: common_vendor.p({
      type: "person",
      size: "14",
      color: "#666"
    }),
    n: common_vendor.t($data.parentProductManager)
  } : {}, {
    o: $data.parentContactPhone
  }, $data.parentContactPhone ? {
    p: common_vendor.p({
      type: "phone",
      size: "14",
      color: "#666"
    }),
    q: common_vendor.t($data.parentContactPhone),
    r: common_vendor.o(($event) => $options.callnum($data.parentContactPhone))
  } : {}) : {}, {
    s: common_vendor.f($data.categories, (item, index, i0) => {
      return common_vendor.e({
        a: item.imageUrl || "/static/熙迈LOGO.png",
        b: item.englishName
      }, item.englishName ? {
        c: common_vendor.t(item.englishName)
      } : {}, {
        d: item.YWLX === "代理"
      }, item.YWLX === "代理" ? {} : {}, {
        e: common_vendor.t(item.productName),
        f: item.isLiked ? "/static/红色小红心.svg" : "/static/灰色小红心.svg",
        g: item.showHeart
      }, item.showHeart ? {
        h: common_vendor.t(item.likeCount)
      } : {}, {
        i: common_vendor.o(($event) => $options.handleLike(item), index),
        j: index,
        k: common_vendor.o(($event) => $options.handleCategoryClick(item), index)
      });
    }),
    t: $data.recommendations.length > 0
  }, $data.recommendations.length > 0 ? {
    v: common_vendor.f($data.recommendations, (item, index, i0) => {
      return common_vendor.e({
        a: item.imageUrl || "/static/熙迈LOGO.png",
        b: common_vendor.t(item.englishName),
        c: item.YWLX === "代理"
      }, item.YWLX === "代理" ? {} : {}, {
        d: common_vendor.t(item.productName),
        e: item.isLiked ? "/static/红色小红心.svg" : "/static/灰色小红心.svg",
        f: item.showHeart
      }, item.showHeart ? {
        g: common_vendor.t(item.likeCount)
      } : {}, {
        h: common_vendor.o(($event) => $options.handleLike(item), index),
        i: index,
        j: common_vendor.o(($event) => $options.handleCategoryClick(item), index)
      });
    })
  } : {}, {
    w: common_vendor.o(($event) => $options.handleContact(this.pm)),
    x: $data.categories.length === 0
  }, $data.categories.length === 0 ? {} : {}, {
    y: $data.showMaterialsModal
  }, $data.showMaterialsModal ? {
    z: common_vendor.p({
      type: "close",
      size: "20",
      color: "#666"
    }),
    A: common_vendor.o((...args) => $options.hideMaterialsList && $options.hideMaterialsList(...args)),
    B: common_vendor.f($data.currentMaterials, (material, index, i0) => {
      return {
        a: "268fc464-7-" + i0,
        b: common_vendor.p({
          type: $options.getMaterialIcon(material.original_file_name),
          size: "24",
          color: "#2979FF"
        }),
        c: common_vendor.t(material.original_file_name),
        d: common_vendor.t($options.formatFileSize(material.file_size)),
        e: "268fc464-8-" + i0,
        f: index,
        g: common_vendor.o(($event) => $options.previewMaterial(material), index)
      };
    }),
    C: common_vendor.p({
      type: "eye",
      size: "16",
      color: "#999"
    }),
    D: common_vendor.o(() => {
    }),
    E: common_vendor.o((...args) => $options.hideMaterialsList && $options.hideMaterialsList(...args))
  } : {}, {
    F: $data.showVideoModal
  }, $data.showVideoModal ? {
    G: common_vendor.t($data.currentVideoName),
    H: common_vendor.p({
      type: "close",
      size: "20",
      color: "#fff"
    }),
    I: common_vendor.o((...args) => $options.closeVideoModal && $options.closeVideoModal(...args)),
    J: $data.currentVideoUrl,
    K: common_vendor.o(() => {
    }),
    L: common_vendor.o((...args) => $options.closeVideoModal && $options.closeVideoModal(...args))
  } : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render]]);
_sfc_main.__runtimeHooks = 6;
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/category/category.js.map
